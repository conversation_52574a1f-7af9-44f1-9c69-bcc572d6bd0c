#!/usr/bin/env python3
"""
充值卡系统测试脚本
测试完整的充值卡生成、使用和时间管理功能
"""

import os
import sys
import time
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_manager import DatabaseManager
from server.recharge_card_manager import RechargeCardManager
from server.user_manager import UserManager


def test_recharge_system():
    """测试充值卡系统"""
    print("=== 充值卡系统测试 ===\n")
    
    try:
        # 初始化数据库和管理器
        db_path = "data/test_recharge.db"
        if os.path.exists(db_path):
            os.remove(db_path)
        
        print("1. 初始化数据库...")
        db_manager = DatabaseManager(db_path)
        user_manager = UserManager(db_manager)
        card_manager = RechargeCardManager(db_manager)
        print("✓ 数据库初始化完成\n")
        
        # 创建测试用户
        print("2. 创建测试用户...")
        result = user_manager.create_user("testuser", "password123")
        if result['success']:
            print("✓ 测试用户创建成功")
        else:
            print(f"× 用户创建失败: {result['message']}")
            return
        print()
        
        # 生成测试卡密
        print("3. 生成测试卡密...")
        
        # 生成不同类型的卡密
        card_types = ['hour_1', 'day_1', 'week_1']
        generated_cards = {}
        
        for card_type in card_types:
            result = card_manager.generate_cards(card_type, 2, "test_generator")
            if result['success']:
                generated_cards[card_type] = result['cards']
                card_name = card_manager.card_types[card_type]['name']
                print(f"✓ 生成 {card_name}: {len(result['cards'])} 张")
                for card in result['cards']:
                    print(f"  - {card['card_code']}")
            else:
                print(f"× 生成 {card_type} 失败: {result['message']}")
        print()
        
        # 测试卡密查询
        print("4. 测试卡密查询...")
        if 'hour_1' in generated_cards and generated_cards['hour_1']:
            test_card = generated_cards['hour_1'][0]['card_code']
            result = card_manager.get_card_info(test_card)
            if result['success']:
                card_info = result['card_info']
                print(f"✓ 卡密查询成功:")
                print(f"  卡密: {card_info['card_code']}")
                print(f"  类型: {card_info['card_name']}")
                print(f"  状态: {card_info['status']}")
            else:
                print(f"× 卡密查询失败: {result['message']}")
        print()
        
        # 测试用户初始时间
        print("5. 检查用户初始剩余时间...")
        result = card_manager.get_user_remaining_time("testuser")
        if result['success']:
            print(f"✓ 用户初始状态: {result['status']}")
            print(f"  剩余时间: {result['remaining_seconds']} 秒")
        else:
            print(f"× 获取剩余时间失败: {result['message']}")
        print()
        
        # 测试使用卡密
        print("6. 测试使用卡密...")
        
        # 使用1小时卡
        if 'hour_1' in generated_cards and generated_cards['hour_1']:
            card_code = generated_cards['hour_1'][0]['card_code']
            print(f"使用1小时卡: {card_code}")
            
            result = card_manager.use_card(card_code, "testuser")
            if result['success']:
                print(f"✓ 卡密使用成功: {result['message']}")
                print(f"  增加时间: {result['added_time']}")
                print(f"  到期时间: {result['expires_at']}")
            else:
                print(f"× 卡密使用失败: {result['message']}")
        
        # 检查使用后的剩余时间
        print("\n7. 检查使用卡密后的剩余时间...")
        result = card_manager.get_user_remaining_time("testuser")
        if result['success']:
            print(f"✓ 用户当前状态: {result['status']}")
            if result['has_time']:
                hours = result['remaining_hours']
                print(f"  剩余时间: {hours:.2f} 小时")
                print(f"  到期时间: {result['expires_at']}")
            else:
                print("  无剩余时间")
        print()
        
        # 使用天卡（累加时间）
        print("8. 测试时间累加（使用天卡）...")
        if 'day_1' in generated_cards and generated_cards['day_1']:
            card_code = generated_cards['day_1'][0]['card_code']
            print(f"使用天卡: {card_code}")
            
            result = card_manager.use_card(card_code, "testuser")
            if result['success']:
                print(f"✓ 天卡使用成功: {result['message']}")
                
                # 检查累加后的时间
                result = card_manager.get_user_remaining_time("testuser")
                if result['success'] and result['has_time']:
                    hours = result['remaining_hours']
                    print(f"  累加后剩余时间: {hours:.2f} 小时")
            else:
                print(f"× 天卡使用失败: {result['message']}")
        print()
        
        # 测试重复使用同一卡密
        print("9. 测试重复使用卡密...")
        if 'hour_1' in generated_cards and generated_cards['hour_1']:
            card_code = generated_cards['hour_1'][0]['card_code']
            result = card_manager.use_card(card_code, "testuser")
            if not result['success']:
                print(f"✓ 正确阻止重复使用: {result['message']}")
            else:
                print(f"× 应该阻止重复使用，但没有阻止")
        print()
        
        # 测试无效卡密
        print("10. 测试无效卡密...")
        result = card_manager.use_card("INVALID-CARD-CODE", "testuser")
        if not result['success']:
            print(f"✓ 正确识别无效卡密: {result['message']}")
        else:
            print(f"× 应该识别为无效卡密，但没有")
        print()
        
        # 测试用户时间历史
        print("11. 测试用户时间历史...")
        history = card_manager.get_user_time_history("testuser")
        print(f"✓ 获取到 {len(history)} 条时间记录:")
        for i, record in enumerate(history, 1):
            print(f"  {i}. {record['description']} - {record['duration_hours']}小时")
        print()
        
        # 测试卡密统计
        print("12. 测试卡密统计...")
        stats = card_manager.get_card_statistics()
        if stats:
            print("✓ 卡密统计信息:")
            print(f"  总卡密数: {stats.get('total_cards', 0)}")
            print(f"  已使用: {stats.get('used_cards', 0)}")
            print(f"  未使用: {stats.get('unused_cards', 0)}")
            
            print("  按类型统计:")
            for card_type, type_stats in stats.get('by_type', {}).items():
                print(f"    {type_stats['name']}: 总数{type_stats['total']}, 已用{type_stats['used']}")
        print()
        
        print("=== 充值卡系统测试完成 ===")
        print("✓ 所有功能测试通过")
        
    except Exception as e:
        print(f"× 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据库
        try:
            if os.path.exists(db_path):
                # 确保数据库连接已关闭
                if 'db_manager' in locals():
                    del db_manager
                if 'card_manager' in locals():
                    del card_manager
                if 'user_manager' in locals():
                    del user_manager
                
                time.sleep(0.1)  # 等待一下确保连接关闭
                os.remove(db_path)
                print("\n✓ 测试数据库已清理")
        except Exception as e:
            print(f"\n⚠ 清理测试数据库失败: {e}")
            print("请手动删除文件: data/test_recharge.db")


def test_time_format():
    """测试时间格式化功能"""
    print("\n=== 时间格式化测试 ===")
    
    test_cases = [
        (3661, "1小时1分钟1秒"),  # 1小时1分1秒
        (7200, "2小时0分钟0秒"),  # 2小时
        (86400, "1天0小时0分钟"),  # 1天
        (90061, "1天1小时1分钟"),  # 1天1小时1分1秒
        (3600 * 24 * 7, "7天0小时0分钟"),  # 1周
    ]
    
    for seconds, expected_format in test_cases:
        # 模拟时间格式化逻辑
        if seconds > 86400:  # 超过1天
            days = seconds // 86400
            hours = (seconds % 86400) // 3600
            minutes = (seconds % 3600) // 60
            formatted = f"{days}天{hours}小时{minutes}分钟"
        elif seconds > 3600:  # 超过1小时
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            secs = seconds % 60
            formatted = f"{hours}小时{minutes}分钟{secs}秒"
        else:  # 小于1小时
            minutes = seconds // 60
            secs = seconds % 60
            formatted = f"{minutes}分钟{secs}秒"
        
        print(f"  {seconds}秒 -> {formatted}")


if __name__ == "__main__":
    test_recharge_system()
    test_time_format()
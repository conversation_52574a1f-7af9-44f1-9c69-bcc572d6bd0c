#!/usr/bin/env python3
"""
调试用户注册问题
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_manager import DatabaseManager
from server.database_migrations import DatabaseMigration


def debug_register():
    """调试用户注册问题"""
    db_path = "data/debug_register.db"
    
    # 删除测试数据库
    if os.path.exists(db_path):
        os.remove(db_path)
    
    # 运行迁移
    migration = DatabaseMigration(db_path)
    success = migration.run_migrations()
    
    if not success:
        print("迁移失败")
        return
    
    # 创建数据库管理器
    db_manager = DatabaseManager(db_path)
    
    # 测试用户注册
    print("测试用户注册...")
    
    # 检查用户是否存在
    user_info = db_manager.get_user_info("testuser")
    print(f"注册前用户信息: {user_info}")
    
    # 注册用户
    success = db_manager.register_user(
        username="testuser",
        password_hash="hash123",
        email="<EMAIL>",
        initial_balance=10.0
    )
    
    print(f"注册结果: {success}")
    
    # 再次检查用户
    user_info = db_manager.get_user_info("testuser")
    print(f"注册后用户信息: {user_info}")
    
    # 尝试重复注册
    success2 = db_manager.register_user(
        username="testuser",
        password_hash="hash456",
        email="<EMAIL>"
    )
    
    print(f"重复注册结果: {success2}")


if __name__ == "__main__":
    debug_register()
"""
增强配置管理模块
专门处理多配置文件的检查、创建和自动修复功能
"""

import os
import shutil
import configparser
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
from pathlib import Path

from common.config_manager import ConfigManager


class ConfigStatus:
    """配置文件状态类"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.exists = os.path.exists(file_path)
        self.is_valid = False
        self.errors = []
        self.warnings = []
        self.last_modified = None
        self.size = 0
        self.backup_available = False
        
        if self.exists:
            try:
                stat = os.stat(file_path)
                self.last_modified = datetime.fromtimestamp(stat.st_mtime)
                self.size = stat.st_size
            except Exception as e:
                self.errors.append(f"无法获取文件信息: {str(e)}")


class RepairResult:
    """配置修复结果类"""
    
    def __init__(self):
        self.success = False
        self.actions_taken = []
        self.errors = []
        self.created_files = []
        self.backed_up_files = []


class ConfigAutoFixer:
    """配置文件自动修复器"""
    
    def __init__(self):
        self.backup_dir = "backup/config"
        self._ensure_backup_dir()
        self.repair_log = []
    
    def _ensure_backup_dir(self):
        """确保备份目录存在"""
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def _log_repair_action(self, action: str):
        """记录修复操作"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {action}"
        self.repair_log.append(log_entry)
        print(log_entry)
    
    def fix_missing_configs(self, required_configs: List[str]) -> RepairResult:
        """修复缺失的配置文件"""
        result = RepairResult()
        self._log_repair_action("开始修复缺失的配置文件")
        
        for config_file in required_configs:
            if not os.path.exists(config_file):
                self._log_repair_action(f"检测到缺失的配置文件: {config_file}")
                try:
                    if self.create_from_example(config_file):
                        result.created_files.append(config_file)
                        action = f"从示例文件创建 {config_file}"
                        result.actions_taken.append(action)
                        self._log_repair_action(f"成功创建配置文件: {config_file}")
                    else:
                        error = f"无法创建配置文件 {config_file}"
                        result.errors.append(error)
                        self._log_repair_action(f"错误: {error}")
                except Exception as e:
                    error = f"创建 {config_file} 时出错: {str(e)}"
                    result.errors.append(error)
                    self._log_repair_action(f"异常: {error}")
            else:
                self._log_repair_action(f"配置文件已存在: {config_file}")
        
        result.success = len(result.errors) == 0
        if result.success:
            self._log_repair_action("配置文件修复完成，所有文件正常")
        else:
            self._log_repair_action(f"配置文件修复完成，但有 {len(result.errors)} 个错误")
        
        return result
    
    def create_from_example(self, config_file: str) -> bool:
        """从示例文件创建配置文件"""
        example_file = f"{config_file}.example"
        
        if not os.path.exists(example_file):
            return False
        
        try:
            shutil.copy2(example_file, config_file)
            self._adjust_config_for_environment(config_file)
            return True
        except Exception as e:
            print(f"从示例文件创建配置失败: {str(e)}")
            return False
    
    def _adjust_config_for_environment(self, config_file: str):
        """根据当前环境调整配置文件"""
        try:
            config = configparser.ConfigParser()
            config.read(config_file, encoding='utf-8')
            
            # 确保日志目录存在
            if config.has_section('logging') and config.has_option('logging', 'file_path'):
                log_path = config.get('logging', 'file_path')
                log_dir = os.path.dirname(log_path)
                if log_dir:
                    os.makedirs(log_dir, exist_ok=True)
            
            # 确保数据目录存在
            if config.has_section('database') and config.has_option('database', 'path'):
                db_path = config.get('database', 'path')
                db_dir = os.path.dirname(db_path)
                if db_dir:
                    os.makedirs(db_dir, exist_ok=True)
            
            # 保存调整后的配置
            with open(config_file, 'w', encoding='utf-8') as f:
                config.write(f)
        except Exception as e:
            print(f"调整配置文件环境设置失败: {str(e)}")
    
    def repair_corrupted_config(self, config_path: str) -> bool:
        """修复损坏的配置文件"""
        try:
            if os.path.exists(config_path):
                backup_path = self._create_backup(config_path)
                if backup_path:
                    print(f"已备份损坏的配置文件到: {backup_path}")
            
            return self.create_from_example(config_path)
        except Exception as e:
            print(f"修复配置文件失败: {str(e)}")
            return False
    
    def update_config_permissions(self, config_path: str) -> bool:
        """更新配置文件权限"""
        try:
            if not os.path.exists(config_path):
                return False
            
            if os.name == 'nt':
                import stat
                os.chmod(config_path, stat.S_IREAD | stat.S_IWRITE)
            else:
                os.chmod(config_path, 0o644)
            
            self._log_repair_action(f"已更新配置文件权限: {config_path}")
            return True
        except Exception as e:
            self._log_repair_action(f"更新配置文件权限失败: {str(e)}")
            return False
    
    def get_repair_log(self) -> List[str]:
        """获取修复操作日志"""
        return self.repair_log.copy()
    
    def clear_repair_log(self):
        """清空修复日志"""
        self.repair_log.clear()
    
    def _create_backup(self, config_path: str) -> Optional[str]:
        """创建配置文件备份"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = os.path.basename(config_path)
            backup_path = os.path.join(self.backup_dir, f"{filename}.backup_{timestamp}")
            
            shutil.copy2(config_path, backup_path)
            self._log_repair_action(f"已创建配置文件备份: {backup_path}")
            return backup_path
        except Exception as e:
            self._log_repair_action(f"创建备份失败: {str(e)}")
            return None


class ConfigValidator:
    """配置文件验证器"""
    
    def validate_config_file(self, config_path: str) -> Tuple[bool, List[str]]:
        """验证配置文件"""
        errors = []
        
        if not os.path.exists(config_path):
            errors.append("配置文件不存在")
            return False, errors
        
        try:
            if not self.validate_config_syntax(config_path):
                errors.append("配置文件语法错误")
            
            missing_items = self.validate_config_completeness(config_path)
            if missing_items:
                errors.extend([f"缺少配置项: {item}" for item in missing_items])
            
            return len(errors) == 0, errors
        except Exception as e:
            errors.append(f"验证过程中出错: {str(e)}")
            return False, errors
    
    def validate_config_syntax(self, config_path: str) -> bool:
        """验证配置文件语法"""
        try:
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')
            return True
        except Exception:
            return False
    
    def validate_config_completeness(self, config_path: str) -> List[str]:
        """验证配置完整性"""
        missing_items = []
        
        try:
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')
            
            if 'client_config.ini' in config_path:
                missing_items.extend(self._check_client_config_items(config))
            elif 'config.ini' in config_path:
                missing_items.extend(self._check_server_config_items(config))
        except Exception as e:
            missing_items.append(f"检查配置完整性时出错: {str(e)}")
        
        return missing_items
    
    def _check_client_config_items(self, config: configparser.ConfigParser) -> List[str]:
        """检查客户端配置必需项"""
        missing = []
        
        required_sections = {
            'client': ['default_host', 'default_port', 'connection_timeout'],
            'logging': ['level', 'file_path'],
            'ui': ['window_width', 'window_height']
        }
        
        for section, options in required_sections.items():
            if not config.has_section(section):
                missing.append(f"[{section}] 节")
                continue
            
            for option in options:
                if not config.has_option(section, option):
                    missing.append(f"[{section}].{option}")
        
        return missing
    
    def _check_server_config_items(self, config: configparser.ConfigParser) -> List[str]:
        """检查服务器配置必需项"""
        missing = []
        
        required_sections = {
            'server': ['host', 'port', 'max_connections', 'session_timeout'],
            'database': ['path'],
            'logging': ['level', 'file_path'],
            'security': ['max_failed_attempts', 'lockout_duration', 'password_min_length']
        }
        
        for section, options in required_sections.items():
            if not config.has_section(section):
                missing.append(f"[{section}] 节")
                continue
            
            for option in options:
                if not config.has_option(section, option):
                    missing.append(f"[{section}].{option}")
        
        return missing


class EnhancedConfigManager:
    """增强配置管理器"""
    
    def __init__(self):
        self.required_configs = ['config.ini', 'client_config.ini']
        self.validator = ConfigValidator()
        self.auto_fixer = ConfigAutoFixer()
        self._config_managers = {}
    
    def check_all_configs(self) -> Dict[str, bool]:
        """检查所有必需的配置文件"""
        status = {}
        for config_file in self.required_configs:
            status[config_file] = os.path.exists(config_file)
        return status
    
    def create_missing_configs(self) -> List[str]:
        """创建缺失的配置文件"""
        repair_result = self.auto_fixer.fix_missing_configs(self.required_configs)
        return repair_result.created_files
    
    def validate_configs(self) -> Dict[str, List[str]]:
        """验证所有配置文件"""
        validation_results = {}
        
        for config_file in self.required_configs:
            if os.path.exists(config_file):
                is_valid, errors = self.validator.validate_config_file(config_file)
                if not is_valid:
                    validation_results[config_file] = errors
            else:
                validation_results[config_file] = ["配置文件不存在"]
        
        return validation_results
    
    def get_config_status(self) -> Dict[str, ConfigStatus]:
        """获取所有配置文件的详细状态"""
        status = {}
        
        for config_file in self.required_configs:
            config_status = ConfigStatus(config_file)
            
            if config_status.exists:
                is_valid, errors = self.validator.validate_config_file(config_file)
                config_status.is_valid = is_valid
                config_status.errors = errors
            
            status[config_file] = config_status
        
        return status
    
    def auto_fix_all(self) -> RepairResult:
        """自动修复所有配置问题"""
        result = RepairResult()
        
        # 修复缺失的配置文件
        missing_fix_result = self.auto_fixer.fix_missing_configs(self.required_configs)
        result.created_files.extend(missing_fix_result.created_files)
        result.actions_taken.extend(missing_fix_result.actions_taken)
        result.errors.extend(missing_fix_result.errors)
        
        # 验证修复后的配置
        validation_results = self.validate_configs()
        for config_file, errors in validation_results.items():
            if errors and os.path.exists(config_file):
                if self.auto_fixer.repair_corrupted_config(config_file):
                    result.actions_taken.append(f"修复损坏的配置文件 {config_file}")
                else:
                    result.errors.append(f"无法修复配置文件 {config_file}")
        
        result.success = len(result.errors) == 0
        return result
    
    def get_config_manager(self, config_file: str) -> ConfigManager:
        """获取指定配置文件的配置管理器"""
        if config_file not in self._config_managers:
            self._config_managers[config_file] = ConfigManager(config_file)
        
        return self._config_managers[config_file]
    
    def get_summary_status(self) -> Dict[str, Any]:
        """获取配置状态摘要"""
        config_status = self.get_config_status()
        
        total_configs = len(self.required_configs)
        existing_configs = sum(1 for status in config_status.values() if status.exists)
        valid_configs = sum(1 for status in config_status.values() if status.is_valid)
        
        missing_configs = [
            config_file for config_file, status in config_status.items() 
            if not status.exists
        ]
        
        invalid_configs = [
            config_file for config_file, status in config_status.items() 
            if status.exists and not status.is_valid
        ]
        
        return {
            'total_configs': total_configs,
            'existing_configs': existing_configs,
            'valid_configs': valid_configs,
            'missing_configs': missing_configs,
            'invalid_configs': invalid_configs,
            'all_configs_ok': existing_configs == total_configs and valid_configs == total_configs
        }
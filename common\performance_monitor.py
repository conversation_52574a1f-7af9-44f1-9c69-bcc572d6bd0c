"""
性能监控模块
实现性能测试、瓶颈分析和系统优化功能
"""

import time
import threading
import psutil
import gc
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from collections import deque, defaultdict
import statistics


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_samples: int = 1000):
        """
        初始化性能监控器
        
        Args:
            max_samples: 最大样本数量
        """
        self.max_samples = max_samples
        self.is_monitoring = False
        self.monitor_thread = None
        self._lock = threading.Lock()
        
        # 性能数据
        self.cpu_samples = deque(maxlen=max_samples)
        self.memory_samples = deque(maxlen=max_samples)
        self.network_samples = deque(maxlen=max_samples)
        self.response_times = deque(maxlen=max_samples)
        self.error_counts = defaultdict(int)
        
        # 统计信息
        self.stats = {
            'start_time': None,
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0,
            'max_response_time': 0,
            'min_response_time': float('inf'),
            'requests_per_second': 0
        }
        
        # 性能阈值
        self.thresholds = {
            'cpu_warning': 70.0,
            'cpu_critical': 90.0,
            'memory_warning': 70.0,
            'memory_critical': 90.0,
            'response_time_warning': 1.0,
            'response_time_critical': 5.0
        }
        
        # 回调函数
        self.alert_callbacks = []
    
    def start_monitoring(self, interval: float = 1.0):
        """
        开始性能监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.stats['start_time'] = datetime.now()
        
        def monitor_loop():
            """监控循环"""
            while self.is_monitoring:
                try:
                    self._collect_system_metrics()
                    self._check_thresholds()
                    time.sleep(interval)
                except Exception as e:
                    print(f"性能监控错误: {e}")
                    time.sleep(interval)
        
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 网络统计
            network = psutil.net_io_counters()
            
            timestamp = time.time()
            
            with self._lock:
                self.cpu_samples.append((timestamp, cpu_percent))
                self.memory_samples.append((timestamp, memory_percent))
                self.network_samples.append((timestamp, {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                }))
                
        except Exception as e:
            print(f"收集系统指标失败: {e}")
    
    def _check_thresholds(self):
        """检查性能阈值"""
        try:
            if not self.cpu_samples or not self.memory_samples:
                return
            
            current_cpu = self.cpu_samples[-1][1]
            current_memory = self.memory_samples[-1][1]
            
            # CPU阈值检查
            if current_cpu >= self.thresholds['cpu_critical']:
                self._trigger_alert('cpu', 'critical', current_cpu)
            elif current_cpu >= self.thresholds['cpu_warning']:
                self._trigger_alert('cpu', 'warning', current_cpu)
            
            # 内存阈值检查
            if current_memory >= self.thresholds['memory_critical']:
                self._trigger_alert('memory', 'critical', current_memory)
            elif current_memory >= self.thresholds['memory_warning']:
                self._trigger_alert('memory', 'warning', current_memory)
            
            # 响应时间阈值检查
            if self.response_times:
                avg_response_time = statistics.mean(list(self.response_times)[-10:])
                
                if avg_response_time >= self.thresholds['response_time_critical']:
                    self._trigger_alert('response_time', 'critical', avg_response_time)
                elif avg_response_time >= self.thresholds['response_time_warning']:
                    self._trigger_alert('response_time', 'warning', avg_response_time)
                    
        except Exception as e:
            print(f"检查性能阈值失败: {e}")
    
    def _trigger_alert(self, metric: str, level: str, value: float):
        """
        触发性能警报
        
        Args:
            metric: 指标名称
            level: 警报级别
            value: 当前值
        """
        alert_data = {
            'metric': metric,
            'level': level,
            'value': value,
            'timestamp': datetime.now(),
            'threshold': self.thresholds.get(f'{metric}_{level}', 0)
        }
        
        for callback in self.alert_callbacks:
            try:
                callback(alert_data)
            except Exception as e:
                print(f"性能警报回调失败: {e}")
    
    def record_request(self, response_time: float, success: bool = True, error_type: str = None):
        """
        记录请求性能
        
        Args:
            response_time: 响应时间
            success: 是否成功
            error_type: 错误类型
        """
        with self._lock:
            self.stats['total_requests'] += 1
            
            if success:
                self.stats['successful_requests'] += 1
            else:
                self.stats['failed_requests'] += 1
                if error_type:
                    self.error_counts[error_type] += 1
            
            # 记录响应时间
            self.response_times.append(response_time)
            
            # 更新响应时间统计
            if response_time > self.stats['max_response_time']:
                self.stats['max_response_time'] = response_time
            
            if response_time < self.stats['min_response_time']:
                self.stats['min_response_time'] = response_time
            
            # 计算平均响应时间
            if self.response_times:
                self.stats['avg_response_time'] = statistics.mean(self.response_times)
            
            # 计算每秒请求数
            if self.stats['start_time']:
                elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
                if elapsed > 0:
                    self.stats['requests_per_second'] = self.stats['total_requests'] / elapsed
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        获取性能报告
        
        Returns:
            Dict[str, Any]: 性能报告
        """
        with self._lock:
            report = {
                'timestamp': datetime.now().isoformat(),
                'monitoring_duration': self._get_monitoring_duration(),
                'system_metrics': self._get_system_metrics_summary(),
                'request_metrics': self.stats.copy(),
                'error_summary': dict(self.error_counts),
                'performance_analysis': self._analyze_performance()
            }
            
            return report
    
    def _get_monitoring_duration(self) -> float:
        """获取监控持续时间"""
        if self.stats['start_time']:
            return (datetime.now() - self.stats['start_time']).total_seconds()
        return 0
    
    def _get_system_metrics_summary(self) -> Dict[str, Any]:
        """获取系统指标摘要"""
        summary = {
            'cpu': {'current': 0, 'avg': 0, 'max': 0, 'min': 100},
            'memory': {'current': 0, 'avg': 0, 'max': 0, 'min': 100},
            'network': {'total_sent': 0, 'total_recv': 0}
        }
        
        try:
            # CPU统计
            if self.cpu_samples:
                cpu_values = [sample[1] for sample in self.cpu_samples]
                summary['cpu'] = {
                    'current': cpu_values[-1],
                    'avg': statistics.mean(cpu_values),
                    'max': max(cpu_values),
                    'min': min(cpu_values)
                }
            
            # 内存统计
            if self.memory_samples:
                memory_values = [sample[1] for sample in self.memory_samples]
                summary['memory'] = {
                    'current': memory_values[-1],
                    'avg': statistics.mean(memory_values),
                    'max': max(memory_values),
                    'min': min(memory_values)
                }
            
            # 网络统计
            if self.network_samples:
                first_sample = self.network_samples[0][1]
                last_sample = self.network_samples[-1][1]
                summary['network'] = {
                    'total_sent': last_sample['bytes_sent'] - first_sample['bytes_sent'],
                    'total_recv': last_sample['bytes_recv'] - first_sample['bytes_recv']
                }
                
        except Exception as e:
            print(f"生成系统指标摘要失败: {e}")
        
        return summary
    
    def _analyze_performance(self) -> Dict[str, Any]:
        """分析性能"""
        analysis = {
            'bottlenecks': [],
            'recommendations': [],
            'health_score': 100
        }
        
        try:
            # 分析CPU性能
            if self.cpu_samples:
                cpu_values = [sample[1] for sample in self.cpu_samples]
                avg_cpu = statistics.mean(cpu_values)
                
                if avg_cpu > 80:
                    analysis['bottlenecks'].append('CPU使用率过高')
                    analysis['recommendations'].append('考虑优化CPU密集型操作或增加服务器资源')
                    analysis['health_score'] -= 20
                elif avg_cpu > 60:
                    analysis['health_score'] -= 10
            
            # 分析内存性能
            if self.memory_samples:
                memory_values = [sample[1] for sample in self.memory_samples]
                avg_memory = statistics.mean(memory_values)
                
                if avg_memory > 80:
                    analysis['bottlenecks'].append('内存使用率过高')
                    analysis['recommendations'].append('检查内存泄漏或增加内存容量')
                    analysis['health_score'] -= 20
                elif avg_memory > 60:
                    analysis['health_score'] -= 10
            
            # 分析响应时间
            if self.response_times:
                avg_response = statistics.mean(self.response_times)
                
                if avg_response > 2.0:
                    analysis['bottlenecks'].append('响应时间过长')
                    analysis['recommendations'].append('优化数据库查询和网络通信')
                    analysis['health_score'] -= 15
                elif avg_response > 1.0:
                    analysis['health_score'] -= 5
            
            # 分析错误率
            if self.stats['total_requests'] > 0:
                error_rate = (self.stats['failed_requests'] / self.stats['total_requests']) * 100
                
                if error_rate > 5:
                    analysis['bottlenecks'].append('错误率过高')
                    analysis['recommendations'].append('检查错误日志并修复常见问题')
                    analysis['health_score'] -= 25
                elif error_rate > 1:
                    analysis['health_score'] -= 10
            
            # 确保健康分数不为负数
            analysis['health_score'] = max(0, analysis['health_score'])
            
        except Exception as e:
            print(f"性能分析失败: {e}")
        
        return analysis
    
    def add_alert_callback(self, callback: Callable):
        """
        添加警报回调函数
        
        Args:
            callback: 回调函数
        """
        if callback not in self.alert_callbacks:
            self.alert_callbacks.append(callback)
    
    def remove_alert_callback(self, callback: Callable):
        """
        移除警报回调函数
        
        Args:
            callback: 回调函数
        """
        if callback in self.alert_callbacks:
            self.alert_callbacks.remove(callback)
    
    def set_threshold(self, metric: str, level: str, value: float):
        """
        设置性能阈值
        
        Args:
            metric: 指标名称
            level: 级别
            value: 阈值
        """
        threshold_key = f"{metric}_{level}"
        if threshold_key in self.thresholds:
            self.thresholds[threshold_key] = value
    
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """
        获取实时指标
        
        Returns:
            Dict[str, Any]: 实时指标
        """
        try:
            current_time = time.time()
            
            # 获取最近的样本
            recent_cpu = 0
            recent_memory = 0
            recent_response_time = 0
            
            if self.cpu_samples:
                recent_cpu = self.cpu_samples[-1][1]
            
            if self.memory_samples:
                recent_memory = self.memory_samples[-1][1]
            
            if self.response_times:
                recent_response_time = statistics.mean(list(self.response_times)[-5:])
            
            return {
                'timestamp': current_time,
                'cpu_percent': recent_cpu,
                'memory_percent': recent_memory,
                'avg_response_time': recent_response_time,
                'active_connections': self._get_active_connections(),
                'requests_per_second': self.stats['requests_per_second']
            }
            
        except Exception as e:
            print(f"获取实时指标失败: {e}")
            return {}
    
    def _get_active_connections(self) -> int:
        """获取活跃连接数"""
        try:
            connections = psutil.net_connections()
            return len([conn for conn in connections if conn.status == 'ESTABLISHED'])
        except:
            return 0
    
    def optimize_memory(self):
        """内存优化"""
        try:
            # 强制垃圾回收
            collected = gc.collect()
            
            # 获取内存使用情况
            memory_before = psutil.virtual_memory().percent
            
            # 清理性能数据（保留最近的样本）
            with self._lock:
                if len(self.cpu_samples) > self.max_samples // 2:
                    self.cpu_samples = deque(list(self.cpu_samples)[-self.max_samples//2:], 
                                           maxlen=self.max_samples)
                
                if len(self.memory_samples) > self.max_samples // 2:
                    self.memory_samples = deque(list(self.memory_samples)[-self.max_samples//2:], 
                                              maxlen=self.max_samples)
                
                if len(self.response_times) > self.max_samples // 2:
                    self.response_times = deque(list(self.response_times)[-self.max_samples//2:], 
                                              maxlen=self.max_samples)
            
            memory_after = psutil.virtual_memory().percent
            
            return {
                'success': True,
                'objects_collected': collected,
                'memory_before': memory_before,
                'memory_after': memory_after,
                'memory_freed': memory_before - memory_after
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def export_performance_data(self, filename: str) -> bool:
        """
        导出性能数据
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 导出成功返回True
        """
        try:
            import json
            
            data = {
                'export_time': datetime.now().isoformat(),
                'monitoring_duration': self._get_monitoring_duration(),
                'cpu_samples': list(self.cpu_samples),
                'memory_samples': list(self.memory_samples),
                'response_times': list(self.response_times),
                'stats': self.stats,
                'error_counts': dict(self.error_counts),
                'thresholds': self.thresholds
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"导出性能数据失败: {e}")
            return False


class RequestTimer:
    """请求计时器"""
    
    def __init__(self, performance_monitor: PerformanceMonitor):
        """
        初始化请求计时器
        
        Args:
            performance_monitor: 性能监控器
        """
        self.performance_monitor = performance_monitor
        self.start_time = None
    
    def __enter__(self):
        """进入上下文"""
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if self.start_time:
            response_time = time.time() - self.start_time
            success = exc_type is None
            error_type = exc_type.__name__ if exc_type else None
            
            self.performance_monitor.record_request(response_time, success, error_type)

# 全局性
能监控实例
performance_monitor = PerformanceMonitor()

def profile(func):
    """性能分析装饰器"""
    import functools
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        with RequestTimer(performance_monitor):
            return func(*args, **kwargs)
    return wrapper


class DatabaseOptimizer:
    """数据库优化器"""
    
    def __init__(self, db_manager):
        """
        初始化数据库优化器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.query_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'max_time': 0.0
        })
    
    def analyze_query_performance(self) -> Dict[str, Any]:
        """
        分析查询性能
        
        Returns:
            Dict[str, Any]: 查询性能分析
        """
        try:
            with self.db_manager.get_connection() as conn:
                # 获取数据库统计信息
                stats = {}
                
                # 表大小统计
                cursor = conn.execute("""
                    SELECT name, 
                           (SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=m.name) as table_count
                    FROM sqlite_master m WHERE type='table'
                """)
                
                tables = {}
                for row in cursor.fetchall():
                    table_name = row['name']
                    if not table_name.startswith('sqlite_'):
                        # 获取表行数
                        count_cursor = conn.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                        row_count = count_cursor.fetchone()['count']
                        tables[table_name] = {'row_count': row_count}
                
                stats['tables'] = tables
                
                # 索引统计
                cursor = conn.execute("""
                    SELECT name, tbl_name, sql 
                    FROM sqlite_master 
                    WHERE type='index' AND name NOT LIKE 'sqlite_%'
                """)
                
                indexes = []
                for row in cursor.fetchall():
                    indexes.append({
                        'name': row['name'],
                        'table': row['tbl_name'],
                        'sql': row['sql']
                    })
                
                stats['indexes'] = indexes
                stats['query_stats'] = dict(self.query_stats)
                
                return stats
                
        except Exception as e:
            return {'error': str(e)}
    
    def optimize_database(self) -> Dict[str, Any]:
        """
        优化数据库
        
        Returns:
            Dict[str, Any]: 优化结果
        """
        results = []
        try:
            with self.db_manager.get_connection() as conn:
                # 执行VACUUM
                start_time = time.time()
                conn.execute("VACUUM")
                vacuum_time = time.time() - start_time
                results.append(f"VACUUM完成，耗时: {vacuum_time:.2f}秒")
                
                # 执行ANALYZE
                start_time = time.time()
                conn.execute("ANALYZE")
                analyze_time = time.time() - start_time
                results.append(f"ANALYZE完成，耗时: {analyze_time:.2f}秒")
                
                # 重建索引
                cursor = conn.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='index' AND name NOT LIKE 'sqlite_%'
                """)
                
                index_count = 0
                for row in cursor.fetchall():
                    conn.execute(f"REINDEX {row['name']}")
                    index_count += 1
                
                results.append(f"重建了{index_count}个索引")
                
                return {
                    'success': True,
                    'results': results,
                    'total_time': vacuum_time + analyze_time
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'results': results
            }
    
    def suggest_indexes(self) -> List[str]:
        """
        建议创建的索引
        
        Returns:
            List[str]: 索引建议
        """
        suggestions = []
        try:
            # 基于查询统计建议索引
            slow_queries = [
                query for query, stats in self.query_stats.items()
                if stats['avg_time'] > 0.1  # 平均查询时间超过100ms
            ]
            
            for query in slow_queries:
                if 'WHERE' in query.upper():
                    suggestions.append(f"考虑为查询创建索引: {query[:100]}...")
            
            # 基于表大小建议索引
            with self.db_manager.get_connection() as conn:
                # 检查大表是否有足够的索引
                for table in ['users', 'sessions', 'logs']:
                    try:
                        cursor = conn.execute(f"SELECT COUNT(*) as count FROM {table}")
                        row_count = cursor.fetchone()['count']
                        
                        if row_count > 1000:
                            # 检查是否有索引
                            cursor = conn.execute("""
                                SELECT COUNT(*) as index_count 
                                FROM sqlite_master 
                                WHERE type='index' AND tbl_name=? AND name NOT LIKE 'sqlite_%'
                            """, (table,))
                            
                            index_count = cursor.fetchone()['index_count']
                            
                            if index_count < 2:  # 除了主键外应该有其他索引
                                if table == 'users':
                                    suggestions.append("建议为users表的username字段创建索引")
                                elif table == 'sessions':
                                    suggestions.append("建议为sessions表的expires_at字段创建索引")
                                elif table == 'logs':
                                    suggestions.append("建议为logs表的timestamp字段创建索引")
                    except Exception:
                        continue
                        
        except Exception as e:
            suggestions.append(f"分析索引建议时发生错误: {e}")
        
        return suggestions
"""
服务器端主应用程序
整合所有模块，实现完整的网络验证服务器
"""
import sys
import os
import threading
import signal
import time
from typing import Dict, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.database_manager import DatabaseManager
from server.user_manager import UserManager
from server.session_manager import SessionManager
from server.authentication_handler import AuthenticationHandler
from server.socket_listener import SocketListener
from server.thread_pool_manager import ThreadPoolManager
from server.protocol_handler import ProtocolHandler
from server.security_utils import RateLimiter
from common.config_manager import ConfigManager
from common.log_manager import LogManager


class ServerApplication:
    """服务器应用程序主类"""
    
    def __init__(self, config_file: str = "config.ini"):
        """
        初始化服务器应用程序
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.is_running = False
        self.shutdown_event = threading.Event()
        
        # 核心组件
        self.config_manager = None
        self.log_manager = None
        self.db_manager = None
        self.user_manager = None
        self.session_manager = None
        self.auth_handler = None
        self.socket_listener = None
        self.thread_pool = None
        self.protocol_handler = None
        self.rate_limiter = None
        
        # 统计信息
        self.start_time = None
        self.stats = {
            'total_connections': 0,
            'successful_auths': 0,
            'failed_auths': 0,
            'active_sessions': 0
        }
        
        # 初始化组件
        self._initialize_components()
        
        # 设置信号处理
        self._setup_signal_handlers()
    
    def _initialize_components(self):
        """初始化所有组件"""
        try:
            print("正在初始化服务器组件...")
            
            # 1. 初始化配置管理器
            print("初始化配置管理器...")
            self.config_manager = ConfigManager(self.config_file)
            
            # 2. 初始化日志管理器
            print("初始化日志管理器...")
            log_config = self.config_manager.get_section('logging')
            self.log_manager = LogManager(log_config)
            self.log_manager.log_info("服务器应用程序开始初始化")
            
            # 3. 初始化数据库管理器
            self.log_manager.log_info("初始化数据库管理器...")
            db_path = self.config_manager.get_value('database', 'path', 'data/auth.db')
            
            # 确保数据目录存在
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            self.db_manager = DatabaseManager(db_path)
            
            # 4. 初始化用户管理器
            self.log_manager.log_info("初始化用户管理器...")
            self.user_manager = UserManager(self.db_manager)
            
            # 5. 初始化会话管理器
            self.log_manager.log_info("初始化会话管理器...")
            self.session_manager = SessionManager(self.db_manager)
            
            # 6. 初始化认证处理器
            self.log_manager.log_info("初始化认证处理器...")
            self.auth_handler = AuthenticationHandler(self.db_manager)
            
            # 7. 初始化线程池管理器
            self.log_manager.log_info("初始化线程池管理器...")
            max_threads = self.config_manager.get_value('server', 'max_connections', 50)
            self.thread_pool = ThreadPoolManager(max_threads)
            
            # 8. 初始化协议处理器
            self.log_manager.log_info("初始化协议处理器...")
            self.protocol_handler = ProtocolHandler(self.user_manager, self.session_manager)
            
            # 9. 初始化Socket监听器
            self.log_manager.log_info("初始化Socket监听器...")
            host = self.config_manager.get_value('server', 'host', '0.0.0.0')
            port = self.config_manager.get_value('server', 'port', 8888)
            max_connections = self.config_manager.get_value('server', 'max_connections', 50)
            self.socket_listener = SocketListener(host, port, max_connections)
            self.socket_listener.set_client_handler(self._handle_client_connection)
            
            # 10. 初始化速率限制器
            self.log_manager.log_info("初始化速率限制器...")
            max_attempts = self.config_manager.get_value('security', 'max_failed_attempts', 5)
            window_seconds = self.config_manager.get_value('security', 'lockout_duration', 1800)
            self.rate_limiter = RateLimiter(max_attempts, window_seconds)
            
            # 创建默认管理员用户（如果不存在）
            self._create_default_admin()
            
            self.log_manager.log_info("所有组件初始化完成")
            print("服务器组件初始化完成")
            
        except Exception as e:
            error_msg = f"初始化组件失败: {str(e)}"
            if self.log_manager:
                self.log_manager.log_error(error_msg)
            else:
                print(error_msg)
            raise
    
    def _create_default_admin(self):
        """创建默认管理员用户"""
        try:
            # 检查是否已存在管理员用户
            admin_user = self.user_manager.get_user_info('admin')
            if not admin_user:
                # 创建默认管理员
                result = self.user_manager.create_user('admin', 'admin123')
                if result['success']:
                    self.log_manager.log_info("已创建默认管理员用户 (用户名: admin, 密码: admin123)")
                    print("已创建默认管理员用户 (用户名: admin, 密码: admin123)")
                    print("请在首次登录后立即修改密码！")
                else:
                    self.log_manager.log_warning(f"创建默认管理员失败: {result['message']}")
        except Exception as e:
            self.log_manager.log_error(f"创建默认管理员时发生错误: {str(e)}")
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            print(f"\n收到信号 {signum}，正在关闭服务器...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start(self) -> Dict[str, Any]:
        """
        启动服务器
        Returns:
            Dict[str, Any]: 启动结果
        """
        try:
            if self.is_running:
                return {
                    'success': False,
                    'message': '服务器已在运行中'
                }
            
            self.log_manager.log_info("正在启动网络验证服务器...")
            print("正在启动网络验证服务器...")
            
            # 启动Socket监听器
            listen_result = self.socket_listener.start_listening()
            if not listen_result['success']:
                error_msg = f"启动Socket监听器失败: {listen_result['message']}"
                self.log_manager.log_error(error_msg)
                return {
                    'success': False,
                    'message': error_msg
                }
            
            # 标记为运行状态
            self.is_running = True
            self.start_time = datetime.now()
            
            # 记录启动信息
            host = self.config_manager.get_value('server', 'host', '0.0.0.0')
            port = listen_result.get('port', self.config_manager.get_value('server', 'port', 8888))
            success_msg = f"服务器已启动，监听 {host}:{port}"
            self.log_manager.log_info(success_msg)
            print(success_msg)
            
            # 启动监控线程
            self._start_monitoring()
            
            return {
                'success': True,
                'message': success_msg,
                'host': host,
                'port': port,
                'start_time': self.start_time.isoformat()
            }
            
        except Exception as e:
            error_msg = f"启动服务器失败: {str(e)}"
            self.log_manager.log_error(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def shutdown(self) -> Dict[str, Any]:
        """
        关闭服务器
        Returns:
            Dict[str, Any]: 关闭结果
        """
        try:
            if not self.is_running:
                return {
                    'success': False,
                    'message': '服务器未在运行'
                }
            
            self.log_manager.log_info("正在关闭网络验证服务器...")
            print("正在关闭网络验证服务器...")
            
            # 设置关闭标志
            self.is_running = False
            self.shutdown_event.set()
            
            # 关闭Socket监听器
            if self.socket_listener:
                stop_result = self.socket_listener.stop_listening()
                if stop_result['success']:
                    self.log_manager.log_info("Socket监听器已关闭")
                else:
                    self.log_manager.log_warning(f"关闭Socket监听器时发生警告: {stop_result['message']}")
            
            # 关闭线程池
            if self.thread_pool:
                shutdown_result = self.thread_pool.shutdown(wait=True, timeout=10)
                if shutdown_result['success']:
                    self.log_manager.log_info("线程池已关闭")
                else:
                    self.log_manager.log_warning(f"关闭线程池时发生警告: {shutdown_result['message']}")
            
            # 清理过期会话
            if self.session_manager:
                cleanup_result = self.session_manager.cleanup_expired_sessions()
                self.log_manager.log_info(f"会话清理完成: {cleanup_result.get('message', '')}")
            
            # 获取最终统计
            final_stats = self.get_server_statistics()
            
            success_msg = "服务器已关闭"
            self.log_manager.log_info(success_msg)
            print(success_msg)
            
            # 关闭日志系统
            if self.log_manager:
                self.log_manager.shutdown()
            
            return {
                'success': True,
                'message': success_msg,
                'final_statistics': final_stats
            }
            
        except Exception as e:
            error_msg = f"关闭服务器时发生错误: {str(e)}"
            if self.log_manager:
                self.log_manager.log_error(error_msg)
            else:
                print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def _handle_client_connection(self, conn_id: str, client_socket, address: tuple):
        """
        处理客户端连接
        Args:
            conn_id: 连接ID
            client_socket: 客户端socket
            address: 客户端地址
        """
        client_ip = address[0]
        session_token = None
        
        try:
            self.stats['total_connections'] += 1
            self.log_manager.log_info(f"新客户端连接 (连接ID: {conn_id}, IP: {client_ip})")
            
            # 发送欢迎消息
            welcome_msg = self.protocol_handler.create_welcome_message()
            self.socket_listener.send_message(conn_id, welcome_msg)
            
            # 处理客户端消息
            while self.is_running:
                try:
                    # 接收消息
                    message = self.socket_listener.receive_message(client_socket, timeout=30.0)
                    if not message:
                        break
                    
                    # 速率限制检查
                    rate_check = self.rate_limiter.is_allowed(client_ip)
                    if not rate_check['allowed']:
                        error_response = {
                            'type': 'error',
                            'message': f'请求过于频繁，请等待 {rate_check["remaining_time"]} 秒'
                        }
                        self.socket_listener.send_message(conn_id, error_response)
                        continue
                    
                    # 记录请求
                    self.rate_limiter.record_attempt(client_ip)
                    
                    # 处理消息
                    response = self.protocol_handler.process_message(conn_id, message, client_ip)
                    
                    # 更新统计
                    if message.get('type') == 'auth_request':
                        if response.get('success'):
                            self.stats['successful_auths'] += 1
                            session_token = response.get('session_token')
                        else:
                            self.stats['failed_auths'] += 1
                    
                    # 发送响应
                    self.socket_listener.send_message(conn_id, response)
                    
                except Exception as e:
                    self.log_manager.log_error(f"处理客户端消息时发生错误: {str(e)}")
                    break
                    
        except Exception as e:
            self.log_manager.log_error(f"处理客户端连接时发生错误: {str(e)}")
        finally:
            # 清理连接
            self.protocol_handler.handle_client_disconnect(conn_id, session_token)
            self.log_manager.log_info(f"客户端断开连接 (连接ID: {conn_id}, IP: {client_ip})")
    
    def _start_monitoring(self):
        """启动监控线程"""
        def monitoring_loop():
            """监控循环"""
            while self.is_running and not self.shutdown_event.is_set():
                try:
                    # 清理过期会话
                    if self.session_manager:
                        self.session_manager.cleanup_expired_sessions()
                    
                    # 更新活跃会话统计
                    if self.session_manager:
                        session_stats = self.session_manager.get_session_statistics()
                        self.stats['active_sessions'] = session_stats['active_sessions']
                    
                    # 等待5分钟或直到关闭事件
                    if self.shutdown_event.wait(300):  # 5分钟
                        break
                        
                except Exception as e:
                    self.log_manager.log_error(f"监控线程发生错误: {str(e)}")
                    time.sleep(60)  # 出错时等待1分钟
        
        monitor_thread = threading.Thread(target=monitoring_loop, daemon=True)
        monitor_thread.start()
        self.log_manager.log_info("监控线程已启动")
    
    def get_server_statistics(self) -> Dict[str, Any]:
        """
        获取服务器统计信息
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = self.stats.copy()
            
            # 添加运行时间
            if self.start_time:
                uptime_seconds = int((datetime.now() - self.start_time).total_seconds())
                stats['uptime_seconds'] = uptime_seconds
                stats['start_time'] = self.start_time.isoformat()
            else:
                stats['uptime_seconds'] = 0
                stats['start_time'] = None
            
            # 添加服务器状态
            stats['is_running'] = self.is_running
            stats['server_version'] = '1.0.0'
            
            return stats
            
        except Exception as e:
            self.log_manager.log_error(f"获取服务器统计信息失败: {str(e)}")
            return {
                'error': str(e),
                'is_running': self.is_running
            }
    
    def is_server_running(self) -> bool:
        """
        检查服务器是否正在运行
        Returns:
            bool: 正在运行返回True
        """
        return self.is_running
    
    def get_server_info(self) -> Dict[str, Any]:
        """
        获取服务器基本信息
        Returns:
            Dict[str, Any]: 服务器信息
        """
        try:
            host = self.config_manager.get_value('server', 'host', '0.0.0.0')
            port = self.config_manager.get_value('server', 'port', 8888)
            return {
                'server_name': 'Network Authentication Tool',
                'version': '1.0.0',
                'host': host,
                'port': port,
                'is_running': self.is_running,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'config_file': self.config_file,
                'database_path': self.config_manager.get_value('database', 'path', 'data/auth.db')
            }
        except Exception as e:
            return {
                'error': str(e),
                'is_running': self.is_running
            }


def main():
    """主函数"""
    try:
        print("网络验证工具服务器 v1.0.0")
        print("=" * 50)
        
        # 创建服务器应用程序
        app = ServerApplication()
        
        # 启动服务器
        start_result = app.start()
        if not start_result['success']:
            print(f"启动失败: {start_result['message']}")
            return 1
        
        print("服务器已启动，按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 等待关闭信号
        try:
            while app.is_server_running():
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        # 关闭服务器
        shutdown_result = app.shutdown()
        if shutdown_result['success']:
            print("服务器已正常关闭")
            return 0
        else:
            print(f"关闭时发生错误: {shutdown_result['message']}")
            return 1
            
    except Exception as e:
        print(f"服务器运行时发生严重错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
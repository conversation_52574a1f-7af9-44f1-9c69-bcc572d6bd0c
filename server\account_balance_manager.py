#!/usr/bin/env python3
"""
账户余额管理器
处理用户余额相关的所有操作，包括充值、扣费、查询等
"""

import sqlite3
import threading
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from contextlib import contextmanager

from server.database_manager import DatabaseManager


class AccountBalanceManager:
    """账户余额管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化余额管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self._lock = threading.Lock()
        
        # 余额操作的精度设置
        self.decimal_places = 2
        self.rounding = ROUND_HALF_UP
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接"""
        with self.db_manager.get_connection() as conn:
            yield conn
    
    def get_user_balance(self, username: str) -> Optional[Decimal]:
        """
        获取用户当前余额
        
        Args:
            username: 用户名
            
        Returns:
            Decimal: 用户余额，用户不存在返回None
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT balance FROM users WHERE username = ?
                """, (username,))
                
                result = cursor.fetchone()
                if result:
                    return Decimal(str(result[0])).quantize(
                        Decimal('0.01'), rounding=self.rounding
                    )
                return None
                
        except Exception as e:
            print(f"获取用户余额失败: {e}")
            return None
    
    def recharge_balance(self, username: str, amount: Decimal, 
                        operator: str = None, description: str = None,
                        recharge_type: str = 'manual') -> Dict[str, Any]:
        """
        为用户充值
        
        Args:
            username: 用户名
            amount: 充值金额
            operator: 操作员（管理员充值时使用）
            description: 充值描述
            recharge_type: 充值类型（manual/admin/system）
            
        Returns:
            Dict: 充值结果
        """
        # 验证充值金额
        validation_result = self._validate_recharge_amount(amount)
        if not validation_result['valid']:
            return {
                'success': False,
                'error': 'invalid_amount',
                'message': validation_result['message']
            }
        
        # 格式化金额
        amount = Decimal(str(amount)).quantize(
            Decimal('0.01'), rounding=self.rounding
        )
        
        with self._lock:
            try:
                with self._get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # 检查用户是否存在
                    cursor.execute("SELECT balance FROM users WHERE username = ?", (username,))
                    user_result = cursor.fetchone()
                    
                    if not user_result:
                        return {
                            'success': False,
                            'error': 'user_not_found',
                            'message': f'用户 "{username}" 不存在'
                        }
                    
                    current_balance = Decimal(str(user_result[0]))
                    new_balance = current_balance + amount
                    
                    # 更新用户余额
                    cursor.execute("""
                        UPDATE users SET balance = ?, updated_at = CURRENT_TIMESTAMP 
                        WHERE username = ?
                    """, (float(new_balance), username))
                    
                    # 记录充值历史
                    cursor.execute("""
                        INSERT INTO recharge_records 
                        (username, amount, recharge_type, operator, description, status)
                        VALUES (?, ?, ?, ?, ?, 'completed')
                    """, (username, float(amount), recharge_type, operator, description))
                    
                    conn.commit()
                    
                    print(f"用户 {username} 充值成功: +{amount}, 余额: {current_balance} -> {new_balance}")
                    
                    return {
                        'success': True,
                        'message': f'充值成功，金额: {amount}',
                        'previous_balance': float(current_balance),
                        'recharge_amount': float(amount),
                        'new_balance': float(new_balance)
                    }
                    
            except Exception as e:
                print(f"充值操作失败: {e}")
                return {
                    'success': False,
                    'error': 'recharge_failed',
                    'message': f'充值操作失败: {str(e)}'
                }
    
    def deduct_balance(self, username: str, amount: Decimal, 
                      service_type: str = 'auth', session_id: str = None,
                      description: str = None) -> Dict[str, Any]:
        """
        扣除用户余额
        
        Args:
            username: 用户名
            amount: 扣费金额
            service_type: 服务类型
            session_id: 会话ID
            description: 扣费描述
            
        Returns:
            Dict: 扣费结果
        """
        # 验证扣费金额
        if amount <= 0:
            return {
                'success': False,
                'error': 'invalid_amount',
                'message': '扣费金额必须大于0'
            }
        
        # 格式化金额
        amount = Decimal(str(amount)).quantize(
            Decimal('0.01'), rounding=self.rounding
        )
        
        with self._lock:
            try:
                with self._get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # 检查用户余额
                    cursor.execute("SELECT balance FROM users WHERE username = ?", (username,))
                    user_result = cursor.fetchone()
                    
                    if not user_result:
                        return {
                            'success': False,
                            'error': 'user_not_found',
                            'message': f'用户 "{username}" 不存在'
                        }
                    
                    current_balance = Decimal(str(user_result[0]))
                    
                    # 检查余额是否充足
                    if current_balance < amount:
                        return {
                            'success': False,
                            'error': 'insufficient_balance',
                            'message': f'余额不足，当前余额: {current_balance}，需要: {amount}',
                            'current_balance': float(current_balance),
                            'required_amount': float(amount)
                        }
                    
                    new_balance = current_balance - amount
                    
                    # 更新用户余额
                    cursor.execute("""
                        UPDATE users SET balance = ?, updated_at = CURRENT_TIMESTAMP 
                        WHERE username = ?
                    """, (float(new_balance), username))
                    
                    # 记录消费历史
                    cursor.execute("""
                        INSERT INTO consumption_records 
                        (username, amount, service_type, session_id, description)
                        VALUES (?, ?, ?, ?, ?)
                    """, (username, float(amount), service_type, session_id, description))
                    
                    conn.commit()
                    
                    print(f"用户 {username} 扣费成功: -{amount}, 余额: {current_balance} -> {new_balance}")
                    
                    return {
                        'success': True,
                        'message': f'扣费成功，金额: {amount}',
                        'previous_balance': float(current_balance),
                        'deducted_amount': float(amount),
                        'new_balance': float(new_balance)
                    }
                    
            except Exception as e:
                print(f"扣费操作失败: {e}")
                return {
                    'success': False,
                    'error': 'deduction_failed',
                    'message': f'扣费操作失败: {str(e)}'
                }
    
    def check_sufficient_balance(self, username: str, required_amount: Decimal) -> Dict[str, Any]:
        """
        检查用户余额是否充足
        
        Args:
            username: 用户名
            required_amount: 所需金额
            
        Returns:
            Dict: 检查结果
        """
        current_balance = self.get_user_balance(username)
        
        if current_balance is None:
            return {
                'sufficient': False,
                'error': 'user_not_found',
                'message': f'用户 "{username}" 不存在'
            }
        
        required_amount = Decimal(str(required_amount)).quantize(
            Decimal('0.01'), rounding=self.rounding
        )
        
        sufficient = current_balance >= required_amount
        
        return {
            'sufficient': sufficient,
            'current_balance': float(current_balance),
            'required_amount': float(required_amount),
            'shortage': float(required_amount - current_balance) if not sufficient else 0.0
        }
    
    def get_recharge_history(self, username: str, limit: int = 50, 
                           offset: int = 0) -> List[Dict[str, Any]]:
        """
        获取用户充值历史
        
        Args:
            username: 用户名
            limit: 返回记录数限制
            offset: 偏移量
            
        Returns:
            List[Dict]: 充值历史记录列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, amount, recharge_time, recharge_type, 
                           operator, description, status
                    FROM recharge_records 
                    WHERE username = ?
                    ORDER BY recharge_time DESC
                    LIMIT ? OFFSET ?
                """, (username, limit, offset))
                
                records = []
                for row in cursor.fetchall():
                    records.append({
                        'id': row[0],
                        'amount': float(row[1]),
                        'recharge_time': row[2],
                        'recharge_type': row[3],
                        'operator': row[4],
                        'description': row[5],
                        'status': row[6]
                    })
                
                return records
                
        except Exception as e:
            print(f"获取充值历史失败: {e}")
            return []
    
    def get_consumption_history(self, username: str, limit: int = 50, 
                              offset: int = 0) -> List[Dict[str, Any]]:
        """
        获取用户消费历史
        
        Args:
            username: 用户名
            limit: 返回记录数限制
            offset: 偏移量
            
        Returns:
            List[Dict]: 消费历史记录列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, amount, consumption_time, service_type, 
                           duration, session_id, description
                    FROM consumption_records 
                    WHERE username = ?
                    ORDER BY consumption_time DESC
                    LIMIT ? OFFSET ?
                """, (username, limit, offset))
                
                records = []
                for row in cursor.fetchall():
                    records.append({
                        'id': row[0],
                        'amount': float(row[1]),
                        'consumption_time': row[2],
                        'service_type': row[3],
                        'duration': row[4],
                        'session_id': row[5],
                        'description': row[6]
                    })
                
                return records
                
        except Exception as e:
            print(f"获取消费历史失败: {e}")
            return []
    
    def get_balance_summary(self, username: str) -> Dict[str, Any]:
        """
        获取用户余额汇总信息
        
        Args:
            username: 用户名
            
        Returns:
            Dict: 余额汇总信息
        """
        try:
            current_balance = self.get_user_balance(username)
            
            if current_balance is None:
                return {
                    'success': False,
                    'error': 'user_not_found',
                    'message': f'用户 "{username}" 不存在'
                }
            
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取总充值金额
                cursor.execute("""
                    SELECT COALESCE(SUM(amount), 0) FROM recharge_records 
                    WHERE username = ? AND status = 'completed'
                """, (username,))
                total_recharged = Decimal(str(cursor.fetchone()[0]))
                
                # 获取总消费金额
                cursor.execute("""
                    SELECT COALESCE(SUM(amount), 0) FROM consumption_records 
                    WHERE username = ?
                """, (username,))
                total_consumed = Decimal(str(cursor.fetchone()[0]))
                
                # 获取最近充值时间
                cursor.execute("""
                    SELECT MAX(recharge_time) FROM recharge_records 
                    WHERE username = ? AND status = 'completed'
                """, (username,))
                last_recharge = cursor.fetchone()[0]
                
                # 获取最近消费时间
                cursor.execute("""
                    SELECT MAX(consumption_time) FROM consumption_records 
                    WHERE username = ?
                """, (username,))
                last_consumption = cursor.fetchone()[0]
                
                return {
                    'success': True,
                    'current_balance': float(current_balance),
                    'total_recharged': float(total_recharged),
                    'total_consumed': float(total_consumed),
                    'last_recharge_time': last_recharge,
                    'last_consumption_time': last_consumption
                }
                
        except Exception as e:
            print(f"获取余额汇总失败: {e}")
            return {
                'success': False,
                'error': 'query_failed',
                'message': f'获取余额汇总失败: {str(e)}'
            }
    
    def _validate_recharge_amount(self, amount: Decimal) -> Dict[str, Any]:
        """
        验证充值金额
        
        Args:
            amount: 充值金额
            
        Returns:
            Dict: 验证结果
        """
        try:
            amount = Decimal(str(amount))
        except (ValueError, TypeError):
            return {
                'valid': False,
                'message': '充值金额格式无效'
            }
        
        if amount <= 0:
            return {
                'valid': False,
                'message': '充值金额必须大于0'
            }
        
        if amount > Decimal('10000'):
            return {
                'valid': False,
                'message': '单次充值金额不能超过10000元'
            }
        
        # 检查小数位数
        if amount.as_tuple().exponent < -2:
            return {
                'valid': False,
                'message': '充值金额最多支持2位小数'
            }
        
        return {
            'valid': True,
            'message': '充值金额有效'
        }
    
    def get_user_statistics(self) -> Dict[str, Any]:
        """
        获取用户余额统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 总用户数
                cursor.execute("SELECT COUNT(*) FROM users")
                total_users = cursor.fetchone()[0]
                
                # 有余额的用户数
                cursor.execute("SELECT COUNT(*) FROM users WHERE balance > 0")
                users_with_balance = cursor.fetchone()[0]
                
                # 总余额
                cursor.execute("SELECT COALESCE(SUM(balance), 0) FROM users")
                total_balance = Decimal(str(cursor.fetchone()[0]))
                
                # 平均余额
                avg_balance = total_balance / total_users if total_users > 0 else Decimal('0')
                
                # 今日充值总额
                cursor.execute("""
                    SELECT COALESCE(SUM(amount), 0) FROM recharge_records 
                    WHERE DATE(recharge_time) = DATE('now') AND status = 'completed'
                """)
                today_recharge = Decimal(str(cursor.fetchone()[0]))
                
                # 今日消费总额
                cursor.execute("""
                    SELECT COALESCE(SUM(amount), 0) FROM consumption_records 
                    WHERE DATE(consumption_time) = DATE('now')
                """)
                today_consumption = Decimal(str(cursor.fetchone()[0]))
                
                return {
                    'total_users': total_users,
                    'users_with_balance': users_with_balance,
                    'total_balance': float(total_balance),
                    'average_balance': float(avg_balance),
                    'today_recharge': float(today_recharge),
                    'today_consumption': float(today_consumption)
                }
                
        except Exception as e:
            print(f"获取用户统计失败: {e}")
            return {}
"""
配置管理模块
实现配置文件加载、验证、保存和默认配置生成
"""

import configparser
import os
import json
import threading
from typing import Dict, Any, Optional, Union
from datetime import datetime

from common.interfaces import IConfigManager
from common.exceptions import ConfigurationError
from common.constants import DEFAULT_CONFIG


class ConfigManager(IConfigManager):
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.ini"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self._lock = threading.Lock()
        
        # 配置缓存
        self._config_cache = {}
        self._last_modified = None
        
        # 加载配置
        self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        with self._lock:
            try:
                # 检查配置文件是否存在
                if not os.path.exists(self.config_file):
                    print(f"配置文件 {self.config_file} 不存在，创建默认配置")
                    self._create_default_config()
                
                # 读取配置文件
                self.config.read(self.config_file, encoding='utf-8')
                
                # 验证配置
                validation_result = self._validate_config()
                if not validation_result['valid']:
                    print(f"配置验证失败: {validation_result['message']}")
                    print("使用默认配置")
                    self._create_default_config()
                    self.config.read(self.config_file, encoding='utf-8')
                
                # 更新缓存
                self._update_cache()
                
                # 记录最后修改时间
                self._last_modified = os.path.getmtime(self.config_file)
                
                return self._config_cache
                
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                print("使用默认配置")
                self._load_default_config()
                return self._config_cache
    
    def save_config(self, config: Dict[str, Any] = None) -> None:
        """
        保存配置到文件
        
        Args:
            config: 要保存的配置字典，为None时保存当前配置
        """
        with self._lock:
            try:
                if config:
                    # 更新配置
                    self._update_config_from_dict(config)
                
                # 创建配置文件目录
                config_dir = os.path.dirname(self.config_file)
                if config_dir and not os.path.exists(config_dir):
                    os.makedirs(config_dir, exist_ok=True)
                
                # 写入配置文件
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    self.config.write(f)
                
                # 更新缓存和修改时间
                self._update_cache()
                self._last_modified = os.path.getmtime(self.config_file)
                
                print(f"配置已保存到 {self.config_file}")
                
            except Exception as e:
                raise ConfigurationError(f"保存配置文件失败: {str(e)}")
    
    def get_value(self, section: str, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            section: 配置节
            key: 配置键
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        try:
            # 检查文件是否被修改
            self._check_file_modified()
            
            if section in self._config_cache and key in self._config_cache[section]:
                return self._config_cache[section][key]
            
            return default
            
        except Exception:
            return default
    
    def set_value(self, section: str, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            section: 配置节
            key: 配置键
            value: 配置值
        """
        with self._lock:
            try:
                # 确保节存在
                if not self.config.has_section(section):
                    self.config.add_section(section)
                
                # 设置值
                self.config.set(section, key, str(value))
                
                # 更新缓存
                if section not in self._config_cache:
                    self._config_cache[section] = {}
                self._config_cache[section][key] = self._convert_value(str(value))
                
            except Exception as e:
                raise ConfigurationError(f"设置配置值失败: {str(e)}")
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        获取整个配置节
        
        Args:
            section: 配置节名
            
        Returns:
            Dict[str, Any]: 配置节字典
        """
        self._check_file_modified()
        return self._config_cache.get(section, {})
    
    def has_section(self, section: str) -> bool:
        """
        检查配置节是否存在
        
        Args:
            section: 配置节名
            
        Returns:
            bool: 存在返回True
        """
        return section in self._config_cache
    
    def has_option(self, section: str, key: str) -> bool:
        """
        检查配置项是否存在
        
        Args:
            section: 配置节名
            key: 配置键名
            
        Returns:
            bool: 存在返回True
        """
        return (section in self._config_cache and 
                key in self._config_cache[section])
    
    def remove_option(self, section: str, key: str) -> bool:
        """
        删除配置项
        
        Args:
            section: 配置节名
            key: 配置键名
            
        Returns:
            bool: 删除成功返回True
        """
        with self._lock:
            try:
                if self.config.has_option(section, key):
                    self.config.remove_option(section, key)
                    
                    if (section in self._config_cache and 
                        key in self._config_cache[section]):
                        del self._config_cache[section][key]
                    
                    return True
                return False
                
            except Exception:
                return False
    
    def remove_section(self, section: str) -> bool:
        """
        删除配置节
        
        Args:
            section: 配置节名
            
        Returns:
            bool: 删除成功返回True
        """
        with self._lock:
            try:
                if self.config.has_section(section):
                    self.config.remove_section(section)
                    
                    if section in self._config_cache:
                        del self._config_cache[section]
                    
                    return True
                return False
                
            except Exception:
                return False
    
    def get_all_config(self) -> Dict[str, Any]:
        """
        获取所有配置
        
        Returns:
            Dict[str, Any]: 完整配置字典
        """
        self._check_file_modified()
        return self._config_cache.copy()
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        with self._lock:
            self._create_default_config()
            self.load_config()
    
    def backup_config(self, backup_file: str = None) -> str:
        """
        备份配置文件
        
        Args:
            backup_file: 备份文件路径，为None时自动生成
            
        Returns:
            str: 备份文件路径
        """
        if not backup_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"{self.config_file}.backup_{timestamp}"
        
        try:
            import shutil
            shutil.copy2(self.config_file, backup_file)
            return backup_file
            
        except Exception as e:
            raise ConfigurationError(f"备份配置文件失败: {str(e)}")
    
    def restore_config(self, backup_file: str) -> None:
        """
        从备份恢复配置
        
        Args:
            backup_file: 备份文件路径
        """
        try:
            import shutil
            shutil.copy2(backup_file, self.config_file)
            self.load_config()
            
        except Exception as e:
            raise ConfigurationError(f"恢复配置文件失败: {str(e)}")
    
    def _create_default_config(self):
        """创建默认配置文件"""
        try:
            # 清空现有配置
            self.config.clear()
            
            # 添加默认配置
            for section_name, section_data in DEFAULT_CONFIG.items():
                self.config.add_section(section_name)
                for key, value in section_data.items():
                    self.config.set(section_name, key, str(value))
            
            # 保存到文件
            config_dir = os.path.dirname(self.config_file)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            
            print(f"已创建默认配置文件: {self.config_file}")
            
        except Exception as e:
            raise ConfigurationError(f"创建默认配置文件失败: {str(e)}")
    
    def _load_default_config(self):
        """加载默认配置到内存"""
        self._config_cache = {}
        for section_name, section_data in DEFAULT_CONFIG.items():
            self._config_cache[section_name] = section_data.copy()
    
    def _validate_config(self) -> Dict[str, Any]:
        """
        验证配置文件
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 检查必需的节
            required_sections = ['server', 'database', 'security', 'logging']
            for section in required_sections:
                if not self.config.has_section(section):
                    return {
                        'valid': False,
                        'message': f'缺少必需的配置节: {section}'
                    }
            
            # 验证服务器配置
            server_validation = self._validate_server_config()
            if not server_validation['valid']:
                return server_validation
            
            # 验证数据库配置
            database_validation = self._validate_database_config()
            if not database_validation['valid']:
                return database_validation
            
            # 验证安全配置
            security_validation = self._validate_security_config()
            if not security_validation['valid']:
                return security_validation
            
            # 验证日志配置
            logging_validation = self._validate_logging_config()
            if not logging_validation['valid']:
                return logging_validation
            
            return {'valid': True, 'message': '配置验证通过'}
            
        except Exception as e:
            return {
                'valid': False,
                'message': f'配置验证时发生错误: {str(e)}'
            }
    
    def _validate_server_config(self) -> Dict[str, Any]:
        """验证服务器配置"""
        try:
            port = self.config.getint('server', 'port')
            if port < 1 or port > 65535:
                return {'valid': False, 'message': '服务器端口必须在1-65535范围内'}
            
            max_connections = self.config.getint('server', 'max_connections')
            if max_connections < 1 or max_connections > 1000:
                return {'valid': False, 'message': '最大连接数必须在1-1000范围内'}
            
            session_timeout = self.config.getint('server', 'session_timeout')
            if session_timeout < 60 or session_timeout > 86400:
                return {'valid': False, 'message': '会话超时时间必须在60-86400秒范围内'}
            
            return {'valid': True, 'message': '服务器配置验证通过'}
            
        except Exception as e:
            return {'valid': False, 'message': f'服务器配置验证失败: {str(e)}'}
    
    def _validate_database_config(self) -> Dict[str, Any]:
        """验证数据库配置"""
        try:
            db_path = self.config.get('database', 'path')
            if not db_path:
                return {'valid': False, 'message': '数据库路径不能为空'}
            
            backup_interval = self.config.getint('database', 'backup_interval')
            if backup_interval < 1 or backup_interval > 168:
                return {'valid': False, 'message': '备份间隔必须在1-168小时范围内'}
            
            return {'valid': True, 'message': '数据库配置验证通过'}
            
        except Exception as e:
            return {'valid': False, 'message': f'数据库配置验证失败: {str(e)}'}
    
    def _validate_security_config(self) -> Dict[str, Any]:
        """验证安全配置"""
        try:
            max_failed_attempts = self.config.getint('security', 'max_failed_attempts')
            if max_failed_attempts < 1 or max_failed_attempts > 20:
                return {'valid': False, 'message': '最大失败尝试次数必须在1-20范围内'}
            
            lockout_duration = self.config.getint('security', 'lockout_duration')
            if lockout_duration < 60 or lockout_duration > 86400:
                return {'valid': False, 'message': '锁定时长必须在60-86400秒范围内'}
            
            password_min_length = self.config.getint('security', 'password_min_length')
            if password_min_length < 4 or password_min_length > 128:
                return {'valid': False, 'message': '密码最小长度必须在4-128范围内'}
            
            return {'valid': True, 'message': '安全配置验证通过'}
            
        except Exception as e:
            return {'valid': False, 'message': f'安全配置验证失败: {str(e)}'}
    
    def _validate_logging_config(self) -> Dict[str, Any]:
        """验证日志配置"""
        try:
            level = self.config.get('logging', 'level')
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if level not in valid_levels:
                return {'valid': False, 'message': f'日志级别必须是: {", ".join(valid_levels)}'}
            
            max_file_size = self.config.getint('logging', 'max_file_size')
            if max_file_size < 1024 or max_file_size > 1073741824:  # 1KB - 1GB
                return {'valid': False, 'message': '日志文件最大大小必须在1KB-1GB范围内'}
            
            backup_count = self.config.getint('logging', 'backup_count')
            if backup_count < 1 or backup_count > 100:
                return {'valid': False, 'message': '日志备份数量必须在1-100范围内'}
            
            return {'valid': True, 'message': '日志配置验证通过'}
            
        except Exception as e:
            return {'valid': False, 'message': f'日志配置验证失败: {str(e)}'}
    
    def _update_cache(self):
        """更新配置缓存"""
        self._config_cache = {}
        
        for section_name in self.config.sections():
            self._config_cache[section_name] = {}
            for key, value in self.config.items(section_name):
                self._config_cache[section_name][key] = self._convert_value(value)
    
    def _convert_value(self, value: str) -> Union[str, int, float, bool]:
        """
        转换配置值类型
        
        Args:
            value: 字符串值
            
        Returns:
            Union[str, int, float, bool]: 转换后的值
        """
        # 尝试转换为布尔值
        if value.lower() in ('true', 'yes', '1', 'on'):
            return True
        elif value.lower() in ('false', 'no', '0', 'off'):
            return False
        
        # 尝试转换为整数
        try:
            return int(value)
        except ValueError:
            pass
        
        # 尝试转换为浮点数
        try:
            return float(value)
        except ValueError:
            pass
        
        # 返回字符串
        return value
    
    def _update_config_from_dict(self, config_dict: Dict[str, Any]):
        """
        从字典更新配置
        
        Args:
            config_dict: 配置字典
        """
        for section_name, section_data in config_dict.items():
            if not self.config.has_section(section_name):
                self.config.add_section(section_name)
            
            for key, value in section_data.items():
                self.config.set(section_name, key, str(value))
    
    def _check_file_modified(self):
        """检查配置文件是否被修改"""
        try:
            if os.path.exists(self.config_file):
                current_modified = os.path.getmtime(self.config_file)
                if (self._last_modified is None or 
                    current_modified > self._last_modified):
                    print("检测到配置文件已修改，重新加载")
                    self.load_config()
        except Exception:
            pass
    
    def get_config_info(self) -> Dict[str, Any]:
        """
        获取配置信息
        
        Returns:
            Dict[str, Any]: 配置信息
        """
        try:
            file_size = os.path.getsize(self.config_file) if os.path.exists(self.config_file) else 0
            file_modified = datetime.fromtimestamp(self._last_modified).isoformat() if self._last_modified else None
            
            return {
                'config_file': self.config_file,
                'file_exists': os.path.exists(self.config_file),
                'file_size': file_size,
                'last_modified': file_modified,
                'sections_count': len(self._config_cache),
                'total_options': sum(len(section) for section in self._config_cache.values())
            }
            
        except Exception as e:
            return {
                'config_file': self.config_file,
                'error': str(e)
            }
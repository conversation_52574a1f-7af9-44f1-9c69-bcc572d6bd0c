#!/usr/bin/env python3
"""
系统优化工具
提供自动化的系统性能优化和调试功能
"""

import sys
import os
import argparse
import time
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.performance_monitor import PerformanceMonitor, DatabaseOptimizer
from common.debug_tools import SystemDiagnostics, debug_logger
from common.log_manager import LogManager
from server.database_manager import DatabaseManager


class SystemOptimizer:
    """系统优化器"""
    
    def __init__(self):
        """初始化系统优化器"""
        self.performance_monitor = PerformanceMonitor()
        self.system_diagnostics = SystemDiagnostics()
        self.log_manager = LogManager()
        
        # 数据库优化器（需要时初始化）
        self.db_optimizer = None
    
    def run_full_optimization(self) -> Dict[str, Any]:
        """
        运行完整的系统优化
        
        Returns:
            Dict[str, Any]: 优化结果
        """
        print("开始系统优化...")
        results = {
            'start_time': time.time(),
            'steps': [],
            'summary': {
                'total_steps': 0,
                'successful_steps': 0,
                'failed_steps': 0
            }
        }
        
        # 1. 运行系统诊断
        print("1. 运行系统诊断...")
        diag_result = self._run_diagnostics()
        results['steps'].append({
            'step': 'system_diagnostics',
            'result': diag_result,
            'success': diag_result.get('summary', {}).get('failed', 0) == 0
        })
        
        # 2. 内存优化
        print("2. 执行内存优化...")
        memory_result = self._optimize_memory()
        results['steps'].append({
            'step': 'memory_optimization',
            'result': memory_result,
            'success': memory_result.get('success', False)
        })
        
        # 3. 数据库优化
        print("3. 执行数据库优化...")
        db_result = self._optimize_database()
        results['steps'].append({
            'step': 'database_optimization',
            'result': db_result,
            'success': db_result.get('success', False)
        })
        
        # 4. 日志清理
        print("4. 执行日志清理...")
        log_result = self._cleanup_logs()
        results['steps'].append({
            'step': 'log_cleanup',
            'result': log_result,
            'success': log_result.get('success', False)
        })
        
        # 5. 性能基准测试
        print("5. 执行性能基准测试...")
        benchmark_result = self._run_benchmark()
        results['steps'].append({
            'step': 'performance_benchmark',
            'result': benchmark_result,
            'success': benchmark_result.get('success', False)
        })
        
        # 计算摘要
        results['summary']['total_steps'] = len(results['steps'])
        results['summary']['successful_steps'] = sum(1 for step in results['steps'] if step['success'])
        results['summary']['failed_steps'] = results['summary']['total_steps'] - results['summary']['successful_steps']
        results['end_time'] = time.time()
        results['total_duration'] = results['end_time'] - results['start_time']
        
        print(f"系统优化完成，耗时: {results['total_duration']:.2f}秒")
        print(f"成功步骤: {results['summary']['successful_steps']}/{results['summary']['total_steps']}")
        
        return results
    
    def _run_diagnostics(self) -> Dict[str, Any]:
        """运行系统诊断"""
        try:
            return self.system_diagnostics.run_diagnostics()
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _optimize_memory(self) -> Dict[str, Any]:
        """优化内存使用"""
        try:
            # 执行垃圾回收和内存优化
            result = self.performance_monitor.optimize_memory()
            
            # 记录优化结果
            if result.get('success'):
                debug_logger.debug(
                    "内存优化完成",
                    context={
                        'objects_collected': result.get('objects_collected', 0),
                        'memory_freed': result.get('memory_freed', 0)
                    }
                )
            
            return result
            
        except Exception as e:
            error_result = {
                'success': False,
                'error': str(e)
            }
            debug_logger.error("内存优化失败", e)
            return error_result
    
    def _optimize_database(self) -> Dict[str, Any]:
        """优化数据库"""
        try:
            # 初始化数据库优化器
            if not self.db_optimizer:
                db_path = 'data/auth.db'
                if os.path.exists(db_path):
                    db_manager = DatabaseManager(db_path)
                    self.db_optimizer = DatabaseOptimizer(db_manager)
                else:
                    return {
                        'success': False,
                        'message': '数据库文件不存在，跳过数据库优化'
                    }
            
            # 执行数据库优化
            result = self.db_optimizer.optimize_database()
            
            if result.get('success'):
                debug_logger.debug(
                    "数据库优化完成",
                    context={
                        'results': result.get('results', []),
                        'total_time': result.get('total_time', 0)
                    }
                )
            
            return result
            
        except Exception as e:
            error_result = {
                'success': False,
                'error': str(e)
            }
            debug_logger.error("数据库优化失败", e)
            return error_result
    
    def _cleanup_logs(self) -> Dict[str, Any]:
        """清理日志文件"""
        try:
            cleaned_files = []
            total_size_freed = 0
            
            # 清理旧的日志文件
            log_dirs = ['logs', 'data/logs']
            
            for log_dir in log_dirs:
                if os.path.exists(log_dir):
                    for filename in os.listdir(log_dir):
                        file_path = os.path.join(log_dir, filename)
                        
                        if os.path.isfile(file_path) and filename.endswith('.log'):
                            # 检查文件大小和修改时间
                            file_size = os.path.getsize(file_path)
                            file_mtime = os.path.getmtime(file_path)
                            
                            # 如果文件超过7天且大于10MB，则清理
                            if (time.time() - file_mtime > 7 * 24 * 3600 and 
                                file_size > 10 * 1024 * 1024):
                                
                                try:
                                    os.remove(file_path)
                                    cleaned_files.append(filename)
                                    total_size_freed += file_size
                                except Exception as e:
                                    debug_logger.error(f"删除日志文件失败: {filename}", e)
            
            result = {
                'success': True,
                'cleaned_files': cleaned_files,
                'files_count': len(cleaned_files),
                'size_freed': total_size_freed
            }
            
            debug_logger.debug(
                "日志清理完成",
                context=result
            )
            
            return result
            
        except Exception as e:
            error_result = {
                'success': False,
                'error': str(e)
            }
            debug_logger.error("日志清理失败", e)
            return error_result
    
    def _run_benchmark(self) -> Dict[str, Any]:
        """运行性能基准测试"""
        try:
            # 简单的性能基准测试
            benchmark_results = {}
            
            # CPU基准测试
            start_time = time.time()
            # 执行一些CPU密集型操作
            result = sum(i * i for i in range(100000))
            cpu_time = time.time() - start_time
            benchmark_results['cpu_benchmark'] = {
                'duration': cpu_time,
                'operations_per_second': 100000 / cpu_time if cpu_time > 0 else 0
            }
            
            # 内存基准测试
            start_time = time.time()
            # 创建和销毁一些对象
            test_data = [list(range(1000)) for _ in range(1000)]
            del test_data
            memory_time = time.time() - start_time
            benchmark_results['memory_benchmark'] = {
                'duration': memory_time,
                'allocations_per_second': 1000000 / memory_time if memory_time > 0 else 0
            }
            
            # 文件I/O基准测试
            start_time = time.time()
            test_file = 'benchmark_test.tmp'
            try:
                with open(test_file, 'w') as f:
                    for i in range(10000):
                        f.write(f"test line {i}\n")
                
                with open(test_file, 'r') as f:
                    lines = f.readlines()
                
                os.remove(test_file)
                
                io_time = time.time() - start_time
                benchmark_results['io_benchmark'] = {
                    'duration': io_time,
                    'lines_per_second': 20000 / io_time if io_time > 0 else 0  # 写入+读取
                }
                
            except Exception as e:
                benchmark_results['io_benchmark'] = {
                    'error': str(e)
                }
            
            result = {
                'success': True,
                'benchmarks': benchmark_results,
                'total_duration': sum(
                    b.get('duration', 0) for b in benchmark_results.values() 
                    if isinstance(b, dict) and 'duration' in b
                )
            }
            
            debug_logger.debug(
                "性能基准测试完成",
                context=result
            )
            
            return result
            
        except Exception as e:
            error_result = {
                'success': False,
                'error': str(e)
            }
            debug_logger.error("性能基准测试失败", e)
            return error_result
    
    def generate_optimization_report(self, results: Dict[str, Any]) -> str:
        """
        生成优化报告
        
        Args:
            results: 优化结果
            
        Returns:
            str: 报告内容
        """
        report_lines = [
            "系统优化报告",
            "=" * 50,
            f"优化时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"总耗时: {results.get('total_duration', 0):.2f}秒",
            f"成功步骤: {results['summary']['successful_steps']}/{results['summary']['total_steps']}",
            "",
            "详细结果:",
            "-" * 30
        ]
        
        for i, step in enumerate(results['steps'], 1):
            step_name = step['step'].replace('_', ' ').title()
            status = "√" if step['success'] else "×"
            report_lines.append(f"{i}. {step_name}: {status}")
            
            # 添加详细信息
            step_result = step['result']
            if step['success'] and isinstance(step_result, dict):
                if step['step'] == 'memory_optimization':
                    freed = step_result.get('memory_freed', 0)
                    if freed > 0:
                        report_lines.append(f"   释放内存: {freed / 1024 / 1024:.2f} MB")
                
                elif step['step'] == 'database_optimization':
                    total_time = step_result.get('total_time', 0)
                    if total_time > 0:
                        report_lines.append(f"   优化耗时: {total_time:.2f}秒")
                
                elif step['step'] == 'log_cleanup':
                    files_count = step_result.get('files_count', 0)
                    size_freed = step_result.get('size_freed', 0)
                    if files_count > 0:
                        report_lines.append(f"   清理文件: {files_count}个")
                        report_lines.append(f"   释放空间: {size_freed / 1024 / 1024:.2f} MB")
                
                elif step['step'] == 'performance_benchmark':
                    benchmarks = step_result.get('benchmarks', {})
                    for bench_name, bench_data in benchmarks.items():
                        if isinstance(bench_data, dict) and 'duration' in bench_data:
                            report_lines.append(f"   {bench_name}: {bench_data['duration']:.3f}秒")
            
            elif not step['success']:
                error = step_result.get('error', '未知错误')
                report_lines.append(f"   错误: {error}")
            
            report_lines.append("")
        
        # 添加建议
        report_lines.extend([
            "优化建议:",
            "-" * 30
        ])
        
        failed_steps = [step for step in results['steps'] if not step['success']]
        if failed_steps:
            report_lines.append("• 检查失败的优化步骤并解决相关问题")
        
        if results['summary']['successful_steps'] == results['summary']['total_steps']:
            report_lines.append("• 系统优化完成，建议定期运行以保持最佳性能")
        else:
            report_lines.append("• 部分优化步骤失败，建议检查系统配置和权限")
        
        return "\n".join(report_lines)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='系统优化工具')
    parser.add_argument('--report', '-r', action='store_true', 
                       help='生成优化报告文件')
    parser.add_argument('--output', '-o', default='optimization_report.txt',
                       help='报告输出文件名')
    
    args = parser.parse_args()
    
    try:
        print("网络验证工具 - 系统优化器")
        print("=" * 50)
        
        # 创建优化器
        optimizer = SystemOptimizer()
        
        # 运行优化
        results = optimizer.run_full_optimization()
        
        # 生成报告
        if args.report:
            report = optimizer.generate_optimization_report(results)
            
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(report)
                print(f"\n优化报告已保存到: {args.output}")
            except Exception as e:
                print(f"保存报告失败: {e}")
                print("\n报告内容:")
                print(report)
        else:
            # 显示简要结果
            print(f"\n优化完成!")
            print(f"成功步骤: {results['summary']['successful_steps']}/{results['summary']['total_steps']}")
            print(f"总耗时: {results.get('total_duration', 0):.2f}秒")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n优化被用户中断")
        return 1
    except Exception as e:
        print(f"优化过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
#!/usr/bin/env python3
"""
测试充值界面功能
"""

import os
import sys
import tkinter as tk
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_manager import DatabaseManager
from server.account_balance_manager import AccountBalanceManager
from server.user_registration_manager import UserRegistrationManager
from client.recharge_dialog import RechargeDialog


def test_recharge_interface():
    """测试充值界面功能"""
    print("=" * 60)
    print("测试充值界面功能")
    print("=" * 60)
    
    # 使用测试数据库
    test_db_path = "data/test_recharge_ui.db"
    
    # 删除测试数据库（如果存在）
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
        print("已删除旧的测试数据库")
    
    try:
        # 初始化组件
        print("\n1. 初始化组件...")
        db_manager = DatabaseManager(test_db_path)
        balance_manager = AccountBalanceManager(db_manager)
        registration_manager = UserRegistrationManager(db_manager)
        
        print("✓ 组件初始化完成")
        
        # 创建测试用户
        print("\n2. 创建测试用户...")
        
        result = registration_manager.register_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>'
        )
        
        if result['success']:
            print(f"✓ 测试用户创建成功: {result}")
        else:
            print(f"✗ 测试用户创建失败: {result}")
            return False
        
        # 先充值一些钱作为当前余额
        balance_manager.recharge_balance('testuser', Decimal('25.50'), description='初始余额')
        current_balance = float(balance_manager.get_user_balance('testuser'))
        print(f"✓ 设置初始余额: ¥{current_balance:.2f}")
        
        # 创建充值回调函数
        def recharge_callback(username, amount):
            """充值回调函数"""
            print(f"\n执行充值: 用户={username}, 金额={amount}")
            
            result = balance_manager.recharge_balance(
                username=username,
                amount=amount,
                description='GUI充值测试',
                recharge_type='manual'
            )
            
            print(f"充值结果: {result}")
            return result
        
        # 创建GUI应用
        print("\n3. 启动充值界面...")
        
        root = tk.Tk()
        root.title("充值界面测试")
        root.geometry("400x300")
        
        # 创建测试按钮
        def show_recharge_dialog():
            dialog = RechargeDialog(
                parent=root,
                username='testuser',
                current_balance=current_balance,
                recharge_callback=recharge_callback
            )
            
            result = dialog.show()
            
            if result:
                print(f"\n✓ 充值完成: {result}")
                
                # 更新当前余额显示
                new_balance = balance_manager.get_user_balance('testuser')
                balance_label.config(text=f"当前余额: ¥{float(new_balance):.2f}")
                
                # 显示充值历史
                history = balance_manager.get_recharge_history('testuser', 5)
                print(f"最近充值记录:")
                for record in history:
                    print(f"  - {record['recharge_time']}: +¥{record['amount']:.2f} ({record['description']})")
            else:
                print("\n用户取消了充值")
        
        # 创建界面元素
        tk.Label(root, text="充值界面测试", font=('Arial', 16, 'bold')).pack(pady=20)
        
        balance_label = tk.Label(root, text=f"当前余额: ¥{current_balance:.2f}", 
                               font=('Arial', 12), fg='green')
        balance_label.pack(pady=10)
        
        tk.Button(root, text="打开充值对话框", command=show_recharge_dialog,
                 font=('Arial', 12), bg='#007bff', fg='white', 
                 padx=20, pady=10).pack(pady=20)
        
        # 显示用户信息
        info_frame = tk.Frame(root)
        info_frame.pack(pady=20)
        
        tk.Label(info_frame, text="测试用户信息:", font=('Arial', 10, 'bold')).pack()
        tk.Label(info_frame, text=f"用户名: testuser").pack()
        tk.Label(info_frame, text=f"邮箱: <EMAIL>").pack()
        
        # 退出按钮
        tk.Button(root, text="退出测试", command=root.quit,
                 font=('Arial', 10), padx=15, pady=5).pack(pady=10)
        
        print("✓ 充值界面已启动")
        print("\n请在GUI中测试充值功能...")
        print("测试建议:")
        print("- 尝试选择预设金额")
        print("- 尝试输入自定义金额")
        print("- 测试金额验证功能")
        print("- 查看充值历史")
        print("- 测试充值确认流程")
        
        # 启动GUI
        root.mainloop()
        
        # 显示最终结果
        print("\n4. 测试完成，显示最终状态...")
        
        final_balance = balance_manager.get_user_balance('testuser')
        print(f"✓ 最终余额: ¥{float(final_balance):.2f}")
        
        # 显示完整的充值历史
        history = balance_manager.get_recharge_history('testuser')
        print(f"✓ 充值历史 ({len(history)} 条记录):")
        for record in history:
            print(f"  - {record['recharge_time']}: +¥{record['amount']:.2f} "
                  f"[{record['recharge_type']}] ({record['description']})")
        
        # 显示余额汇总
        summary = balance_manager.get_balance_summary('testuser')
        if summary['success']:
            print(f"✓ 余额汇总:")
            print(f"  - 总充值: ¥{summary['total_recharged']:.2f}")
            print(f"  - 总消费: ¥{summary['total_consumed']:.2f}")
            print(f"  - 当前余额: ¥{summary['current_balance']:.2f}")
        
        print("\n" + "=" * 60)
        print("✅ 充值界面测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试数据库
        try:
            if os.path.exists(test_db_path):
                import time
                time.sleep(0.1)
                os.remove(test_db_path)
                print("\n已清理测试数据库")
        except PermissionError:
            print(f"\n注意: 无法删除测试数据库 {test_db_path}，请手动删除")


def main():
    """主函数"""
    print("开始充值界面测试...")
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 运行测试
    success = test_recharge_interface()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
#!/usr/bin/env python3
"""
修复数据库结构 - 重建users表
"""

import os
import sys
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def fix_database_structure():
    """修复数据库结构"""
    print("=" * 60)
    print("修复数据库结构 - 重建users表")
    print("=" * 60)
    
    db_path = "data/auth.db"
    
    if not os.path.exists(db_path):
        print(f"✗ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 备份原数据库
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✓ 已备份数据库到: {backup_path}")
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 1. 检查现有数据
            print("\n1. 检查现有用户数据...")
            
            cursor.execute("SELECT * FROM users")
            existing_users = cursor.fetchall()
            
            # 获取列名
            cursor.execute("PRAGMA table_info(users)")
            old_columns = cursor.fetchall()
            old_column_names = [col[1] for col in old_columns]
            
            print(f"现有用户数量: {len(existing_users)}")
            print(f"现有列: {old_column_names}")
            
            # 2. 创建新的users表结构
            print("\n2. 创建新的users表结构...")
            
            # 重命名旧表
            cursor.execute("ALTER TABLE users RENAME TO users_old")
            print("✓ 已重命名旧表为 users_old")
            
            # 创建新表
            cursor.execute("""
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) NOT NULL UNIQUE,
                    password_hash VARCHAR(255) NOT NULL,
                    salt VARCHAR(32),
                    balance DECIMAL(10,2) DEFAULT 0.00,
                    email VARCHAR(255),
                    registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_login DATETIME,
                    status VARCHAR(20) DEFAULT 'active',
                    is_active BOOLEAN DEFAULT 1,
                    failed_attempts INTEGER DEFAULT 0,
                    locked_until DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✓ 已创建新的users表")
            
            # 3. 迁移数据
            print("\n3. 迁移现有用户数据...")
            
            if existing_users:
                # 构建插入语句
                current_time = datetime.now().isoformat()
                
                for user_data in existing_users:
                    # 创建用户数据字典
                    user_dict = dict(zip(old_column_names, user_data))
                    
                    # 准备新表的数据
                    new_user_data = {
                        'username': user_dict.get('username'),
                        'password_hash': user_dict.get('password_hash'),
                        'salt': user_dict.get('salt'),
                        'balance': user_dict.get('balance', 0.00),
                        'email': user_dict.get('email'),
                        'registration_date': user_dict.get('created_at', current_time),
                        'last_login': user_dict.get('last_login'),
                        'status': user_dict.get('status', 'active'),
                        'is_active': user_dict.get('is_active', 1),
                        'failed_attempts': user_dict.get('failed_attempts', 0),
                        'locked_until': user_dict.get('locked_until'),
                        'created_at': user_dict.get('created_at', current_time),
                        'updated_at': current_time
                    }
                    
                    # 插入数据
                    cursor.execute("""
                        INSERT INTO users (
                            username, password_hash, salt, balance, email,
                            registration_date, last_login, status, is_active,
                            failed_attempts, locked_until, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        new_user_data['username'],
                        new_user_data['password_hash'],
                        new_user_data['salt'],
                        new_user_data['balance'],
                        new_user_data['email'],
                        new_user_data['registration_date'],
                        new_user_data['last_login'],
                        new_user_data['status'],
                        new_user_data['is_active'],
                        new_user_data['failed_attempts'],
                        new_user_data['locked_until'],
                        new_user_data['created_at'],
                        new_user_data['updated_at']
                    ))
                    
                    print(f"✓ 已迁移用户: {new_user_data['username']}")
                
                print(f"✓ 已迁移 {len(existing_users)} 个用户")
            else:
                print("没有现有用户数据需要迁移")
            
            # 4. 创建索引
            print("\n4. 创建索引...")
            
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
            
            print("✓ 已创建索引")
            
            # 5. 删除旧表
            print("\n5. 清理旧表...")
            
            cursor.execute("DROP TABLE users_old")
            print("✓ 已删除旧表")
            
            # 提交更改
            conn.commit()
            
            # 6. 验证新表结构
            print("\n6. 验证新表结构...")
            
            cursor.execute("PRAGMA table_info(users)")
            new_columns = cursor.fetchall()
            
            print("新users表字段:")
            for col in new_columns:
                print(f"  - {col[1]} ({col[2]})")
            
            # 验证数据
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"✓ 用户数据验证: {user_count} 个用户")
        
        print("\n" + "=" * 60)
        print("✅ 数据库结构修复完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 尝试恢复备份
        if os.path.exists(backup_path):
            try:
                shutil.copy2(backup_path, db_path)
                print(f"已恢复数据库备份")
            except Exception as restore_e:
                print(f"恢复备份失败: {restore_e}")
        
        return False


def test_registration_after_fix():
    """修复后测试注册功能"""
    print("\n" + "=" * 60)
    print("测试修复后的注册功能")
    print("=" * 60)
    
    try:
        from server.database_manager import DatabaseManager
        from server.user_registration_manager import UserRegistrationManager
        
        # 初始化组件
        db_manager = DatabaseManager("data/auth.db")
        registration_manager = UserRegistrationManager(db_manager)
        
        # 测试注册
        test_username = "structure_test_user"
        
        # 清理可能存在的测试用户
        try:
            existing_user = db_manager.get_user_info(test_username)
            if existing_user:
                db_manager.delete_user(test_username)
                print(f"已清理现有测试用户: {test_username}")
        except Exception as e:
            print(f"清理测试用户时出错: {e}")
        
        # 执行注册
        result = registration_manager.register_user(
            username=test_username,
            password="testpass123",
            confirm_password="testpass123",
            email="<EMAIL>"
        )
        
        if result['success']:
            print(f"✓ 注册测试成功: {result['message']}")
            
            # 验证用户信息
            user_info = db_manager.get_user_info(test_username)
            if user_info:
                print(f"✓ 用户信息: {user_info}")
            
            # 清理测试用户
            db_manager.delete_user(test_username)
            print(f"✓ 已清理测试用户")
            
            return True
        else:
            print(f"✗ 注册测试失败: {result['message']}")
            if 'errors' in result:
                for error in result['errors']:
                    print(f"    - {error}")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始修复数据库结构...")
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 修复数据库结构
    fix_success = fix_database_structure()
    
    if fix_success:
        # 测试注册功能
        test_success = test_registration_after_fix()
        
        if test_success:
            print("\n🎉 数据库结构修复成功，注册功能正常！")
            print("\n现在您可以正常使用注册功能了。")
            print("请重新启动应用程序并尝试注册。")
        else:
            print("\n⚠️ 数据库结构修复完成，但注册功能仍有问题。")
    else:
        print("\n❌ 数据库结构修复失败。")
    
    return 0 if fix_success else 1


if __name__ == "__main__":
    sys.exit(main())
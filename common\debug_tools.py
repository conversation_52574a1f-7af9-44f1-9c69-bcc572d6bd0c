"""
调试工具模块
提供系统调试、错误诊断和问题排查功能
"""

import sys
import traceback
import inspect
import threading
import time
import json
import os
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from collections import defaultdict, deque
import functools


class DebugLogger:
    """调试日志记录器"""
    
    def __init__(self, max_entries: int = 1000):
        """
        初始化调试日志记录器
        
        Args:
            max_entries: 最大日志条目数
        """
        self.max_entries = max_entries
        self.debug_entries = deque(maxlen=max_entries)
        self.error_entries = deque(maxlen=max_entries)
        self.function_calls = deque(maxlen=max_entries)
        self._lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_debug_entries': 0,
            'total_errors': 0,
            'total_function_calls': 0,
            'start_time': datetime.now()
        }
    
    def debug(self, message: str, context: Dict[str, Any] = None):
        """
        记录调试信息
        
        Args:
            message: 调试消息
            context: 上下文信息
        """
        entry = {
            'timestamp': datetime.now(),
            'level': 'DEBUG',
            'message': message,
            'context': context or {},
            'thread_id': threading.get_ident(),
            'caller': self._get_caller_info()
        }
        
        with self._lock:
            self.debug_entries.append(entry)
            self.stats['total_debug_entries'] += 1
    
    def error(self, message: str, exception: Exception = None, context: Dict[str, Any] = None):
        """
        记录错误信息
        
        Args:
            message: 错误消息
            exception: 异常对象
            context: 上下文信息
        """
        entry = {
            'timestamp': datetime.now(),
            'level': 'ERROR',
            'message': message,
            'context': context or {},
            'thread_id': threading.get_ident(),
            'caller': self._get_caller_info(),
            'exception': {
                'type': type(exception).__name__ if exception else None,
                'message': str(exception) if exception else None,
                'traceback': traceback.format_exc() if exception else None
            }
        }
        
        with self._lock:
            self.error_entries.append(entry)
            self.stats['total_errors'] += 1
    
    def function_call(self, func_name: str, args: tuple = None, kwargs: dict = None, 
                     result: Any = None, execution_time: float = None):
        """
        记录函数调用
        
        Args:
            func_name: 函数名
            args: 位置参数
            kwargs: 关键字参数
            result: 返回结果
            execution_time: 执行时间
        """
        entry = {
            'timestamp': datetime.now(),
            'function': func_name,
            'args': str(args) if args else None,
            'kwargs': str(kwargs) if kwargs else None,
            'result_type': type(result).__name__ if result is not None else None,
            'execution_time': execution_time,
            'thread_id': threading.get_ident(),
            'caller': self._get_caller_info(depth=3)
        }
        
        with self._lock:
            self.function_calls.append(entry)
            self.stats['total_function_calls'] += 1
    
    def _get_caller_info(self, depth: int = 2) -> Dict[str, Any]:
        """
        获取调用者信息
        
        Args:
            depth: 调用栈深度
            
        Returns:
            Dict[str, Any]: 调用者信息
        """
        try:
            frame = inspect.currentframe()
            for _ in range(depth):
                frame = frame.f_back
                if not frame:
                    break
            
            if frame:
                return {
                    'filename': frame.f_code.co_filename,
                    'function': frame.f_code.co_name,
                    'line_number': frame.f_lineno
                }
        except:
            pass
        
        return {'filename': 'unknown', 'function': 'unknown', 'line_number': 0}
    
    def get_recent_entries(self, entry_type: str = 'all', limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取最近的日志条目
        
        Args:
            entry_type: 条目类型 ('debug', 'error', 'function', 'all')
            limit: 返回数量限制
            
        Returns:
            List[Dict[str, Any]]: 日志条目列表
        """
        with self._lock:
            entries = []
            
            if entry_type in ('debug', 'all'):
                entries.extend(list(self.debug_entries)[-limit:])
            
            if entry_type in ('error', 'all'):
                entries.extend(list(self.error_entries)[-limit:])
            
            if entry_type in ('function', 'all'):
                entries.extend(list(self.function_calls)[-limit:])
            
            # 按时间排序
            entries.sort(key=lambda x: x['timestamp'], reverse=True)
            
            return entries[:limit]
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取调试统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            stats = self.stats.copy()
            stats['uptime'] = (datetime.now() - stats['start_time']).total_seconds()
            
            # 错误统计
            error_types = defaultdict(int)
            for entry in self.error_entries:
                if entry['exception']['type']:
                    error_types[entry['exception']['type']] += 1
            
            stats['error_types'] = dict(error_types)
            
            # 函数调用统计
            function_stats = defaultdict(lambda: {'count': 0, 'total_time': 0})
            for entry in self.function_calls:
                func_name = entry['function']
                function_stats[func_name]['count'] += 1
                if entry['execution_time']:
                    function_stats[func_name]['total_time'] += entry['execution_time']
            
            # 计算平均执行时间
            for func_name, stats_data in function_stats.items():
                if stats_data['count'] > 0:
                    stats_data['avg_time'] = stats_data['total_time'] / stats_data['count']
            
            stats['function_stats'] = dict(function_stats)
            
            return stats
    
    def export_debug_data(self, filename: str) -> bool:
        """
        导出调试数据
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 导出成功返回True
        """
        try:
            data = {
                'export_time': datetime.now().isoformat(),
                'statistics': self.get_statistics(),
                'debug_entries': [
                    {**entry, 'timestamp': entry['timestamp'].isoformat()}
                    for entry in self.debug_entries
                ],
                'error_entries': [
                    {**entry, 'timestamp': entry['timestamp'].isoformat()}
                    for entry in self.error_entries
                ],
                'function_calls': [
                    {**entry, 'timestamp': entry['timestamp'].isoformat()}
                    for entry in self.function_calls
                ]
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            
            return True
            
        except Exception as e:
            self.error(f"导出调试数据失败: {e}", e)
            return False


class SystemDiagnostics:
    """系统诊断工具"""
    
    def __init__(self):
        """初始化系统诊断工具"""
        self.diagnostic_tests = {}
        self._register_default_tests()
    
    def _register_default_tests(self):
        """注册默认诊断测试"""
        self.register_test('memory_usage', self._test_memory_usage)
        self.register_test('disk_space', self._test_disk_space)
        self.register_test('network_connectivity', self._test_network_connectivity)
        self.register_test('database_connection', self._test_database_connection)
        self.register_test('file_permissions', self._test_file_permissions)
    
    def register_test(self, name: str, test_func: Callable):
        """
        注册诊断测试
        
        Args:
            name: 测试名称
            test_func: 测试函数
        """
        self.diagnostic_tests[name] = test_func
    
    def run_diagnostics(self, tests: List[str] = None) -> Dict[str, Any]:
        """
        运行诊断测试
        
        Args:
            tests: 要运行的测试列表，None表示运行所有测试
            
        Returns:
            Dict[str, Any]: 诊断结果
        """
        if tests is None:
            tests = list(self.diagnostic_tests.keys())
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'tests_run': len(tests),
            'results': {},
            'summary': {'passed': 0, 'failed': 0, 'warnings': 0}
        }
        
        for test_name in tests:
            if test_name in self.diagnostic_tests:
                try:
                    test_result = self.diagnostic_tests[test_name]()
                    results['results'][test_name] = test_result
                    
                    # 更新摘要
                    status = test_result.get('status', 'unknown')
                    if status == 'pass':
                        results['summary']['passed'] += 1
                    elif status == 'fail':
                        results['summary']['failed'] += 1
                    elif status == 'warning':
                        results['summary']['warnings'] += 1
                        
                except Exception as e:
                    results['results'][test_name] = {
                        'status': 'error',
                        'message': f'测试执行失败: {str(e)}',
                        'exception': str(e)
                    }
                    results['summary']['failed'] += 1
        
        return results
    
    def _test_memory_usage(self) -> Dict[str, Any]:
        """测试内存使用情况"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            
            if memory.percent > 90:
                status = 'fail'
                message = f'内存使用率过高: {memory.percent:.1f}%'
            elif memory.percent > 80:
                status = 'warning'
                message = f'内存使用率较高: {memory.percent:.1f}%'
            else:
                status = 'pass'
                message = f'内存使用率正常: {memory.percent:.1f}%'
            
            return {
                'status': status,
                'message': message,
                'details': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used,
                    'free': memory.free
                }
            }
            
        except ImportError:
            return {
                'status': 'warning',
                'message': '无法检测内存使用情况，psutil模块未安装'
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'内存检测失败: {str(e)}'
            }
    
    def _test_disk_space(self) -> Dict[str, Any]:
        """测试磁盘空间"""
        try:
            import shutil
            total, used, free = shutil.disk_usage('.')
            
            usage_percent = (used / total) * 100
            
            if usage_percent > 95:
                status = 'fail'
                message = f'磁盘空间不足: {usage_percent:.1f}%已使用'
            elif usage_percent > 85:
                status = 'warning'
                message = f'磁盘空间较少: {usage_percent:.1f}%已使用'
            else:
                status = 'pass'
                message = f'磁盘空间充足: {usage_percent:.1f}%已使用'
            
            return {
                'status': status,
                'message': message,
                'details': {
                    'total': total,
                    'used': used,
                    'free': free,
                    'usage_percent': usage_percent
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'磁盘空间检测失败: {str(e)}'
            }
    
    def _test_network_connectivity(self) -> Dict[str, Any]:
        """测试网络连接"""
        try:
            import socket
            
            # 测试本地回环
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            
            try:
                result = sock.connect_ex(('127.0.0.1', 80))
                if result == 0:
                    status = 'pass'
                    message = '网络连接正常'
                else:
                    status = 'warning'
                    message = '本地网络连接可能有问题'
            finally:
                sock.close()
            
            return {
                'status': status,
                'message': message,
                'details': {
                    'test_target': '127.0.0.1:80',
                    'connection_result': result
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'网络连接测试失败: {str(e)}'
            }
    
    def _test_database_connection(self) -> Dict[str, Any]:
        """测试数据库连接"""
        try:
            # 这里需要根据实际的数据库管理器进行测试
            # 暂时返回一个基本的测试结果
            return {
                'status': 'pass',
                'message': '数据库连接测试需要具体实现',
                'details': {
                    'note': '请在实际使用时连接具体的数据库管理器'
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'数据库连接测试失败: {str(e)}'
            }
    
    def _test_file_permissions(self) -> Dict[str, Any]:
        """测试文件权限"""
        try:
            test_dirs = ['logs', 'data', 'config']
            results = {}
            
            for dir_name in test_dirs:
                if os.path.exists(dir_name):
                    readable = os.access(dir_name, os.R_OK)
                    writable = os.access(dir_name, os.W_OK)
                    results[dir_name] = {
                        'readable': readable,
                        'writable': writable,
                        'exists': True
                    }
                else:
                    results[dir_name] = {
                        'readable': False,
                        'writable': False,
                        'exists': False
                    }
            
            # 检查是否有权限问题
            permission_issues = []
            for dir_name, perms in results.items():
                if perms['exists'] and not (perms['readable'] and perms['writable']):
                    permission_issues.append(dir_name)
            
            if permission_issues:
                status = 'warning'
                message = f'以下目录权限不足: {", ".join(permission_issues)}'
            else:
                status = 'pass'
                message = '文件权限检查通过'
            
            return {
                'status': status,
                'message': message,
                'details': results
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'文件权限检测失败: {str(e)}'
            }


class DebugDecorator:
    """调试装饰器"""
    
    def __init__(self, debug_logger: DebugLogger):
        """
        初始化调试装饰器
        
        Args:
            debug_logger: 调试日志记录器
        """
        self.debug_logger = debug_logger
    
    def trace_calls(self, include_args: bool = True, include_result: bool = False):
        """
        函数调用跟踪装饰器
        
        Args:
            include_args: 是否包含参数
            include_result: 是否包含返回结果
        """
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                func_name = f"{func.__module__}.{func.__name__}"
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    self.debug_logger.function_call(
                        func_name=func_name,
                        args=args if include_args else None,
                        kwargs=kwargs if include_args else None,
                        result=result if include_result else None,
                        execution_time=execution_time
                    )
                    
                    return result
                    
                except Exception as e:
                    execution_time = time.time() - start_time
                    
                    self.debug_logger.error(
                        f"函数 {func_name} 执行失败",
                        exception=e,
                        context={
                            'args': str(args) if include_args else None,
                            'kwargs': str(kwargs) if include_args else None,
                            'execution_time': execution_time
                        }
                    )
                    
                    raise
            
            return wrapper
        return decorator
    
    def debug_on_error(self, message: str = None):
        """
        错误时调试装饰器
        
        Args:
            message: 自定义错误消息
        """
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    error_msg = message or f"函数 {func.__name__} 发生错误"
                    
                    self.debug_logger.error(
                        error_msg,
                        exception=e,
                        context={
                            'function': func.__name__,
                            'args': str(args),
                            'kwargs': str(kwargs)
                        }
                    )
                    
                    raise
            
            return wrapper
        return decorator


# 全局调试实例
debug_logger = DebugLogger()
system_diagnostics = SystemDiagnostics()
debug_decorator = DebugDecorator(debug_logger)

# 便捷函数
def debug(message: str, context: Dict[str, Any] = None):
    """记录调试信息"""
    debug_logger.debug(message, context)

def error(message: str, exception: Exception = None, context: Dict[str, Any] = None):
    """记录错误信息"""
    debug_logger.error(message, exception, context)

def trace_calls(include_args: bool = True, include_result: bool = False):
    """函数调用跟踪装饰器"""
    return debug_decorator.trace_calls(include_args, include_result)

def debug_on_error(message: str = None):
    """错误时调试装饰器"""
    return debug_decorator.debug_on_error(message)

def run_system_diagnostics(tests: List[str] = None) -> Dict[str, Any]:
    """运行系统诊断"""
    return system_diagnostics.run_diagnostics(tests)
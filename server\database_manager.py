"""
数据库管理器模块
实现SQLite数据库的连接管理、表创建和基础操作
"""

import sqlite3
import threading
import os
from typing import Dict, Any, Optional, List
from contextlib import contextmanager
from datetime import datetime

from common.interfaces import IDatabaseManager
from common.exceptions import DatabaseError


class DatabaseManager(IDatabaseManager):
    """SQLite数据库管理器"""
    
    def __init__(self, db_path: str = "data/auth.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self._lock = threading.Lock()
        self._ensure_directory_exists()
        # 不再自动创建表，依赖迁移系统
        # self.create_tables()
        self._run_migrations()
    
    def _ensure_directory_exists(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
    
    def _run_migrations(self):
        """运行数据库迁移"""
        try:
            from server.database_migrations import DatabaseMigration
            migration = DatabaseMigration(self.db_path)
            
            version_info = migration.check_database_version()
            if version_info['needs_migration']:
                print("检测到数据库需要迁移，正在执行...")
                success = migration.run_migrations()
                if not success:
                    print("数据库迁移失败")
                    raise Exception("数据库迁移失败")
            
        except ImportError:
            print("警告: 无法导入数据库迁移模块，使用旧版表结构")
            self.create_tables()
        except Exception as e:
            print(f"数据库迁移过程中出错: {e}")
            # 如果迁移失败，尝试使用旧版表结构
            self.create_tables()
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器
        确保连接的正确关闭和异常处理
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path, check_same_thread=False)
            conn.row_factory = sqlite3.Row  # 使查询结果可以像字典一样访问
            yield conn
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise DatabaseError(f"数据库操作失败: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def create_tables(self) -> None:
        """创建数据库表"""
        with self._lock:
            with self.get_connection() as conn:
                try:
                    # 创建用户表
                    conn.execute("""
                        CREATE TABLE IF NOT EXISTS users (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            username VARCHAR(50) UNIQUE NOT NULL,
                            password_hash VARCHAR(64) NOT NULL,
                            salt VARCHAR(32) NOT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            last_login TIMESTAMP,
                            is_active BOOLEAN DEFAULT 1,
                            failed_attempts INTEGER DEFAULT 0,
                            locked_until TIMESTAMP NULL
                        )
                    """)
                    
                    # 创建会话表
                    conn.execute("""
                        CREATE TABLE IF NOT EXISTS sessions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            user_id INTEGER NOT NULL,
                            session_token VARCHAR(64) UNIQUE NOT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            expires_at TIMESTAMP NOT NULL,
                            client_ip VARCHAR(45),
                            FOREIGN KEY (user_id) REFERENCES users (id)
                        )
                    """)
                    
                    # 创建日志表
                    conn.execute("""
                        CREATE TABLE IF NOT EXISTS logs (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            level VARCHAR(10) NOT NULL,
                            message TEXT NOT NULL,
                            client_ip VARCHAR(45),
                            username VARCHAR(50)
                        )
                    """)
                    
                    # 创建索引以提高查询性能
                    conn.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
                    conn.execute("CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(session_token)")
                    conn.execute("CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)")
                    conn.execute("CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp)")
                    
                    conn.commit()
                    
                except sqlite3.Error as e:
                    raise DatabaseError(f"创建数据库表失败: {str(e)}")
    
    def add_user(self, username: str, password_hash: str, salt: str) -> bool:
        """
        添加新用户
        
        Args:
            username: 用户名
            password_hash: 密码哈希值
            salt: 盐值
            
        Returns:
            bool: 添加成功返回True，否则返回False
        """
        with self._lock:
            with self.get_connection() as conn:
                try:
                    conn.execute("""
                        INSERT INTO users (username, password_hash, salt)
                        VALUES (?, ?, ?)
                    """, (username, password_hash, salt))
                    conn.commit()
                    return True
                except sqlite3.IntegrityError:
                    # 用户名已存在
                    return False
                except sqlite3.Error as e:
                    raise DatabaseError(f"添加用户失败: {str(e)}")
    
    def get_user(self, username: str) -> Optional[Dict[str, Any]]:
        """
        获取用户信息
        
        Args:
            username: 用户名
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息字典，不存在则返回None
        """
        with self._lock:
            with self.get_connection() as conn:
                try:
                    cursor = conn.execute("""
                        SELECT id, username, password_hash, salt, created_at,
                               last_login, is_active, failed_attempts, locked_until
                        FROM users WHERE username = ?
                    """, (username,))
                    
                    row = cursor.fetchone()
                    if row:
                        return dict(row)
                    return None
                    
                except sqlite3.Error as e:
                    raise DatabaseError(f"查询用户失败: {str(e)}")
    
    def delete_user(self, username: str) -> bool:
        """
        删除用户
        
        Args:
            username: 用户名
            
        Returns:
            bool: 删除成功返回True，用户不存在返回False
        """
        with self._lock:
            with self.get_connection() as conn:
                try:
                    # 先删除相关的会话记录
                    conn.execute("""
                        DELETE FROM sessions 
                        WHERE user_id = (SELECT id FROM users WHERE username = ?)
                    """, (username,))
                    
                    # 删除用户
                    cursor = conn.execute("DELETE FROM users WHERE username = ?", (username,))
                    conn.commit()
                    
                    return cursor.rowcount > 0
                    
                except sqlite3.Error as e:
                    raise DatabaseError(f"删除用户失败: {str(e)}")
    
    def update_user(self, username: str, **kwargs) -> bool:
        """
        更新用户信息
        
        Args:
            username: 用户名
            **kwargs: 要更新的字段和值
            
        Returns:
            bool: 更新成功返回True，用户不存在返回False
        """
        if not kwargs:
            return False
            
        # 构建更新语句
        valid_fields = {
            'password_hash', 'salt', 'last_login', 'is_active', 
            'failed_attempts', 'locked_until'
        }
        
        update_fields = {k: v for k, v in kwargs.items() if k in valid_fields}
        if not update_fields:
            return False
        
        set_clause = ", ".join([f"{field} = ?" for field in update_fields.keys()])
        values = list(update_fields.values()) + [username]
        
        with self._lock:
            with self.get_connection() as conn:
                try:
                    cursor = conn.execute(
                        f"UPDATE users SET {set_clause} WHERE username = ?",
                        values
                    )
                    conn.commit()
                    
                    return cursor.rowcount > 0
                    
                except sqlite3.Error as e:
                    raise DatabaseError(f"更新用户失败: {str(e)}")
    
    def get_all_users(self, limit=100, offset=0) -> List[Dict[str, Any]]:
        """
        获取所有用户列表
        
        Args:
            limit: 限制返回数量
            offset: 偏移量
        
        Returns:
            List[Dict[str, Any]]: 用户信息列表
        """
        with self._lock:
            with self.get_connection() as conn:
                try:
                    cursor = conn.execute("""
                        SELECT id, username, created_at, last_login, 
                               is_active, failed_attempts, locked_until,
                               status, email, balance, registration_date
                        FROM users ORDER BY created_at DESC
                        LIMIT ? OFFSET ?
                    """, (limit, offset))
                    
                    return [dict(row) for row in cursor.fetchall()]
                    
                except sqlite3.Error as e:
                    raise DatabaseError(f"查询用户列表失败: {str(e)}")
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """
        执行自定义查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果列表
        """
        with self._lock:
            with self.get_connection() as conn:
                try:
                    cursor = conn.execute(query, params)
                    return [dict(row) for row in cursor.fetchall()]
                    
                except sqlite3.Error as e:
                    raise DatabaseError(f"执行查询失败: {str(e)}")
    
    def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库信息
        
        Returns:
            Dict[str, Any]: 数据库统计信息
        """
        with self._lock:
            with self.get_connection() as conn:
                try:
                    # 获取用户数量
                    user_count = conn.execute("SELECT COUNT(*) FROM users").fetchone()[0]
                    
                    # 获取活跃会话数量
                    session_count = conn.execute("""
                        SELECT COUNT(*) FROM sessions 
                        WHERE expires_at > datetime('now')
                    """).fetchone()[0]
                    
                    # 获取数据库文件大小
                    db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
                    
                    return {
                        'user_count': user_count,
                        'active_sessions': session_count,
                        'database_size': db_size,
                        'database_path': self.db_path
                    }
                    
                except sqlite3.Error as e:
                    raise DatabaseError(f"获取数据库信息失败: {str(e)}") 
   
    # 用户账号管理相关方法
    
    def get_user_info(self, username: str) -> Optional[Dict[str, Any]]:
        """
        获取用户完整信息
        
        Args:
            username: 用户名
            
        Returns:
            Dict: 用户信息，如果用户不存在返回None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT username, balance, email, registration_date, 
                           last_login, status, created_at, updated_at
                    FROM users WHERE username = ?
                """, (username,))
                
                result = cursor.fetchone()
                if result:
                    columns = ['username', 'balance', 'email', 'registration_date', 
                             'last_login', 'status', 'created_at', 'updated_at']
                    return dict(zip(columns, result))
                return None
                
        except sqlite3.Error as e:
            print(f"获取用户信息失败: {e}")
            return None
    
    def register_user(self, username: str, password_hash: str, email: str = None, 
                     initial_balance: float = 0.0) -> bool:
        """
        注册新用户
        
        Args:
            username: 用户名
            password_hash: 密码哈希
            email: 邮箱地址
            initial_balance: 初始余额
            
        Returns:
            bool: 注册成功返回True
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO users (username, password_hash, email, balance, 
                                     registration_date, status, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, 'active', 
                           CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """, (username, password_hash, email, initial_balance))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except sqlite3.IntegrityError:
            print(f"用户名 {username} 已存在")
            return False
        except sqlite3.Error as e:
            print(f"注册用户失败: {e}")
            return False
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络验证工具 - 服务器管理</title>
    
    <!-- 内联样式 -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: var(--card-background);
            border-right: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .logo-icon {
            width: 2.5rem;
            height: 2.5rem;
            background: var(--primary-color);
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
        }

        .logo-text {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .nav-menu {
            padding: 1rem 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            width: 100%;
            padding: 0.75rem 1.5rem;
            border: none;
            background: none;
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
        }

        .nav-item:hover {
            background: #f1f5f9;
            color: var(--text-primary);
        }

        .nav-item.active {
            background: #eff6ff;
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .card {
            background: var(--card-background);
            border-radius: 0.75rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            margin-bottom: 1.5rem;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-body {
            padding: 1.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--card-background);
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .stat-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.125rem;
        }

        .stat-icon.primary { background: var(--primary-color); }
        .stat-icon.success { background: var(--success-color); }
        .stat-icon.warning { background: var(--warning-color); }
        .stat-icon.danger { background: var(--danger-color); }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-change.positive { color: var(--success-color); }
        .stat-change.negative { color: var(--danger-color); }
        .stat-change.neutral { color: var(--text-secondary); }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            flex-direction: column;
            gap: 1rem;
        }

        .loading {
            width: 2rem;
            height: 2rem;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: var(--shadow-lg);
            z-index: 9999;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left: 4px solid var(--success-color);
        }

        .notification.error {
            border-left: 4px solid var(--danger-color);
        }

        .notification.info {
            border-left: 4px solid var(--primary-color);
        }
    </style>
    
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="logo-text">网络验证工具</div>
                </div>
            </div>
            
            <div class="nav-menu">
                <button class="nav-item active" data-page="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    仪表板
                </button>
                
                <button class="nav-item" data-page="users">
                    <i class="fas fa-users"></i>
                    用户管理
                </button>
                
                <button class="nav-item" data-page="sessions">
                    <i class="fas fa-user-clock"></i>
                    会话管理
                </button>
                
                <button class="nav-item" data-page="logs">
                    <i class="fas fa-file-alt"></i>
                    系统日志
                </button>
                
                <button class="nav-item" data-page="settings">
                    <i class="fas fa-cog"></i>
                    系统设置
                </button>
                
                <div style="margin-top: 2rem; padding: 0 1.5rem;">
                    <div style="border-top: 1px solid var(--border-color); padding-top: 1rem;">
                        <button class="nav-item" onclick="app.showAbout()">
                            <i class="fas fa-info-circle"></i>
                            关于
                        </button>
                        
                        <button class="nav-item" onclick="window.close()">
                            <i class="fas fa-sign-out-alt"></i>
                            退出
                        </button>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <div id="main-content">
                <!-- 页面内容将通过 JavaScript 动态加载 -->
                <div class="loading-container">
                    <div class="loading"></div>
                    <span>正在加载...</span>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 内联JavaScript -->
    <script>
        // 简化版的应用程序
        class ServerApp {
            constructor() {
                this.currentPage = 'dashboard';
                this.init();
            }

            init() {
                console.log('初始化应用程序...');
                this.setupEventListeners();
                
                // 等待 pywebview API 准备
                this.waitForAPI();
            }

            waitForAPI() {
                console.log('等待 PyWebView API...');
                
                if (typeof pywebview !== 'undefined' && pywebview.api) {
                    console.log('PyWebView API 已准备就绪');
                    this.loadPage('dashboard');
                } else {
                    let attempts = 0;
                    const checkAPI = () => {
                        attempts++;
                        console.log(`检查 API (尝试 ${attempts})`);
                        
                        if (typeof pywebview !== 'undefined' && pywebview.api) {
                            console.log('PyWebView API 现在可用');
                            this.loadPage('dashboard');
                        } else if (attempts < 20) {
                            setTimeout(checkAPI, 500);
                        } else {
                            console.error('API 加载超时');
                            this.showError('API 连接超时', '无法连接到后端服务');
                        }
                    };
                    setTimeout(checkAPI, 100);
                }
            }

            setupEventListeners() {
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        const page = item.dataset.page;
                        if (page) {
                            this.loadPage(page);
                        }
                    });
                });
            }

            async loadPage(pageName) {
                console.log(`加载页面: ${pageName}`);
                
                // 更新导航状态
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                const activeItem = document.querySelector(`[data-page="${pageName}"]`);
                if (activeItem) {
                    activeItem.classList.add('active');
                }

                this.currentPage = pageName;

                try {
                    const content = await this.getPageContent(pageName);
                    document.getElementById('main-content').innerHTML = content;
                } catch (error) {
                    console.error('加载页面失败:', error);
                    this.showError('页面加载失败', error.message);
                }
            }

            async getPageContent(pageName) {
                switch (pageName) {
                    case 'dashboard':
                        return await this.getDashboardContent();
                    case 'users':
                        return this.getUsersContent();
                    case 'sessions':
                        return this.getSessionsContent();
                    case 'logs':
                        return this.getLogsContent();
                    case 'settings':
                        return this.getSettingsContent();
                    default:
                        return '<div class="card"><div class="card-body"><h3>页面未找到</h3></div></div>';
                }
            }

            async getDashboardContent() {
                const stats = await this.fetchServerStats();
                
                return `
                    <div class="page-header">
                        <h1 class="page-title">仪表板</h1>
                        <p class="page-subtitle">服务器状态和统计信息概览</p>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-title">服务器状态</span>
                                <div class="stat-icon primary">
                                    <i class="fas fa-server"></i>
                                </div>
                            </div>
                            <div class="stat-value">${stats.is_running ? '运行中' : '已停止'}</div>
                            <div class="stat-change ${stats.is_running ? 'positive' : 'negative'}">
                                ${stats.is_running ? '✓ 正常运行' : '✗ 服务停止'}
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-title">总连接数</span>
                                <div class="stat-icon success">
                                    <i class="fas fa-plug"></i>
                                </div>
                            </div>
                            <div class="stat-value">${stats.total_connections || 0}</div>
                            <div class="stat-change neutral">累计连接</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-title">成功认证</span>
                                <div class="stat-icon success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                            <div class="stat-value">${stats.successful_auths || 0}</div>
                            <div class="stat-change positive">认证成功</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-title">活跃会话</span>
                                <div class="stat-icon warning">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="stat-value">${stats.active_sessions || 0}</div>
                            <div class="stat-change neutral">当前在线</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">服务器控制</h3>
                        </div>
                        <div class="card-body">
                            <div style="display: flex; gap: 1rem; margin-bottom: 1rem;">
                                <button class="btn btn-success" onclick="app.startServer()">
                                    <i class="fas fa-play"></i> 启动服务器
                                </button>
                                <button class="btn btn-danger" onclick="app.stopServer()">
                                    <i class="fas fa-stop"></i> 停止服务器
                                </button>
                                <button class="btn btn-secondary" onclick="app.refreshStats()">
                                    <i class="fas fa-sync"></i> 刷新状态
                                </button>
                            </div>
                            
                            <div class="server-info">
                                <p><strong>监听地址:</strong> ${stats.host || 'N/A'}:${stats.port || 'N/A'}</p>
                                <p><strong>版本:</strong> ${stats.server_version || '1.0.0'}</p>
                            </div>
                        </div>
                    </div>
                `;
            }

            async getUsersContent() {
                const users = await this.fetchUsers();
                const stats = await this.fetchRegistrationStats();
                
                return `
                    <div class="page-header">
                        <h1 class="page-title">用户管理</h1>
                        <p class="page-subtitle">管理系统用户账户和注册</p>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-title">总用户数</span>
                                <div class="stat-icon primary">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="stat-value">${stats.total_users || 0}</div>
                            <div class="stat-change neutral">注册用户</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-title">活跃用户</span>
                                <div class="stat-icon success">
                                    <i class="fas fa-user-check"></i>
                                </div>
                            </div>
                            <div class="stat-value">${stats.active_users || 0}</div>
                            <div class="stat-change positive">正常状态</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-title">今日注册</span>
                                <div class="stat-icon warning">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                            </div>
                            <div class="stat-value">${this.getTodayRegistrations(stats.registration_by_date)}</div>
                            <div class="stat-change neutral">新用户</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">用户注册</h3>
                        </div>
                        <div class="card-body">
                            <div style="display: flex; gap: 1rem; margin-bottom: 1rem;">
                                <button class="btn btn-primary" onclick="app.showRegisterDialog()">
                                    <i class="fas fa-user-plus"></i> 注册新用户
                                </button>
                                <button class="btn btn-secondary" onclick="app.refreshUsers()">
                                    <i class="fas fa-sync"></i> 刷新列表
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">用户列表</h3>
                        </div>
                        <div class="card-body">
                            ${this.renderUserTable(users)}
                        </div>
                    </div>
                `;
            }

            getSessionsContent() {
                return `
                    <div class="page-header">
                        <h1 class="page-title">会话管理</h1>
                        <p class="page-subtitle">查看和管理用户会话</p>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <p>会话管理功能正在开发中...</p>
                        </div>
                    </div>
                `;
            }

            getLogsContent() {
                return `
                    <div class="page-header">
                        <h1 class="page-title">系统日志</h1>
                        <p class="page-subtitle">查看服务器运行日志</p>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <p>日志查看功能正在开发中...</p>
                        </div>
                    </div>
                `;
            }

            getSettingsContent() {
                return `
                    <div class="page-header">
                        <h1 class="page-title">系统设置</h1>
                        <p class="page-subtitle">配置服务器参数</p>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <p>系统设置功能正在开发中...</p>
                        </div>
                    </div>
                `;
            }

            async fetchServerStats() {
                try {
                    if (!pywebview || !pywebview.api) {
                        throw new Error('PyWebView API 不可用');
                    }
                    
                    console.log('获取服务器统计信息...');
                    const response = await pywebview.api.get_server_stats();
                    console.log('服务器统计信息:', response);
                    return response;
                } catch (error) {
                    console.error('获取服务器统计失败:', error);
                    return {
                        is_running: false,
                        total_connections: 0,
                        successful_auths: 0,
                        failed_auths: 0,
                        active_sessions: 0,
                        uptime_seconds: 0,
                        host: 'N/A',
                        port: 'N/A',
                        server_version: '1.0.0',
                        error: error.message
                    };
                }
            }

            async startServer() {
                try {
                    const result = await pywebview.api.start_server();
                    if (result.success) {
                        this.showNotification('服务器启动成功', 'success');
                        this.refreshStats();
                    } else {
                        this.showNotification('服务器启动失败: ' + result.message, 'error');
                    }
                } catch (error) {
                    this.showNotification('启动服务器时发生错误', 'error');
                }
            }

            async stopServer() {
                try {
                    const result = await pywebview.api.stop_server();
                    if (result.success) {
                        this.showNotification('服务器已停止', 'success');
                        this.refreshStats();
                    } else {
                        this.showNotification('停止服务器失败: ' + result.message, 'error');
                    }
                } catch (error) {
                    this.showNotification('停止服务器时发生错误', 'error');
                }
            }

            async refreshStats() {
                if (this.currentPage === 'dashboard') {
                    await this.loadPage('dashboard');
                }
            }

            showError(title, message) {
                document.getElementById('main-content').innerHTML = `
                    <div class="card">
                        <div class="card-body" style="text-align: center; padding: 3rem;">
                            <div style="font-size: 3rem; margin-bottom: 1rem; color: var(--danger-color);">⚠️</div>
                            <h2 style="color: var(--danger-color); margin-bottom: 1rem;">${title}</h2>
                            <p style="color: var(--text-secondary); margin-bottom: 2rem;">${message}</p>
                            <button class="btn btn-primary" onclick="location.reload()">
                                <i class="fas fa-redo"></i> 重新加载
                            </button>
                        </div>
                    </div>
                `;
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                        <span>${message}</span>
                    </div>
                `;
                
                document.body.appendChild(notification);
                
                setTimeout(() => notification.classList.add('show'), 100);
                
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }

            async fetchUsers() {
                try {
                    if (!pywebview || !pywebview.api) {
                        return [];
                    }
                    
                    const response = await pywebview.api.get_users();
                    return response || [];
                } catch (error) {
                    console.error('获取用户列表失败:', error);
                    return [];
                }
            }

            async fetchRegistrationStats() {
                try {
                    if (!pywebview || !pywebview.api) {
                        return {};
                    }
                    
                    const response = await pywebview.api.get_registration_statistics();
                    return response.success ? response.data : {};
                } catch (error) {
                    console.error('获取注册统计失败:', error);
                    return {};
                }
            }

            getTodayRegistrations(registrationByDate) {
                const today = new Date().toISOString().split('T')[0];
                return registrationByDate[today] || 0;
            }

            renderUserTable(users) {
                if (!users || users.length === 0) {
                    return '<p>暂无用户数据</p>';
                }

                let tableHtml = `
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: var(--background-color);">
                                <th style="padding: 0.75rem; text-align: left; border-bottom: 1px solid var(--border-color);">用户名</th>
                                <th style="padding: 0.75rem; text-align: left; border-bottom: 1px solid var(--border-color);">邮箱</th>
                                <th style="padding: 0.75rem; text-align: left; border-bottom: 1px solid var(--border-color);">余额</th>
                                <th style="padding: 0.75rem; text-align: left; border-bottom: 1px solid var(--border-color);">注册时间</th>
                                <th style="padding: 0.75rem; text-align: left; border-bottom: 1px solid var(--border-color);">状态</th>
                                <th style="padding: 0.75rem; text-align: left; border-bottom: 1px solid var(--border-color);">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                users.forEach(user => {
                    const statusColor = user.status === 'active' ? 'var(--success-color)' : 'var(--danger-color)';
                    tableHtml += `
                        <tr>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">${user.username || 'N/A'}</td>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">${user.email || '未设置'}</td>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">¥${(user.balance || 0).toFixed(2)}</td>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">${user.registration_date || user.created_at || 'N/A'}</td>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <span style="color: ${statusColor}; font-weight: 500;">
                                    ${user.status === 'active' ? '正常' : '禁用'}
                                </span>
                            </td>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <button class="btn btn-secondary" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;" onclick="app.editUser('${user.username}')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                            </td>
                        </tr>
                    `;
                });

                tableHtml += '</tbody></table>';
                return tableHtml;
            }

            async showRegisterDialog() {
                const username = prompt('请输入用户名:');
                if (!username) return;

                const password = prompt('请输入密码:');
                if (!password) return;

                const email = prompt('请输入邮箱 (可选):');

                try {
                    const result = await pywebview.api.register_user(username, password, email || null);
                    
                    if (result.success) {
                        this.showNotification(`用户 ${username} 注册成功`, 'success');
                        this.refreshUsers();
                    } else {
                        this.showNotification(`注册失败: ${result.message}`, 'error');
                    }
                } catch (error) {
                    this.showNotification('注册时发生错误', 'error');
                }
            }

            async refreshUsers() {
                if (this.currentPage === 'users') {
                    await this.loadPage('users');
                }
            }

            editUser(username) {
                this.showNotification(`编辑用户 ${username} 功能开发中`, 'info');
            }

            showAbout() {
                this.showNotification('网络验证工具 v1.0.0', 'info');
            }
        }

        // 初始化应用
        let app;

        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM 已加载完成');
            app = new ServerApp();
        });

        window.addEventListener('pywebviewready', () => {
            console.log('PyWebView ready 事件触发');
            if (!app) {
                app = new ServerApp();
            }
        });
    </script>
</body>
</html>
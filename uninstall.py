#!/usr/bin/env python3
"""
网络验证工具卸载脚本
"""

import os
import shutil
from pathlib import Path

def uninstall():
    project_root = Path(__file__).parent
    
    print("卸载网络验证工具...")
    
    # 删除数据目录（询问用户）
    data_dir = project_root / 'data'
    if data_dir.exists():
        response = input("是否删除数据目录（包含用户数据）？(y/N): ")
        if response.lower() == 'y':
            shutil.rmtree(data_dir)
            print("√ 删除数据目录")
        else:
            print("保留数据目录")
    
    # 删除日志目录
    logs_dir = project_root / 'logs'
    if logs_dir.exists():
        shutil.rmtree(logs_dir)
        print("√ 删除日志目录")
    
    # 删除配置文件
    config_files = ['config.ini', 'client_config.ini']
    for config_file in config_files:
        config_path = project_root / config_file
        if config_path.exists():
            os.remove(config_path)
            print(f"√ 删除配置文件: {config_file}")
    
    # 删除启动脚本
    script_files = ['start_server.bat', 'start_client.bat', 'start_server.sh', 'start_client.sh']
    for script_file in script_files:
        script_path = project_root / script_file
        if script_path.exists():
            os.remove(script_path)
            print(f"√ 删除启动脚本: {script_file}")
    
    print("卸载完成")

if __name__ == "__main__":
    uninstall()

#!/usr/bin/env python3
"""
用户注册管理器演示脚本
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_manager import DatabaseManager
from server.user_registration_manager import UserRegistrationManager
from server.database_migrations import DatabaseMigration


def demo_user_registration():
    """演示用户注册功能"""
    print("=" * 60)
    print("用户注册管理器功能演示")
    print("=" * 60)
    
    # 使用演示数据库
    demo_db_path = "data/demo_registration.db"
    
    # 删除演示数据库（如果存在）
    if os.path.exists(demo_db_path):
        os.remove(demo_db_path)
        print("已删除旧的演示数据库")
    
    try:
        # 初始化管理器
        print("\n1. 初始化系统...")
        db_manager = DatabaseManager(demo_db_path)
        
        # 运行数据库迁移
        migration = DatabaseMigration(demo_db_path)
        migration.run_migrations()
        
        registration_manager = UserRegistrationManager(db_manager)
        print("✓ 系统初始化完成")
        
        # 演示用户注册
        print("\n2. 演示用户注册...")
        
        # 注册第一个用户
        print("\n注册用户: alice")
        result1 = registration_manager.register_user(
            username="alice",
            password="alice123456",
            confirm_password="alice123456",
            email="<EMAIL>"
        )
        print(f"注册结果: {result1}")
        
        # 注册第二个用户
        print("\n注册用户: bob")
        result2 = registration_manager.register_user(
            username="bob",
            password="bob123456",
            confirm_password="bob123456"
        )
        print(f"注册结果: {result2}")
        
        # 尝试重复注册
        print("\n尝试重复注册用户: alice")
        result3 = registration_manager.register_user(
            username="alice",
            password="newpassword123",
            confirm_password="newpassword123"
        )
        print(f"重复注册结果: {result3}")
        
        # 尝试弱密码注册
        print("\n尝试使用弱密码注册用户: charlie")
        result4 = registration_manager.register_user(
            username="charlie",
            password="123",
            confirm_password="123"
        )
        print(f"弱密码注册结果: {result4}")
        
        # 演示密码验证
        print("\n3. 演示密码验证...")
        
        test_passwords = [
            "123",           # 太短
            "password",      # 缺少数字
            "123456",        # 缺少字母
            "abc123",        # 常见弱密码
            "mypassword123", # 强密码
            "SecurePass2024" # 强密码
        ]
        
        for password in test_passwords:
            errors = registration_manager.validate_password(password)
            if errors:
                print(f"密码 '{password}': ✗ {', '.join(errors)}")
            else:
                print(f"密码 '{password}': ✓ 强度合格")
        
        # 演示用户名验证
        print("\n4. 演示用户名验证...")
        
        test_usernames = [
            "ab",           # 太短
            "admin",        # 保留用户名
            "123user",      # 数字开头
            "user@name",    # 特殊字符
            "validuser",    # 有效用户名
            "test_user",    # 有效用户名
            "user-123"      # 有效用户名
        ]
        
        for username in test_usernames:
            errors = registration_manager.validate_username(username)
            if errors:
                print(f"用户名 '{username}': ✗ {', '.join(errors)}")
            else:
                print(f"用户名 '{username}': ✓ 格式正确")
        
        # 演示邮箱验证
        print("\n5. 演示邮箱验证...")
        
        test_emails = [
            "invalid-email",        # 无效格式
            "test@",               # 不完整
            "<EMAIL>",    # 有效邮箱
            "<EMAIL>", # 有效邮箱
            "<EMAIL>"  # 有效邮箱
        ]
        
        for email in test_emails:
            errors = registration_manager.validate_email(email)
            if errors:
                print(f"邮箱 '{email}': ✗ {', '.join(errors)}")
            else:
                print(f"邮箱 '{email}': ✓ 格式正确")
        
        # 演示配置管理
        print("\n6. 演示配置管理...")
        
        # 显示当前配置
        config = registration_manager.get_registration_config()
        print(f"当前最小密码长度: {config['min_password_length']}")
        print(f"是否要求邮箱: {config['require_email']}")
        
        # 更新配置
        print("\n更新配置: 最小密码长度改为8位，要求邮箱")
        registration_manager.update_registration_config({
            'min_password_length': 8,
            'require_email': True
        })
        
        # 测试新配置
        print("\n使用新配置测试注册...")
        result5 = registration_manager.register_user(
            username="david",
            password="short12",  # 7位密码
            confirm_password="short12"
            # 没有提供邮箱
        )
        print(f"新配置下的注册结果: {result5}")
        
        # 获取统计信息
        print("\n7. 注册统计信息...")
        stats = registration_manager.get_registration_statistics()
        print(f"总用户数: {stats['total_users']}")
        print(f"活跃用户数: {stats['active_users']}")
        print(f"按日期统计: {stats['registration_by_date']}")
        
        print("\n" + "=" * 60)
        print("✅ 用户注册管理器演示完成！")
        print("=" * 60)
        
        # 显示已注册的用户
        print("\n已注册的用户:")
        users = db_manager.get_all_users()
        for user in users:
            print(f"- {user.get('username', 'N/A')} (邮箱: {user.get('email', '未设置')})")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 保留演示数据库供查看
        print(f"\n演示数据库保存在: {demo_db_path}")


def main():
    """主函数"""
    print("开始用户注册管理器演示...")
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 运行演示
    success = demo_user_registration()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
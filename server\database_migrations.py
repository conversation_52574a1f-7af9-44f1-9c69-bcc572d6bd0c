#!/usr/bin/env python3
"""
数据库迁移脚本
用于扩展数据库结构以支持用户账号管理功能
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Any


class DatabaseMigration:
    """数据库迁移管理器"""
    
    def __init__(self, db_path: str = "data/auth.db"):
        self.db_path = db_path
        self.migrations = [
            self._migration_001_add_user_account_fields,
            self._migration_002_create_recharge_records,
            self._migration_003_create_consumption_records,
            self._migration_004_create_password_change_records,
            self._migration_005_create_billing_config,
        ]
    
    def run_migrations(self) -> bool:
        """
        运行所有数据库迁移
        
        Returns:
            bool: 迁移成功返回True
        """
        try:
            # 确保数据目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建迁移记录表
                self._create_migration_table(cursor)
                
                # 获取已执行的迁移
                executed_migrations = self._get_executed_migrations(cursor)
                
                # 执行未完成的迁移
                for i, migration in enumerate(self.migrations, 1):
                    migration_name = f"migration_{i:03d}"
                    
                    if migration_name not in executed_migrations:
                        print(f"执行迁移: {migration_name}")
                        
                        try:
                            migration(cursor)
                            self._record_migration(cursor, migration_name)
                            print(f"✓ 迁移 {migration_name} 完成")
                        except Exception as e:
                            print(f"✗ 迁移 {migration_name} 失败: {e}")
                            return False
                    else:
                        print(f"跳过已执行的迁移: {migration_name}")
                
                conn.commit()
                print("所有数据库迁移完成")
                return True
                
        except Exception as e:
            print(f"数据库迁移失败: {e}")
            return False
    
    def _create_migration_table(self, cursor: sqlite3.Cursor):
        """创建迁移记录表"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS database_migrations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                migration_name VARCHAR(100) NOT NULL UNIQUE,
                executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    def _get_executed_migrations(self, cursor: sqlite3.Cursor) -> List[str]:
        """获取已执行的迁移列表"""
        cursor.execute("SELECT migration_name FROM database_migrations")
        return [row[0] for row in cursor.fetchall()]
    
    def _record_migration(self, cursor: sqlite3.Cursor, migration_name: str):
        """记录已执行的迁移"""
        cursor.execute(
            "INSERT INTO database_migrations (migration_name) VALUES (?)",
            (migration_name,)
        )
    
    def _migration_001_add_user_account_fields(self, cursor: sqlite3.Cursor):
        """迁移001: 为用户表添加账户管理字段"""
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='users'
        """)
        
        if not cursor.fetchone():
            # 如果用户表不存在，创建完整的用户表
            cursor.execute("""
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) NOT NULL UNIQUE,
                    password_hash VARCHAR(255) NOT NULL,
                    salt VARCHAR(255) NOT NULL,
                    balance DECIMAL(10,2) DEFAULT 0.00,
                    email VARCHAR(255),
                    registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_login DATETIME,
                    status VARCHAR(20) DEFAULT 'active',
                    is_active BOOLEAN DEFAULT 1,
                    failed_attempts INTEGER DEFAULT 0,
                    locked_until DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
        else:
            # 如果用户表存在，添加新字段
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN salt VARCHAR(255)")
            except sqlite3.OperationalError:
                pass  # 字段可能已存在
            
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00")
            except sqlite3.OperationalError:
                pass
            
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN email VARCHAR(255)")
            except sqlite3.OperationalError:
                pass
            
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN registration_date DATETIME DEFAULT CURRENT_TIMESTAMP")
            except sqlite3.OperationalError:
                pass
            
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT 'active'")
            except sqlite3.OperationalError:
                pass
            
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN is_active BOOLEAN DEFAULT 1")
            except sqlite3.OperationalError:
                pass
            
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN failed_attempts INTEGER DEFAULT 0")
            except sqlite3.OperationalError:
                pass
            
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN locked_until DATETIME")
            except sqlite3.OperationalError:
                pass
            
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP")
            except sqlite3.OperationalError:
                pass
        
        # 创建用户名索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)
        """)
        
        # 创建状态索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)
        """)
    
    def _migration_002_create_recharge_records(self, cursor: sqlite3.Cursor):
        """迁移002: 创建充值记录表"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS recharge_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                recharge_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                recharge_type VARCHAR(20) DEFAULT 'manual',
                operator VARCHAR(50),
                description TEXT,
                status VARCHAR(20) DEFAULT 'completed',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (username) REFERENCES users(username)
            )
        """)
        
        # 创建索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_recharge_username ON recharge_records(username)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_recharge_time ON recharge_records(recharge_time)
        """)
    
    def _migration_003_create_consumption_records(self, cursor: sqlite3.Cursor):
        """迁移003: 创建消费记录表"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS consumption_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                consumption_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                service_type VARCHAR(50) DEFAULT 'auth',
                duration INTEGER,
                session_id VARCHAR(100),
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (username) REFERENCES users(username)
            )
        """)
        
        # 创建索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_consumption_username ON consumption_records(username)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_consumption_time ON consumption_records(consumption_time)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_consumption_session ON consumption_records(session_id)
        """)
    
    def _migration_004_create_password_change_records(self, cursor: sqlite3.Cursor):
        """迁移004: 创建密码修改记录表"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS password_change_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) NOT NULL,
                change_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                old_password_hash VARCHAR(255),
                ip_address VARCHAR(45),
                change_type VARCHAR(20) DEFAULT 'user_change',
                operator VARCHAR(50),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (username) REFERENCES users(username)
            )
        """)
        
        # 创建索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_password_change_username ON password_change_records(username)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_password_change_time ON password_change_records(change_time)
        """)
    
    def _migration_005_create_billing_config(self, cursor: sqlite3.Cursor):
        """迁移005: 创建计费配置表"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS billing_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                service_type VARCHAR(50) NOT NULL UNIQUE,
                rate_per_minute DECIMAL(10,4) NOT NULL,
                rate_per_session DECIMAL(10,2) DEFAULT 0.00,
                minimum_charge DECIMAL(10,2) DEFAULT 0.00,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 插入默认计费配置
        cursor.execute("""
            INSERT OR IGNORE INTO billing_config 
            (service_type, rate_per_minute, rate_per_session, minimum_charge, description) 
            VALUES 
            ('auth', 0.01, 0.00, 0.01, '网络验证服务'),
            ('premium', 0.02, 0.00, 0.01, '高级验证服务')
        """)
        
        # 创建索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_billing_service_type ON billing_config(service_type)
        """)
    
    def check_database_version(self) -> Dict[str, Any]:
        """检查数据库版本和迁移状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查迁移表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='database_migrations'
                """)
                
                if not cursor.fetchone():
                    return {
                        'version': 0,
                        'migrations_executed': 0,
                        'total_migrations': len(self.migrations),
                        'needs_migration': True
                    }
                
                # 获取已执行的迁移数量
                cursor.execute("SELECT COUNT(*) FROM database_migrations")
                executed_count = cursor.fetchone()[0]
                
                return {
                    'version': executed_count,
                    'migrations_executed': executed_count,
                    'total_migrations': len(self.migrations),
                    'needs_migration': executed_count < len(self.migrations)
                }
                
        except Exception as e:
            return {
                'version': 0,
                'migrations_executed': 0,
                'total_migrations': len(self.migrations),
                'needs_migration': True,
                'error': str(e)
            }


def main():
    """主函数 - 运行数据库迁移"""
    print("开始数据库迁移...")
    
    migration = DatabaseMigration()
    
    # 检查当前数据库状态
    version_info = migration.check_database_version()
    print(f"当前数据库版本: {version_info['version']}")
    print(f"已执行迁移: {version_info['migrations_executed']}/{version_info['total_migrations']}")
    
    if not version_info['needs_migration']:
        print("数据库已是最新版本，无需迁移")
        return True
    
    # 运行迁移
    success = migration.run_migrations()
    
    if success:
        print("✓ 数据库迁移完成")
        
        # 验证迁移结果
        new_version_info = migration.check_database_version()
        print(f"新数据库版本: {new_version_info['version']}")
    else:
        print("✗ 数据库迁移失败")
    
    return success


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
# 网络验证工具用户手册

## 目录

1. [概述](#概述)
2. [系统要求](#系统要求)
3. [安装指南](#安装指南)
4. [快速开始](#快速开始)
5. [服务器端使用](#服务器端使用)
6. [客户端使用](#客户端使用)
7. [配置说明](#配置说明)
8. [故障排除](#故障排除)
9. [常见问题](#常见问题)

## 概述

网络验证工具是一个基于Python的客户端-服务器认证系统，提供安全的用户认证、会话管理和系统监控功能。

### 主要功能

- **用户认证**: 安全的用户名/密码认证
- **会话管理**: 自动会话超时和管理
- **图形界面**: 直观的GUI界面
- **系统监控**: 实时性能监控和统计
- **日志记录**: 详细的操作日志
- **安全防护**: 防暴力破解和速率限制

## 系统要求

### 最低要求

- **操作系统**: Windows 7+, macOS 10.12+, Linux (Ubuntu 16.04+)
- **Python版本**: Python 3.7 或更高版本
- **内存**: 512MB RAM
- **存储空间**: 100MB 可用空间

### 推荐配置

- **操作系统**: Windows 10, macOS 11+, Linux (Ubuntu 20.04+)
- **Python版本**: Python 3.9 或更高版本
- **内存**: 1GB RAM
- **存储空间**: 500MB 可用空间

### 依赖项

#### 必需依赖
- tkinter (GUI界面)
- sqlite3 (数据库)
- socket (网络通信)
- threading (多线程)

#### 可选依赖
- psutil (系统监控)
- cryptography (高级加密)

## 安装指南

### 自动安装

1. 下载项目文件到本地目录
2. 打开命令行/终端，进入项目目录
3. 运行安装脚本：
   ```bash
   python install.py
   ```
4. 按照提示完成安装

### 手动安装

1. **创建目录结构**：
   ```
   network-auth-tool/
   ├── data/
   ├── logs/
   ├── config/
   └── backup/
   ```

2. **安装可选依赖**：
   ```bash
   pip install psutil
   ```

3. **创建配置文件**：
   - 复制 `config.ini.example` 到 `config.ini`
   - 复制 `client_config.ini.example` 到 `client_config.ini`

4. **初始化数据库**：
   ```bash
   python -c "from server.database_manager import DatabaseManager; DatabaseManager('data/auth.db')"
   ```

## 快速开始

### 1. 启动服务器

**Windows**:
```cmd
start_server.bat
```

**Linux/macOS**:
```bash
./start_server.sh
```

**或直接运行**:
```bash
python server_main.py
```

### 2. 启动客户端

**Windows**:
```cmd
start_client.bat
```

**Linux/macOS**:
```bash
./start_client.sh
```

**或直接运行**:
```bash
python client_main.py
```

### 3. 首次登录

- 默认管理员账户：`admin`
- 默认密码：`admin123`
- **重要**: 首次登录后请立即修改密码！

## 服务器端使用

### 启动服务器

服务器启动后会显示以下信息：
```
网络验证工具服务器 v1.0.0
==================================================
正在初始化服务器组件...
服务器已启动，监听 0.0.0.0:8888
已创建默认管理员用户 (用户名: admin, 密码: admin123)
请在首次登录后立即修改密码！
服务器已启动，按 Ctrl+C 停止服务器
==================================================
```

### 服务器管理界面

如果安装了GUI组件，可以使用图形界面管理服务器：

```bash
python -m server.server_gui
```

#### 主要功能

1. **用户管理**
   - 创建新用户
   - 修改用户密码
   - 删除用户
   - 查看用户列表

2. **会话监控**
   - 查看活跃会话
   - 强制断开会话
   - 会话统计信息

3. **系统监控**
   - 实时性能指标
   - 连接统计
   - 错误日志查看

4. **配置管理**
   - 修改服务器配置
   - 重新加载配置
   - 备份和恢复

### 命令行管理

服务器也支持命令行管理：

```bash
# 创建用户
python -m server.user_manager create_user username password

# 修改密码
python -m server.user_manager change_password username new_password

# 删除用户
python -m server.user_manager delete_user username

# 查看统计
python -m server.user_manager show_stats
```

## 客户端使用

### 连接服务器

1. 启动客户端应用程序
2. 在连接界面输入：
   - **服务器地址**: 服务器IP地址（默认：127.0.0.1）
   - **端口**: 服务器端口（默认：8888）
3. 点击"连接"按钮

### 用户认证

连接成功后：
1. 输入用户名和密码
2. 点击"登录"按钮
3. 等待认证结果

### 客户端界面

#### 连接状态区域
- 显示当前连接状态
- 服务器地址和端口
- 连接/断开按钮

#### 认证区域
- 用户名输入框
- 密码输入框
- 登录/注销按钮

#### 状态显示区域
- 认证状态
- 会话信息
- 错误消息

#### 操作按钮
- **连接/断开**: 管理服务器连接
- **登录/注销**: 用户认证操作
- **清除日志**: 清除显示的日志信息

### 自动重连

客户端支持自动重连功能：
- 连接断开时自动尝试重连
- 可配置重连间隔和最大尝试次数
- 在配置文件中设置 `auto_reconnect = true`

## 配置说明

### 服务器配置 (config.ini)

```ini
[server]
# 服务器监听地址 (0.0.0.0 表示所有接口)
host = 0.0.0.0
# 服务器端口
port = 8888
# 最大并发连接数
max_connections = 50

[database]
# 数据库文件路径
path = data/auth.db

[logging]
# 日志级别 (DEBUG, INFO, WARNING, ERROR)
level = INFO
# 日志文件路径
file_path = logs/server.log
# 日志文件最大大小 (字节)
max_file_size = 5242880
# 日志文件备份数量
backup_count = 3

[security]
# 会话超时时间 (秒)
session_timeout = 3600
# 最大失败尝试次数
max_failed_attempts = 5
# 锁定持续时间 (秒)
lockout_duration = 1800
# 密码最小长度
password_min_length = 6
```

### 客户端配置 (client_config.ini)

```ini
[client]
# 默认服务器地址
default_host = 127.0.0.1
# 默认服务器端口
default_port = 8888
# 连接超时时间 (秒)
connection_timeout = 10
# 接收超时时间 (秒)
receive_timeout = 30
# 是否自动重连
auto_reconnect = true
# 最大重试次数
max_retry_attempts = 3

[logging]
# 日志级别
level = INFO
# 日志文件路径
file_path = logs/client.log
# 日志文件最大大小
max_file_size = 5242880
# 日志文件备份数量
backup_count = 3

[ui]
# 窗口宽度
window_width = 500
# 窗口高度
window_height = 600
# 界面主题
theme = default
# 自动保存设置
auto_save_settings = true
```

## 故障排除

### 常见问题及解决方案

#### 1. 服务器无法启动

**问题**: 端口被占用
```
OSError: [Errno 98] Address already in use
```

**解决方案**:
- 检查端口是否被其他程序占用
- 修改配置文件中的端口号
- 或终止占用端口的程序

#### 2. 客户端无法连接

**问题**: 连接被拒绝
```
ConnectionRefusedError: [Errno 111] Connection refused
```

**解决方案**:
- 确认服务器已启动
- 检查服务器地址和端口是否正确
- 检查防火墙设置
- 确认网络连通性

#### 3. 数据库错误

**问题**: 数据库文件损坏或无法访问

**解决方案**:
- 检查数据库文件权限
- 重新初始化数据库：
  ```bash
  python -c "from server.database_manager import DatabaseManager; DatabaseManager('data/auth.db')"
  ```
- 从备份恢复数据库

#### 4. 认证失败

**问题**: 用户名或密码错误

**解决方案**:
- 确认用户名和密码正确
- 检查用户是否存在
- 重置用户密码：
  ```bash
  python -m server.user_manager change_password username new_password
  ```

#### 5. 性能问题

**问题**: 响应缓慢或连接超时

**解决方案**:
- 检查系统资源使用情况
- 运行系统优化工具：
  ```bash
  python tools/system_optimizer.py
  ```
- 调整配置参数（超时时间、连接数等）

### 日志分析

#### 服务器日志位置
- 默认位置: `logs/server.log`
- 日志级别: DEBUG, INFO, WARNING, ERROR

#### 客户端日志位置
- 默认位置: `logs/client.log`

#### 重要日志事件
- `USER_LOGIN`: 用户登录事件
- `USER_LOGOUT`: 用户注销事件
- `CONNECTION_ERROR`: 连接错误
- `AUTH_FAILED`: 认证失败
- `SESSION_EXPIRED`: 会话过期

### 性能监控

使用内置的性能监控工具：

```bash
# 查看实时性能
python -c "
from common.performance_monitor import performance_monitor
performance_monitor.start_monitoring()
import time
time.sleep(10)
print(performance_monitor.get_performance_report())
"
```

## 常见问题

### Q: 如何修改默认管理员密码？

A: 有两种方法：
1. 通过客户端登录后在用户管理界面修改
2. 使用命令行工具：
   ```bash
   python -m server.user_manager change_password admin new_password
   ```

### Q: 如何添加新用户？

A: 可以通过以下方式：
1. 服务器管理界面的用户管理功能
2. 命令行工具：
   ```bash
   python -m server.user_manager create_user username password
   ```

### Q: 如何备份用户数据？

A: 备份数据库文件：
```bash
cp data/auth.db backup/auth_backup_$(date +%Y%m%d).db
```

### Q: 如何更改服务器端口？

A: 修改 `config.ini` 文件中的端口设置：
```ini
[server]
port = 新端口号
```

### Q: 客户端支持多个服务器连接吗？

A: 当前版本每个客户端实例只能连接一个服务器。如需连接多个服务器，请启动多个客户端实例。

### Q: 如何启用调试模式？

A: 修改配置文件中的日志级别：
```ini
[logging]
level = DEBUG
```

### Q: 系统支持多少并发用户？

A: 默认配置支持50个并发连接。可以通过修改配置文件调整：
```ini
[server]
max_connections = 100
```

### Q: 如何卸载系统？

A: 运行卸载脚本：
```bash
python uninstall.py
```

### Q: 忘记管理员密码怎么办？

A: 可以重置数据库或直接修改数据库中的密码哈希值：
```bash
# 重新初始化数据库（会丢失所有用户数据）
rm data/auth.db
python -c "from server.database_manager import DatabaseManager; DatabaseManager('data/auth.db')"
```

### Q: 如何查看系统状态？

A: 使用系统诊断工具：
```bash
python -c "
from common.debug_tools import run_system_diagnostics
import json
result = run_system_diagnostics()
print(json.dumps(result, indent=2, ensure_ascii=False))
"
```

---

## 技术支持

如果遇到本手册未涵盖的问题，请：

1. 检查日志文件获取详细错误信息
2. 运行系统诊断工具
3. 查看项目文档和源代码注释
4. 提交问题报告时请包含：
   - 操作系统和Python版本
   - 错误日志
   - 重现步骤
   - 系统配置信息

---

*网络验证工具 v1.0.0 - 用户手册*
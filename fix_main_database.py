#!/usr/bin/env python3
"""
修复主数据库结构
"""

import os
import sys
import sqlite3

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_migrations import DatabaseMigration


def fix_main_database():
    """修复主数据库结构"""
    print("=" * 60)
    print("修复主数据库结构")
    print("=" * 60)
    
    db_path = "data/auth.db"
    
    if not os.path.exists(db_path):
        print(f"✗ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 备份原数据库
        backup_path = f"{db_path}.backup"
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✓ 已备份数据库到: {backup_path}")
        
        # 检查当前表结构
        print("\n1. 检查当前表结构...")
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 获取users表结构
            cursor.execute("PRAGMA table_info(users)")
            columns = cursor.fetchall()
            
            existing_columns = [col[1] for col in columns]
            print(f"现有字段: {existing_columns}")
            
            # 检查缺少的字段
            required_columns = {
                'balance': 'DECIMAL(10,2) DEFAULT 0.00',
                'email': 'VARCHAR(255)',
                'registration_date': 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'status': 'VARCHAR(20) DEFAULT \'active\'',
                'updated_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            }
            
            missing_columns = []
            for col_name, col_def in required_columns.items():
                if col_name not in existing_columns:
                    missing_columns.append((col_name, col_def))
            
            if missing_columns:
                print(f"缺少字段: {[col[0] for col in missing_columns]}")
                
                # 添加缺少的字段
                print("\n2. 添加缺少的字段...")
                
                for col_name, col_def in missing_columns:
                    try:
                        alter_sql = f"ALTER TABLE users ADD COLUMN {col_name} {col_def}"
                        print(f"执行: {alter_sql}")
                        cursor.execute(alter_sql)
                        print(f"✓ 已添加字段: {col_name}")
                    except sqlite3.OperationalError as e:
                        if "duplicate column name" in str(e):
                            print(f"! 字段 {col_name} 已存在")
                        else:
                            print(f"✗ 添加字段 {col_name} 失败: {e}")
                
                conn.commit()
            else:
                print("✓ 所有必需字段都存在")
        
        # 运行完整的数据库迁移
        print("\n3. 运行数据库迁移...")
        
        migration = DatabaseMigration(db_path)
        success = migration.run_migrations()
        
        if success:
            print("✓ 数据库迁移完成")
        else:
            print("✗ 数据库迁移失败")
            return False
        
        # 验证修复结果
        print("\n4. 验证修复结果...")
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查users表结构
            cursor.execute("PRAGMA table_info(users)")
            columns = cursor.fetchall()
            
            print("修复后的users表字段:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
            
            # 检查其他表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"\n数据库中的表: {[table[0] for table in tables]}")
            
            # 检查必需的表
            required_tables = ['users', 'recharge_records', 'consumption_records', 'password_change_records', 'billing_config']
            existing_tables = [table[0] for table in tables]
            
            missing_tables = [table for table in required_tables if table not in existing_tables]
            if missing_tables:
                print(f"缺少表: {missing_tables}")
            else:
                print("✓ 所有必需的表都存在")
        
        print("\n" + "=" * 60)
        print("✅ 主数据库修复完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 尝试恢复备份
        if os.path.exists(backup_path):
            try:
                shutil.copy2(backup_path, db_path)
                print(f"已恢复数据库备份")
            except Exception as restore_e:
                print(f"恢复备份失败: {restore_e}")
        
        return False


def test_registration_after_fix():
    """修复后测试注册功能"""
    print("\n" + "=" * 60)
    print("测试修复后的注册功能")
    print("=" * 60)
    
    try:
        from server.database_manager import DatabaseManager
        from server.user_registration_manager import UserRegistrationManager
        
        # 初始化组件
        db_manager = DatabaseManager("data/auth.db")
        registration_manager = UserRegistrationManager(db_manager)
        
        # 测试注册
        test_username = "fix_test_user"
        
        # 清理可能存在的测试用户
        existing_user = db_manager.get_user_info(test_username)
        if existing_user:
            db_manager.delete_user(test_username)
            print(f"已清理现有测试用户: {test_username}")
        
        # 执行注册
        result = registration_manager.register_user(
            username=test_username,
            password="testpass123",
            confirm_password="testpass123",
            email="<EMAIL>"
        )
        
        if result['success']:
            print(f"✓ 注册测试成功: {result['message']}")
            
            # 验证用户信息
            user_info = db_manager.get_user_info(test_username)
            if user_info:
                print(f"✓ 用户信息: {user_info}")
            
            # 清理测试用户
            db_manager.delete_user(test_username)
            print(f"✓ 已清理测试用户")
            
            return True
        else:
            print(f"✗ 注册测试失败: {result['message']}")
            if 'errors' in result:
                for error in result['errors']:
                    print(f"    - {error}")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始修复主数据库...")
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 修复数据库
    fix_success = fix_main_database()
    
    if fix_success:
        # 测试注册功能
        test_success = test_registration_after_fix()
        
        if test_success:
            print("\n🎉 数据库修复成功，注册功能正常！")
            print("\n现在您可以正常使用注册功能了。")
        else:
            print("\n⚠️ 数据库修复完成，但注册功能仍有问题。")
    else:
        print("\n❌ 数据库修复失败。")
    
    return 0 if fix_success else 1


if __name__ == "__main__":
    sys.exit(main())
#!/usr/bin/env python3
"""
完整的用户注册功能测试
测试客户端、服务器和Web界面的注册功能
"""

import os
import sys
import time
import threading
import socket

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.server_application import ServerApplication
from client.network_client import NetworkClient
from server.web_gui.api import WebGUIAPI


def test_server_registration():
    """测试服务器端注册功能"""
    print("=" * 60)
    print("测试服务器端注册功能")
    print("=" * 60)
    
    # 使用测试数据库
    test_db_path = "data/test_complete_registration.db"
    
    # 删除测试数据库
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    
    try:
        # 启动服务器
        print("1. 启动测试服务器...")
        server_app = ServerApplication()
        
        # 修改配置使用测试数据库
        server_app.config_manager.config['database']['path'] = test_db_path
        
        # 在新线程中启动服务器
        server_thread = threading.Thread(target=server_app.start, daemon=True)
        server_thread.start()
        
        # 等待服务器启动
        time.sleep(2)
        
        print("✓ 服务器启动成功")
        
        # 测试客户端注册
        print("\n2. 测试客户端注册...")
        
        client = NetworkClient()
        
        # 连接到服务器
        connect_result = client.connect("127.0.0.1", 8888)
        if not connect_result['success']:
            print(f"✗ 连接失败: {connect_result['message']}")
            return False
        
        print("✓ 客户端连接成功")
        
        # 测试用户注册
        register_result = client.register_user(
            username="testuser123",
            password="testpass123",
            email="<EMAIL>"
        )
        
        if register_result['success']:
            print(f"✓ 用户注册成功: {register_result['username']}")
        else:
            print(f"✗ 用户注册失败: {register_result['message']}")
            return False
        
        # 测试重复注册
        duplicate_result = client.register_user(
            username="testuser123",
            password="anotherpass123"
        )
        
        if not duplicate_result['success']:
            print("✓ 重复注册正确被拒绝")
        else:
            print("✗ 重复注册处理异常")
        
        # 测试注册后登录
        print("\n3. 测试注册后登录...")
        
        auth_result = client.authenticate("testuser123", "testpass123")
        if auth_result['success']:
            print("✓ 注册用户登录成功")
        else:
            print(f"✗ 注册用户登录失败: {auth_result['message']}")
        
        # 断开连接
        client.disconnect()
        
        # 测试Web API
        print("\n4. 测试Web API注册功能...")
        
        web_api = WebGUIAPI()
        
        # 测试Web注册
        web_register_result = web_api.register_user(
            username="webuser123",
            password="webpass123",
            email="<EMAIL>"
        )
        
        if web_register_result['success']:
            print("✓ Web API注册成功")
        else:
            print(f"✗ Web API注册失败: {web_register_result['message']}")
        
        # 测试获取用户列表
        users = web_api.get_users()
        print(f"✓ 获取用户列表: {len(users)} 个用户")
        
        # 测试获取注册统计
        stats_result = web_api.get_registration_statistics()
        if stats_result['success']:
            stats = stats_result['data']
            print(f"✓ 注册统计: 总用户 {stats['total_users']}, 活跃用户 {stats['active_users']}")
        else:
            print(f"✗ 获取注册统计失败: {stats_result['message']}")
        
        print("\n" + "=" * 60)
        print("✅ 完整注册功能测试通过！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 停止服务器
        try:
            server_app.stop()
        except:
            pass
        
        # 清理测试数据库
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
            print("\n已清理测试数据库")


def test_registration_validation():
    """测试注册验证功能"""
    print("\n" + "=" * 60)
    print("测试注册验证功能")
    print("=" * 60)
    
    try:
        from server.user_registration_manager import UserRegistrationManager
        from server.database_manager import DatabaseManager
        
        # 创建测试数据库
        test_db_path = "data/test_validation.db"
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        db_manager = DatabaseManager(test_db_path)
        registration_manager = UserRegistrationManager(db_manager)
        
        # 测试各种验证场景
        test_cases = [
            {
                'name': '正常注册',
                'username': 'validuser',
                'password': 'validpass123',
                'email': '<EMAIL>',
                'should_succeed': True
            },
            {
                'name': '用户名太短',
                'username': 'ab',
                'password': 'validpass123',
                'email': '<EMAIL>',
                'should_succeed': False
            },
            {
                'name': '密码太弱',
                'username': 'testuser2',
                'password': '123',
                'email': '<EMAIL>',
                'should_succeed': False
            },
            {
                'name': '邮箱格式错误',
                'username': 'testuser3',
                'password': 'validpass123',
                'email': 'invalid-email',
                'should_succeed': False
            },
            {
                'name': '重复用户名',
                'username': 'validuser',
                'password': 'anotherpass123',
                'email': '<EMAIL>',
                'should_succeed': False
            }
        ]
        
        for test_case in test_cases:
            print(f"\n测试: {test_case['name']}")
            
            result = registration_manager.register_user(
                username=test_case['username'],
                password=test_case['password'],
                email=test_case['email']
            )
            
            if result['success'] == test_case['should_succeed']:
                print(f"✓ {test_case['name']} - 结果符合预期")
            else:
                print(f"✗ {test_case['name']} - 结果不符合预期")
                print(f"  期望: {'成功' if test_case['should_succeed'] else '失败'}")
                print(f"  实际: {'成功' if result['success'] else '失败'}")
                print(f"  消息: {result.get('message', 'N/A')}")
        
        print("\n✅ 注册验证测试完成")
        
        # 清理测试数据库
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始完整的用户注册功能测试...")
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 测试注册验证
    validation_success = test_registration_validation()
    
    # 测试完整功能
    server_success = test_server_registration()
    
    if validation_success and server_success:
        print("\n🎉 所有测试通过！用户注册功能已完全实现。")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查问题。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
#!/usr/bin/env python3
"""
卡密充值对话框
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Optional, Callable


class CardRechargeDialog:
    """卡密充值对话框"""
    
    def __init__(self, parent, username: str, current_time_info: Dict[str, Any] = None,
                 recharge_callback: Optional[Callable] = None):
        """
        初始化充值对话框
        
        Args:
            parent: 父窗口
            username: 用户名
            current_time_info: 当前时间信息
            recharge_callback: 充值回调函数
        """
        self.parent = parent
        self.username = username
        self.current_time_info = current_time_info or {}
        self.recharge_callback = recharge_callback
        self.result = None
        self.dialog = None
    
    def show(self) -> Optional[Dict[str, Any]]:
        """
        显示充值对话框
        
        Returns:
            Dict: 充值结果，取消返回None
        """
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("卡密充值")
        self.dialog.geometry("500x600")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        self.create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
        return self.result
    
    def create_widgets(self):
        """创建对话框控件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="卡密充值", font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 用户信息框架
        info_frame = ttk.LabelFrame(main_frame, text="账户信息", padding="15")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 用户名
        ttk.Label(info_frame, text="用户名:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Label(info_frame, text=self.username, font=('Arial', 10)).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 当前状态
        ttk.Label(info_frame, text="当前状态:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=5)
        
        if self.current_time_info.get('has_time', False):
            remaining_hours = self.current_time_info.get('remaining_hours', 0)
            if remaining_hours > 24:
                time_text = f"{remaining_hours/24:.1f} 天"
            else:
                time_text = f"{remaining_hours:.1f} 小时"
            status_text = f"剩余时间: {time_text}"
            status_color = "green"
        else:
            status_text = "已过期或无时间"
            status_color = "red"
        
        self.status_label = ttk.Label(info_frame, text=status_text, 
                                     font=('Arial', 10, 'bold'), foreground=status_color)
        self.status_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 卡密输入框架
        card_frame = ttk.LabelFrame(main_frame, text="充值卡密", padding="15")
        card_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 卡密输入
        ttk.Label(card_frame, text="请输入卡密:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, pady=(0, 5))
        
        self.card_code_var = tk.StringVar()
        self.card_entry = ttk.Entry(card_frame, textvariable=self.card_code_var, 
                                   font=('Arial', 12), width=30)
        self.card_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 卡密格式提示
        ttk.Label(card_frame, text="格式: XXXX-XXXX-XXXX-XXXX", 
                 font=('Arial', 9), foreground="gray").pack(anchor=tk.W)
        
        # 卡密类型说明
        types_frame = ttk.LabelFrame(main_frame, text="卡密类型说明", padding="15")
        types_frame.pack(fill=tk.X, pady=(0, 20))
        
        card_types = [
            ("1小时卡", "1小时使用时间"),
            ("天卡", "24小时使用时间"),
            ("周卡", "7天使用时间"),
            ("月卡", "30天使用时间"),
            ("季卡", "90天使用时间"),
            ("年卡", "365天使用时间"),
            ("10年卡", "3650天使用时间")
        ]
        
        for i, (card_name, description) in enumerate(card_types):
            row = i // 2
            col = i % 2
            
            type_frame = ttk.Frame(types_frame)
            type_frame.grid(row=row, column=col, sticky=tk.W, padx=(0, 20), pady=2)
            
            ttk.Label(type_frame, text=f"• {card_name}:", 
                     font=('Arial', 9, 'bold')).pack(side=tk.LEFT)
            ttk.Label(type_frame, text=description, 
                     font=('Arial', 9)).pack(side=tk.LEFT, padx=(5, 0))
        
        # 充值说明
        note_frame = ttk.LabelFrame(main_frame, text="充值说明", padding="15")
        note_frame.pack(fill=tk.X, pady=(0, 20))
        
        notes = [
            "• 卡密充值后立即生效",
            "• 时间会累加到现有时间上",
            "• 每个卡密只能使用一次",
            "• 请妥善保管您的卡密"
        ]
        
        for note in notes:
            ttk.Label(note_frame, text=note, font=('Arial', 9)).pack(anchor=tk.W, pady=1)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 充值按钮
        self.recharge_btn = ttk.Button(button_frame, text="确认充值", 
                                      command=self.confirm_recharge, style="Accent.TButton")
        self.recharge_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 取消按钮
        cancel_btn = ttk.Button(button_frame, text="取消", command=self.cancel)
        cancel_btn.pack(side=tk.RIGHT)
        
        # 绑定事件
        self.card_code_var.trace('w', self.validate_card_code)
        self.card_entry.bind('<Return>', lambda e: self.confirm_recharge())
        
        # 设置焦点
        self.card_entry.focus()
    
    def validate_card_code(self, *args):
        """验证卡密格式"""
        card_code = self.card_code_var.get().strip().upper()
        
        # 自动格式化卡密
        if len(card_code) > 0:
            # 移除所有非字母数字字符
            clean_code = ''.join(c for c in card_code if c.isalnum())
            
            # 自动添加连字符
            if len(clean_code) > 4:
                formatted_parts = []
                for i in range(0, len(clean_code), 4):
                    formatted_parts.append(clean_code[i:i+4])
                formatted_code = '-'.join(formatted_parts)
                
                # 更新输入框（避免递归）
                if formatted_code != card_code:
                    self.card_code_var.set(formatted_code)
                    self.card_entry.icursor(len(formatted_code))
        
        # 验证格式
        if len(card_code) == 0:
            self.recharge_btn.config(state=tk.DISABLED)
        elif len(card_code) >= 16:  # 至少16个字符（包含连字符）
            self.recharge_btn.config(state=tk.NORMAL)
        else:
            self.recharge_btn.config(state=tk.DISABLED)
    
    def confirm_recharge(self):
        """确认充值"""
        card_code = self.card_code_var.get().strip().upper()
        
        if not card_code:
            messagebox.showerror("错误", "请输入卡密")
            return
        
        if len(card_code) < 16:
            messagebox.showerror("错误", "卡密格式不正确")
            return
        
        # 显示确认对话框
        confirm_msg = f"确认使用卡密充值？\\n\\n"
        confirm_msg += f"用户: {self.username}\\n"
        confirm_msg += f"卡密: {card_code}\\n\\n"
        confirm_msg += "注意: 每个卡密只能使用一次！"
        
        if messagebox.askyesno("确认充值", confirm_msg):
            # 执行充值
            self.process_recharge(card_code)
    
    def process_recharge(self, card_code: str):
        """处理充值请求"""
        try:
            # 禁用按钮，防止重复提交
            self.recharge_btn.config(state=tk.DISABLED, text="充值中...")
            self.dialog.update()
            
            if self.recharge_callback:
                # 调用充值回调函数
                result = self.recharge_callback(self.username, card_code)
                
                if result and result.get('success'):
                    # 充值成功
                    hours_added = result.get('hours_added', 0)
                    card_type = result.get('card_type', '未知')
                    
                    success_msg = f"充值成功！\\n\\n"
                    success_msg += f"卡密类型: {card_type}\\n"
                    success_msg += f"增加时间: {hours_added} 小时\\n"
                    
                    if hours_added > 24:
                        success_msg += f"({hours_added/24:.1f} 天)\\n"
                    
                    messagebox.showinfo("充值成功", success_msg)
                    
                    self.result = result
                    self.dialog.destroy()
                else:
                    # 充值失败
                    error_msg = result.get('message', '充值失败') if result else '充值请求失败'
                    messagebox.showerror("充值失败", error_msg)
                    
                    # 恢复按钮状态
                    self.recharge_btn.config(state=tk.NORMAL, text="确认充值")
            else:
                # 没有充值回调函数，返回充值数据
                self.result = {
                    'success': True,
                    'card_code': card_code,
                    'username': self.username
                }
                self.dialog.destroy()
                
        except Exception as e:
            messagebox.showerror("充值失败", f"充值过程中发生错误：{str(e)}")
            self.recharge_btn.config(state=tk.NORMAL, text="确认充值")
    
    def cancel(self):
        """取消充值"""
        self.result = None
        self.dialog.destroy()


def test_card_recharge_dialog():
    """测试卡密充值对话框"""
    def mock_recharge_callback(username, card_code):
        """模拟充值回调"""
        print(f"模拟充值: 用户={username}, 卡密={card_code}")
        
        # 模拟不同的结果
        if "TEST" in card_code:
            return {
                'success': True,
                'message': '充值成功',
                'hours_added': 24,
                'card_type': 'DAY_1'
            }
        else:
            return {
                'success': False,
                'message': '卡密不存在或已使用'
            }
    
    root = tk.Tk()
    root.title("卡密充值对话框测试")
    root.geometry("300x200")
    
    # 模拟时间信息
    time_info = {
        'has_time': True,
        'remaining_hours': 12.5
    }
    
    def show_dialog():
        dialog = CardRechargeDialog(
            parent=root,
            username="testuser",
            current_time_info=time_info,
            recharge_callback=mock_recharge_callback
        )
        result = dialog.show()
        if result:
            print(f"充值结果: {result}")
        else:
            print("用户取消了充值")
    
    ttk.Button(root, text="打开充值对话框", command=show_dialog).pack(pady=50)
    
    root.mainloop()


if __name__ == "__main__":
    test_card_recharge_dialog()
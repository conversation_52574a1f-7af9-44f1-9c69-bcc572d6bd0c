#!/usr/bin/env python3
"""
测试客户端注册功能
"""

import os
import sys
import time
import threading

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.server_application import ServerApplication
from client.network_client import NetworkClient


def test_client_registration():
    """测试客户端注册功能"""
    print("=" * 60)
    print("测试客户端注册功能")
    print("=" * 60)
    
    # 使用测试数据库
    test_db_path = "data/test_client_reg.db"
    
    # 删除测试数据库（如果存在）
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
        print("已删除旧的测试数据库")
    
    server_app = None
    
    try:
        # 1. 启动测试服务器
        print("\n1. 启动测试服务器...")
        
        # 创建临时配置
        test_config = "test_config.ini"
        with open(test_config, 'w', encoding='utf-8') as f:
            f.write(f"""[server]
host = 127.0.0.1
port = 18888
max_connections = 10

[database]
path = {test_db_path}

[security]
session_timeout = 3600
max_failed_attempts = 5
lockout_duration = 300
password_min_length = 6

[logging]
level = INFO
file = logs/test_server.log
""")
        
        # 启动服务器
        server_app = ServerApplication(test_config)
        
        def start_server():
            server_app.start()
        
        server_thread = threading.Thread(target=start_server, daemon=True)
        server_thread.start()
        
        # 等待服务器启动
        time.sleep(2)
        
        if not server_app.is_server_running():
            print("✗ 服务器启动失败")
            return False
        
        print("✓ 测试服务器启动成功")
        
        # 2. 测试客户端连接
        print("\n2. 测试客户端连接...")
        
        client = NetworkClient()
        
        # 连接到服务器
        connect_result = client.connect('127.0.0.1', 8888)
        if not connect_result['success']:
            print(f"✗ 客户端连接失败: {connect_result['message']}")
            return False
        
        print(f"✓ 客户端连接成功: {connect_result['message']}")
        
        # 3. 测试用户注册
        print("\n3. 测试用户注册...")
        
        test_users = [
            {'username': 'testuser1', 'password': 'testpass123', 'email': '<EMAIL>'},
            {'username': 'testuser2', 'password': 'testpass456', 'email': None},
            {'username': 'testuser3', 'password': 'testpass789', 'email': '<EMAIL>'},
        ]
        
        for i, user_data in enumerate(test_users, 1):
            print(f"\n3.{i} 注册用户: {user_data['username']}")
            
            register_result = client.register_user(
                username=user_data['username'],
                password=user_data['password'],
                email=user_data['email']
            )
            
            if register_result['success']:
                print(f"✓ 用户 {user_data['username']} 注册成功")
                print(f"  消息: {register_result['message']}")
            else:
                print(f"✗ 用户 {user_data['username']} 注册失败")
                print(f"  错误: {register_result['message']}")
                if 'errors' in register_result:
                    for error in register_result['errors']:
                        print(f"    - {error}")
        
        # 4. 测试重复注册
        print("\n4. 测试重复注册...")
        
        duplicate_result = client.register_user(
            username='testuser1',  # 重复用户名
            password='anotherpass123',
            email='<EMAIL>'
        )
        
        if not duplicate_result['success']:
            print(f"✓ 重复注册正确被拒绝: {duplicate_result['message']}")
        else:
            print(f"✗ 重复注册处理异常: {duplicate_result}")
        
        # 5. 测试无效数据注册
        print("\n5. 测试无效数据注册...")
        
        invalid_tests = [
            {'username': 'ab', 'password': 'testpass123', 'desc': '用户名过短'},
            {'username': 'validuser', 'password': '123', 'desc': '密码过短'},
            {'username': '', 'password': 'testpass123', 'desc': '用户名为空'},
            {'username': 'validuser2', 'password': '', 'desc': '密码为空'},
        ]
        
        for test_case in invalid_tests:
            print(f"\n  测试 {test_case['desc']}...")
            
            invalid_result = client.register_user(
                username=test_case['username'],
                password=test_case['password']
            )
            
            if not invalid_result['success']:
                print(f"  ✓ 无效数据正确被拒绝: {invalid_result['message']}")
            else:
                print(f"  ✗ 无效数据处理异常: {invalid_result}")
        
        # 6. 测试注册后登录
        print("\n6. 测试注册后登录...")
        
        auth_result = client.authenticate('testuser1', 'testpass123')
        
        if auth_result['success']:
            print(f"✓ 注册用户登录成功: {auth_result['message']}")
        else:
            print(f"✗ 注册用户登录失败: {auth_result['message']}")
        
        # 7. 断开连接
        print("\n7. 断开连接...")
        
        disconnect_result = client.disconnect()
        if disconnect_result['success']:
            print(f"✓ 客户端断开连接: {disconnect_result['message']}")
        
        print("\n" + "=" * 60)
        print("✅ 客户端注册功能测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        try:
            if server_app and server_app.is_server_running():
                print("\n停止测试服务器...")
                server_app.shutdown()
                time.sleep(1)
            
            # 删除临时文件
            for temp_file in ['test_config.ini', test_db_path]:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            
            print("已清理测试资源")
            
        except Exception as e:
            print(f"清理资源时出错: {e}")


def main():
    """主函数"""
    print("开始客户端注册功能测试...")
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    # 运行测试
    success = test_client_registration()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
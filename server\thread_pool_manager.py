"""
线程池管理器模块
实现多线程并发处理、线程安全和资源管理
"""

import threading
import queue
import time
from typing import Dict, Any, Callable, Optional, List
from concurrent.futures import ThreadPoolExecutor, Future
from datetime import datetime

from common.exceptions import NetworkError


class ThreadPoolManager:
    """线程池管理器"""
    
    def __init__(self, max_threads: int = 50, queue_size: int = 100):
        """
        初始化线程池管理器
        
        Args:
            max_threads: 最大线程数
            queue_size: 任务队列大小
        """
        self.max_threads = max_threads
        self.queue_size = queue_size
        
        # 创建线程池
        self.executor = ThreadPoolExecutor(
            max_workers=max_threads,
            thread_name_prefix="AuthServer"
        )
        
        # 任务队列和统计
        self.task_queue = queue.Queue(maxsize=queue_size)
        self.active_tasks = {}  # 活跃任务字典
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.total_tasks = 0
        
        # 线程安全锁
        self._lock = threading.Lock()
        self._task_id_counter = 0
        
        # 启动时间
        self.start_time = datetime.now()
        
        # 监控线程
        self._monitor_thread = None
        self._shutdown_event = threading.Event()
        self._start_monitor()
    
    def _start_monitor(self):
        """启动监控线程"""
        def monitor_tasks():
            """监控任务执行情况"""
            while not self._shutdown_event.is_set():
                try:
                    # 清理已完成的任务
                    self._cleanup_completed_tasks()
                    
                    # 每5秒检查一次
                    if self._shutdown_event.wait(5):
                        break
                        
                except Exception as e:
                    print(f"监控线程发生错误: {e}")
        
        self._monitor_thread = threading.Thread(target=monitor_tasks, daemon=True)
        self._monitor_thread.start()
    
    def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        with self._lock:
            completed_task_ids = []
            for task_id, task_info in self.active_tasks.items():
                future = task_info['future']
                if future.done():
                    completed_task_ids.append(task_id)
                    
                    # 更新统计
                    if future.exception():
                        self.failed_tasks += 1
                    else:
                        self.completed_tasks += 1
            
            # 移除已完成的任务
            for task_id in completed_task_ids:
                del self.active_tasks[task_id]
    
    def _generate_task_id(self) -> str:
        """生成任务ID"""
        with self._lock:
            self._task_id_counter += 1
            return f"task_{self._task_id_counter}_{int(time.time())}"
    
    def submit_task(self, func: Callable, *args, **kwargs) -> Dict[str, Any]:
        """
        提交任务到线程池
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            Dict[str, Any]: 任务提交结果
        """
        try:
            # 检查线程池是否已关闭
            if self.executor._shutdown:
                return {
                    'success': False,
                    'message': '线程池已关闭',
                    'task_id': None
                }
            
            # 生成任务ID
            task_id = self._generate_task_id()
            
            # 提交任务
            future = self.executor.submit(self._task_wrapper, task_id, func, *args, **kwargs)
            
            # 记录任务信息
            task_info = {
                'task_id': task_id,
                'function': func.__name__ if hasattr(func, '__name__') else str(func),
                'submitted_at': datetime.now().isoformat(),
                'future': future,
                'args_count': len(args),
                'kwargs_count': len(kwargs)
            }
            
            with self._lock:
                self.active_tasks[task_id] = task_info
                self.total_tasks += 1
            
            return {
                'success': True,
                'message': '任务已提交',
                'task_id': task_id,
                'future': future
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'提交任务失败: {str(e)}',
                'task_id': None
            }
    
    def _task_wrapper(self, task_id: str, func: Callable, *args, **kwargs):
        """
        任务包装器，用于异常处理和日志记录
        
        Args:
            task_id: 任务ID
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
        """
        start_time = time.time()
        
        try:
            # 更新任务状态
            with self._lock:
                if task_id in self.active_tasks:
                    self.active_tasks[task_id]['started_at'] = datetime.now().isoformat()
                    self.active_tasks[task_id]['thread_name'] = threading.current_thread().name
            
            # 执行任务
            result = func(*args, **kwargs)
            
            # 记录执行时间
            execution_time = time.time() - start_time
            with self._lock:
                if task_id in self.active_tasks:
                    self.active_tasks[task_id]['completed_at'] = datetime.now().isoformat()
                    self.active_tasks[task_id]['execution_time'] = execution_time
            
            return result
            
        except Exception as e:
            # 记录异常信息
            execution_time = time.time() - start_time
            with self._lock:
                if task_id in self.active_tasks:
                    self.active_tasks[task_id]['failed_at'] = datetime.now().isoformat()
                    self.active_tasks[task_id]['execution_time'] = execution_time
                    self.active_tasks[task_id]['error'] = str(e)
            
            print(f"任务 {task_id} 执行失败: {e}")
            raise
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务状态信息
        """
        with self._lock:
            if task_id not in self.active_tasks:
                return None
            
            task_info = self.active_tasks[task_id].copy()
            future = task_info['future']
            
            # 添加状态信息
            if future.done():
                if future.exception():
                    task_info['status'] = 'failed'
                    task_info['exception'] = str(future.exception())
                else:
                    task_info['status'] = 'completed'
            elif future.running():
                task_info['status'] = 'running'
            else:
                task_info['status'] = 'pending'
            
            # 移除future对象（不可序列化）
            del task_info['future']
            
            return task_info
    
    def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 取消结果
        """
        with self._lock:
            if task_id not in self.active_tasks:
                return {
                    'success': False,
                    'message': '任务不存在'
                }
            
            future = self.active_tasks[task_id]['future']
            
            if future.cancel():
                self.active_tasks[task_id]['cancelled_at'] = datetime.now().isoformat()
                return {
                    'success': True,
                    'message': '任务已取消'
                }
            else:
                return {
                    'success': False,
                    'message': '任务无法取消（可能已在执行中）'
                }
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """
        获取活跃任务列表
        
        Returns:
            List[Dict[str, Any]]: 活跃任务列表
        """
        with self._lock:
            tasks = []
            for task_id, task_info in self.active_tasks.items():
                task_copy = task_info.copy()
                future = task_copy['future']
                
                # 添加状态信息
                if future.done():
                    if future.exception():
                        task_copy['status'] = 'failed'
                    else:
                        task_copy['status'] = 'completed'
                elif future.running():
                    task_copy['status'] = 'running'
                else:
                    task_copy['status'] = 'pending'
                
                # 移除future对象
                del task_copy['future']
                tasks.append(task_copy)
            
            return tasks
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取线程池统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            # 计算当前状态
            running_tasks = 0
            pending_tasks = 0
            
            for task_info in self.active_tasks.values():
                future = task_info['future']
                if future.running():
                    running_tasks += 1
                elif not future.done():
                    pending_tasks += 1
            
            # 计算运行时间
            uptime_seconds = int((datetime.now() - self.start_time).total_seconds())
            
            return {
                'max_threads': self.max_threads,
                'active_tasks': len(self.active_tasks),
                'running_tasks': running_tasks,
                'pending_tasks': pending_tasks,
                'completed_tasks': self.completed_tasks,
                'failed_tasks': self.failed_tasks,
                'total_tasks': self.total_tasks,
                'success_rate': (self.completed_tasks / max(self.total_tasks, 1)) * 100,
                'uptime_seconds': uptime_seconds,
                'queue_size': self.queue_size,
                'is_shutdown': self.executor._shutdown
            }
    
    def wait_for_task(self, task_id: str, timeout: float = None) -> Dict[str, Any]:
        """
        等待任务完成
        
        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            
        Returns:
            Dict[str, Any]: 等待结果
        """
        with self._lock:
            if task_id not in self.active_tasks:
                return {
                    'success': False,
                    'message': '任务不存在'
                }
            
            future = self.active_tasks[task_id]['future']
        
        try:
            # 等待任务完成
            result = future.result(timeout=timeout)
            return {
                'success': True,
                'message': '任务完成',
                'result': result
            }
            
        except TimeoutError:
            return {
                'success': False,
                'message': '等待任务超时'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'任务执行失败: {str(e)}',
                'exception': str(e)
            }
    
    def shutdown(self, wait: bool = True, timeout: float = 30.0) -> Dict[str, Any]:
        """
        关闭线程池
        
        Args:
            wait: 是否等待任务完成
            timeout: 等待超时时间
            
        Returns:
            Dict[str, Any]: 关闭结果
        """
        try:
            # 停止监控线程
            self._shutdown_event.set()
            if self._monitor_thread and self._monitor_thread.is_alive():
                self._monitor_thread.join(timeout=5)
            
            # 关闭线程池
            self.executor.shutdown(wait=wait)
            
            # 等待所有任务完成（如果指定了等待）
            if wait:
                start_time = time.time()
                while time.time() - start_time < timeout:
                    with self._lock:
                        if not self.active_tasks:
                            break
                    time.sleep(0.1)
            
            # 获取最终统计
            final_stats = self.get_statistics()
            
            return {
                'success': True,
                'message': '线程池已关闭',
                'final_stats': final_stats
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'关闭线程池失败: {str(e)}'
            }
    
    def is_shutdown(self) -> bool:
        """
        检查线程池是否已关闭
        
        Returns:
            bool: 已关闭返回True
        """
        return self.executor._shutdown
    
    def get_thread_info(self) -> Dict[str, Any]:
        """
        获取线程信息
        
        Returns:
            Dict[str, Any]: 线程信息
        """
        return {
            'max_workers': self.max_threads,
            'current_threads': threading.active_count(),
            'main_thread': threading.main_thread().name,
            'current_thread': threading.current_thread().name,
            'daemon_threads': sum(1 for t in threading.enumerate() if t.daemon)
        }
#!/usr/bin/env python3
"""
测试数据创建工具
为网络验证工具创建测试用户和数据
"""

import sys
import os
import random
import string
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.database_manager import DatabaseManager
from server.user_manager import UserManager
from server.session_manager import SessionManager


class TestDataCreator:
    """测试数据创建器"""
    
    def __init__(self, db_path: str = "data/auth.db"):
        """
        初始化测试数据创建器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.db_manager = None
        self.user_manager = None
        self.session_manager = None
        
        # 测试用户数据
        self.test_users = [
            {"username": "admin", "password": "admin123", "role": "admin"},
            {"username": "user1", "password": "password1", "role": "user"},
            {"username": "user2", "password": "password2", "role": "user"},
            {"username": "test_user", "password": "test123", "role": "user"},
            {"username": "demo_user", "password": "demo123", "role": "user"},
            {"username": "guest", "password": "guest123", "role": "guest"},
            {"username": "manager", "password": "manager123", "role": "manager"},
            {"username": "operator", "password": "operator123", "role": "operator"},
        ]
        
        # 随机用户名前缀
        self.username_prefixes = [
            "user", "test", "demo", "sample", "example", 
            "client", "member", "account", "profile"
        ]
    
    def initialize_components(self):
        """初始化组件"""
        try:
            print("初始化数据库组件...")
            
            # 确保数据目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # 初始化数据库管理器
            self.db_manager = DatabaseManager(self.db_path)
            
            # 初始化用户管理器
            self.user_manager = UserManager(self.db_manager)
            
            # 初始化会话管理器
            self.session_manager = SessionManager(self.db_manager)
            
            print("√ 组件初始化完成")
            return True
            
        except Exception as e:
            print(f"× 组件初始化失败: {e}")
            return False
    
    def create_basic_users(self):
        """创建基本测试用户"""
        print("创建基本测试用户...")
        
        created_count = 0
        failed_count = 0
        
        for user_data in self.test_users:
            try:
                # 检查用户是否已存在
                existing_user = self.user_manager.get_user_info(user_data["username"])
                if existing_user:
                    print(f"  用户 {user_data['username']} 已存在，跳过")
                    continue
                
                # 创建用户
                result = self.user_manager.create_user(
                    user_data["username"], 
                    user_data["password"]
                )
                
                if result["success"]:
                    print(f"  √ 创建用户: {user_data['username']}")
                    created_count += 1
                else:
                    print(f"  × 创建用户失败: {user_data['username']} - {result['message']}")
                    failed_count += 1
                    
            except Exception as e:
                print(f"  × 创建用户异常: {user_data['username']} - {e}")
                failed_count += 1
        
        print(f"基本用户创建完成: 成功 {created_count}, 失败 {failed_count}")
        return created_count > 0
    
    def create_random_users(self, count: int = 10):
        """
        创建随机测试用户
        
        Args:
            count: 创建用户数量
        """
        print(f"创建 {count} 个随机测试用户...")
        
        created_count = 0
        failed_count = 0
        
        for i in range(count):
            try:
                # 生成随机用户名
                prefix = random.choice(self.username_prefixes)
                suffix = random.randint(100, 999)
                username = f"{prefix}{suffix}"
                
                # 生成随机密码
                password = self._generate_random_password()
                
                # 检查用户是否已存在
                existing_user = self.user_manager.get_user_info(username)
                if existing_user:
                    continue
                
                # 创建用户
                result = self.user_manager.create_user(username, password)
                
                if result["success"]:
                    print(f"  √ 创建随机用户: {username} / {password}")
                    created_count += 1
                else:
                    print(f"  × 创建随机用户失败: {username} - {result['message']}")
                    failed_count += 1
                    
            except Exception as e:
                print(f"  × 创建随机用户异常: {e}")
                failed_count += 1
        
        print(f"随机用户创建完成: 成功 {created_count}, 失败 {failed_count}")
        return created_count > 0
    
    def _generate_random_password(self, length: int = 8):
        """
        生成随机密码
        
        Args:
            length: 密码长度
            
        Returns:
            str: 随机密码
        """
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))
    
    def create_test_sessions(self, count: int = 5):
        """
        创建测试会话
        
        Args:
            count: 创建会话数量
        """
        print(f"创建 {count} 个测试会话...")
        
        # 获取所有用户
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute("SELECT username FROM users LIMIT ?", (count,))
                users = [row['username'] for row in cursor.fetchall()]
        except Exception as e:
            print(f"× 获取用户列表失败: {e}")
            return False
        
        if not users:
            print("× 没有可用的用户创建会话")
            return False
        
        created_count = 0
        failed_count = 0
        
        for username in users:
            try:
                # 创建会话
                result = self.session_manager.create_session(username, "127.0.0.1")
                
                if result["success"]:
                    session_token = result["session_token"]
                    print(f"  √ 创建会话: {username} -> {session_token[:16]}...")
                    created_count += 1
                else:
                    print(f"  × 创建会话失败: {username} - {result['message']}")
                    failed_count += 1
                    
            except Exception as e:
                print(f"  × 创建会话异常: {username} - {e}")
                failed_count += 1
        
        print(f"测试会话创建完成: 成功 {created_count}, 失败 {failed_count}")
        return created_count > 0
    
    def create_test_logs(self, count: int = 20):
        """
        创建测试日志条目
        
        Args:
            count: 创建日志条目数量
        """
        print(f"创建 {count} 个测试日志条目...")
        
        log_types = ['INFO', 'WARNING', 'ERROR', 'DEBUG']
        log_messages = [
            "用户登录成功",
            "用户登录失败",
            "会话创建",
            "会话过期",
            "密码修改",
            "用户创建",
            "用户删除",
            "系统启动",
            "系统关闭",
            "配置更新",
            "数据库连接",
            "网络错误",
            "认证失败",
            "权限检查",
            "数据备份"
        ]
        
        created_count = 0
        
        try:
            with self.db_manager.get_connection() as conn:
                for i in range(count):
                    # 生成随机日志数据
                    log_type = random.choice(log_types)
                    message = random.choice(log_messages)
                    timestamp = datetime.now() - timedelta(
                        days=random.randint(0, 30),
                        hours=random.randint(0, 23),
                        minutes=random.randint(0, 59)
                    )
                    
                    # 插入日志记录
                    conn.execute("""
                        INSERT INTO logs (timestamp, level, message, source)
                        VALUES (?, ?, ?, ?)
                    """, (timestamp.isoformat(), log_type, message, "test_data"))
                    
                    created_count += 1
                
                conn.commit()
                print(f"√ 测试日志创建完成: {created_count} 条")
                return True
                
        except Exception as e:
            print(f"× 创建测试日志失败: {e}")
            return False
    
    def create_performance_data(self):
        """创建性能测试数据"""
        print("创建性能测试数据...")
        
        try:
            # 创建大量用户进行性能测试
            bulk_users = []
            for i in range(100):
                username = f"perf_user_{i:03d}"
                password = f"perf_pass_{i:03d}"
                bulk_users.append((username, password))
            
            created_count = 0
            
            with self.db_manager.get_connection() as conn:
                for username, password in bulk_users:
                    # 检查用户是否已存在
                    cursor = conn.execute("SELECT id FROM users WHERE username = ?", (username,))
                    if cursor.fetchone():
                        continue
                    
                    # 创建用户（简化版，直接插入数据库）
                    import hashlib
                    password_hash = hashlib.sha256(password.encode()).hexdigest()
                    
                    conn.execute("""
                        INSERT INTO users (username, password_hash, created_at, last_login)
                        VALUES (?, ?, ?, ?)
                    """, (username, password_hash, datetime.now().isoformat(), None))
                    
                    created_count += 1
                
                conn.commit()
            
            print(f"√ 性能测试用户创建完成: {created_count} 个")
            return True
            
        except Exception as e:
            print(f"× 创建性能测试数据失败: {e}")
            return False
    
    def show_statistics(self):
        """显示数据库统计信息"""
        print("\n数据库统计信息:")
        print("-" * 30)
        
        try:
            with self.db_manager.get_connection() as conn:
                # 用户统计
                cursor = conn.execute("SELECT COUNT(*) as count FROM users")
                user_count = cursor.fetchone()['count']
                print(f"用户总数: {user_count}")
                
                # 会话统计
                cursor = conn.execute("SELECT COUNT(*) as count FROM sessions")
                session_count = cursor.fetchone()['count']
                print(f"会话总数: {session_count}")
                
                # 活跃会话统计
                cursor = conn.execute("""
                    SELECT COUNT(*) as count FROM sessions 
                    WHERE expires_at > datetime('now')
                """)
                active_session_count = cursor.fetchone()['count']
                print(f"活跃会话: {active_session_count}")
                
                # 日志统计
                cursor = conn.execute("SELECT COUNT(*) as count FROM logs")
                log_count = cursor.fetchone()['count']
                print(f"日志条目: {log_count}")
                
                # 最近创建的用户
                cursor = conn.execute("""
                    SELECT username, created_at FROM users 
                    ORDER BY created_at DESC LIMIT 5
                """)
                recent_users = cursor.fetchall()
                
                if recent_users:
                    print("\n最近创建的用户:")
                    for user in recent_users:
                        print(f"  - {user['username']} ({user['created_at']})")
                
        except Exception as e:
            print(f"× 获取统计信息失败: {e}")
    
    def export_user_list(self, filename: str = "test_users.txt"):
        """
        导出用户列表
        
        Args:
            filename: 导出文件名
        """
        print(f"导出用户列表到 {filename}...")
        
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT username, created_at, last_login 
                    FROM users 
                    ORDER BY username
                """)
                users = cursor.fetchall()
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("网络验证工具测试用户列表\n")
                f.write("=" * 50 + "\n")
                f.write(f"导出时间: {datetime.now().isoformat()}\n")
                f.write(f"用户总数: {len(users)}\n\n")
                
                f.write("基本测试用户:\n")
                f.write("-" * 30 + "\n")
                for user_data in self.test_users:
                    f.write(f"用户名: {user_data['username']}\n")
                    f.write(f"密码: {user_data['password']}\n")
                    f.write(f"角色: {user_data['role']}\n\n")
                
                f.write("所有用户列表:\n")
                f.write("-" * 30 + "\n")
                for user in users:
                    f.write(f"用户名: {user['username']}\n")
                    f.write(f"创建时间: {user['created_at']}\n")
                    f.write(f"最后登录: {user['last_login'] or '从未登录'}\n\n")
            
            print(f"√ 用户列表已导出到 {filename}")
            return True
            
        except Exception as e:
            print(f"× 导出用户列表失败: {e}")
            return False
    
    def cleanup_test_data(self):
        """清理测试数据"""
        print("清理测试数据...")
        
        try:
            with self.db_manager.get_connection() as conn:
                # 删除测试用户（保留admin）
                cursor = conn.execute("""
                    DELETE FROM users 
                    WHERE username != 'admin' 
                    AND (username LIKE 'test_%' 
                         OR username LIKE 'demo_%' 
                         OR username LIKE 'user%' 
                         OR username LIKE 'perf_%'
                         OR username IN ('guest', 'manager', 'operator'))
                """)
                deleted_users = cursor.rowcount
                
                # 删除过期会话
                cursor = conn.execute("""
                    DELETE FROM sessions 
                    WHERE expires_at < datetime('now')
                """)
                deleted_sessions = cursor.rowcount
                
                # 删除测试日志
                cursor = conn.execute("""
                    DELETE FROM logs 
                    WHERE source = 'test_data'
                """)
                deleted_logs = cursor.rowcount
                
                conn.commit()
            
            print(f"√ 清理完成:")
            print(f"  - 删除用户: {deleted_users}")
            print(f"  - 删除会话: {deleted_sessions}")
            print(f"  - 删除日志: {deleted_logs}")
            
            return True
            
        except Exception as e:
            print(f"× 清理测试数据失败: {e}")
            return False
    
    def create_all_test_data(self):
        """创建所有测试数据"""
        print("创建完整的测试数据集...")
        print("=" * 50)
        
        success_count = 0
        total_steps = 6
        
        # 1. 创建基本用户
        if self.create_basic_users():
            success_count += 1
        
        # 2. 创建随机用户
        if self.create_random_users(15):
            success_count += 1
        
        # 3. 创建测试会话
        if self.create_test_sessions(8):
            success_count += 1
        
        # 4. 创建测试日志
        if self.create_test_logs(50):
            success_count += 1
        
        # 5. 创建性能测试数据
        if self.create_performance_data():
            success_count += 1
        
        # 6. 导出用户列表
        if self.export_user_list():
            success_count += 1
        
        print("\n" + "=" * 50)
        print(f"测试数据创建完成: {success_count}/{total_steps} 步骤成功")
        
        # 显示统计信息
        self.show_statistics()
        
        return success_count == total_steps


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='网络验证工具测试数据创建器')
    parser.add_argument('--db', default='data/auth.db', help='数据库文件路径')
    parser.add_argument('--users', type=int, default=10, help='创建随机用户数量')
    parser.add_argument('--sessions', type=int, default=5, help='创建测试会话数量')
    parser.add_argument('--logs', type=int, default=20, help='创建测试日志数量')
    parser.add_argument('--cleanup', action='store_true', help='清理测试数据')
    parser.add_argument('--stats', action='store_true', help='仅显示统计信息')
    parser.add_argument('--export', action='store_true', help='导出用户列表')
    parser.add_argument('--all', action='store_true', help='创建所有测试数据')
    
    args = parser.parse_args()
    
    try:
        print("网络验证工具测试数据创建器")
        print("=" * 50)
        
        # 创建测试数据创建器
        creator = TestDataCreator(args.db)
        
        # 初始化组件
        if not creator.initialize_components():
            return 1
        
        # 根据参数执行相应操作
        if args.cleanup:
            creator.cleanup_test_data()
        elif args.stats:
            creator.show_statistics()
        elif args.export:
            creator.export_user_list()
        elif args.all:
            creator.create_all_test_data()
        else:
            # 默认操作：创建基本测试数据
            creator.create_basic_users()
            if args.users > 0:
                creator.create_random_users(args.users)
            if args.sessions > 0:
                creator.create_test_sessions(args.sessions)
            if args.logs > 0:
                creator.create_test_logs(args.logs)
            creator.show_statistics()
        
        return 0
        
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return 1
    except Exception as e:
        print(f"操作失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
"""
认证处理器模块
实现认证核心逻辑、用户名密码验证和账户锁定机制
"""

import hashlib
import secrets
import threading
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from server.database_manager import DatabaseManager
from server.user_manager import UserManager
from server.session_manager import SessionManager
from common.interfaces import IAuthentication<PERSON>andler
from common.exceptions import AuthenticationError, DatabaseError
from common.constants import DEFAULT_CONFIG, AUTH_STATUS


class AuthenticationHandler(IAuthenticationHandler):
    """认证处理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化认证处理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.user_manager = UserManager(db_manager)
        self.session_manager = SessionManager(db_manager)
        
        # 认证配置
        self.max_failed_attempts = DEFAULT_CONFIG['security']['max_failed_attempts']
        self.lockout_duration = DEFAULT_CONFIG['security']['lockout_duration']
        self.session_timeout = DEFAULT_CONFIG['server']['session_timeout']
        
        # 线程安全锁
        self._lock = threading.Lock()
        
        # 认证统计
        self.auth_stats = {
            'total_attempts': 0,
            'successful_auths': 0,
            'failed_auths': 0,
            'locked_accounts': 0,
            'active_sessions': 0,
            'start_time': datetime.now().isoformat()
        }
    
    def authenticate_user(self, username: str, password: str, client_ip: str = None) -> Dict[str, Any]:
        """
        用户认证主方法
        
        Args:
            username: 用户名
            password: 密码
            client_ip: 客户端IP地址
            
        Returns:
            Dict[str, Any]: 认证结果
        """
        with self._lock:
            self.auth_stats['total_attempts'] += 1
        
        try:
            # 输入验证
            validation_result = self._validate_credentials(username, password)
            if not validation_result['valid']:
                self._record_failed_auth(username, client_ip, validation_result['message'])
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'status': AUTH_STATUS['FAILED']
                }
            
            # 执行认证
            auth_result = self.user_manager.authenticate_user(username, password, client_ip)
            
            if auth_result['success']:
                # 认证成功，创建会话
                session_result = self.session_manager.create_session(
                    auth_result['user_id'], 
                    client_ip
                )
                
                if session_result['success']:
                    # 更新统计
                    with self._lock:
                        self.auth_stats['successful_auths'] += 1
                        self.auth_stats['active_sessions'] += 1
                    
                    # 记录成功认证
                    self._record_successful_auth(username, client_ip, session_result['session_token'])
                    
                    return {
                        'success': True,
                        'message': '认证成功',
                        'status': AUTH_STATUS['SUCCESS'],
                        'user_id': auth_result['user_id'],
                        'username': username,
                        'session_token': session_result['session_token'],
                        'expires_at': session_result['expires_at'],
                        'timeout': session_result['timeout'],
                        'last_login': auth_result.get('last_login')
                    }
                else:
                    # 会话创建失败
                    self._record_failed_auth(username, client_ip, '会话创建失败')
                    return {
                        'success': False,
                        'message': f'认证成功但创建会话失败: {session_result["message"]}',
                        'status': AUTH_STATUS['FAILED']
                    }
            else:
                # 认证失败
                with self._lock:
                    self.auth_stats['failed_auths'] += 1
                    if auth_result.get('locked'):
                        self.auth_stats['locked_accounts'] += 1
                
                self._record_failed_auth(username, client_ip, auth_result['message'])
                
                return {
                    'success': False,
                    'message': auth_result['message'],
                    'status': AUTH_STATUS['LOCKED'] if auth_result.get('locked') else AUTH_STATUS['FAILED'],
                    'locked': auth_result.get('locked', False),
                    'remaining_attempts': auth_result.get('remaining_attempts'),
                    'remaining_time': auth_result.get('remaining_time')
                }
                
        except Exception as e:
            self._record_failed_auth(username, client_ip, f'认证异常: {str(e)}')
            return {
                'success': False,
                'message': f'认证过程中发生错误: {str(e)}',
                'status': AUTH_STATUS['FAILED']
            }
    
    def _validate_credentials(self, username: str, password: str) -> Dict[str, Any]:
        """
        验证凭据格式
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        # 检查用户名
        if not username or not isinstance(username, str):
            return {'valid': False, 'message': '用户名不能为空'}
        
        username = username.strip()
        if len(username) < 3 or len(username) > 50:
            return {'valid': False, 'message': '用户名长度必须在3-50字符之间'}
        
        # 检查密码
        if not password or not isinstance(password, str):
            return {'valid': False, 'message': '密码不能为空'}
        
        if len(password) < DEFAULT_CONFIG['security']['password_min_length']:
            return {'valid': False, 'message': f'密码长度至少{DEFAULT_CONFIG["security"]["password_min_length"]}位'}
        
        return {'valid': True, 'message': '凭据格式正确'}
    
    def hash_password(self, password: str, salt: str) -> str:
        """
        使用SHA-256和盐值对密码进行哈希
        
        Args:
            password: 原始密码
            salt: 盐值
            
        Returns:
            str: 哈希后的密码
        """
        return self.user_manager.hash_password(password, salt)
    
    def generate_session_token(self) -> str:
        """
        生成会话令牌
        
        Returns:
            str: 会话令牌
        """
        return self.session_manager.generate_session_token()
    
    def validate_session(self, token: str) -> bool:
        """
        验证会话令牌
        
        Args:
            token: 会话令牌
            
        Returns:
            bool: 会话有效返回True
        """
        validation_result = self.session_manager.validate_session(token)
        return validation_result['valid']
    
    def get_session_info(self, token: str) -> Optional[Dict[str, Any]]:
        """
        获取会话信息
        
        Args:
            token: 会话令牌
            
        Returns:
            Optional[Dict[str, Any]]: 会话信息
        """
        validation_result = self.session_manager.validate_session(token)
        if validation_result['valid']:
            return {
                'session_id': validation_result['session_id'],
                'user_id': validation_result['user_id'],
                'username': validation_result['username'],
                'expires_at': validation_result['expires_at'],
                'client_ip': validation_result['client_ip']
            }
        return None
    
    def refresh_session(self, token: str) -> Dict[str, Any]:
        """
        刷新会话
        
        Args:
            token: 会话令牌
            
        Returns:
            Dict[str, Any]: 刷新结果
        """
        return self.session_manager.refresh_session(token)
    
    def logout_user(self, token: str) -> Dict[str, Any]:
        """
        用户登出
        
        Args:
            token: 会话令牌
            
        Returns:
            Dict[str, Any]: 登出结果
        """
        # 销毁会话
        destroy_result = self.session_manager.destroy_session(token)
        
        if destroy_result['success']:
            with self._lock:
                self.auth_stats['active_sessions'] = max(0, self.auth_stats['active_sessions'] - 1)
            
            return {
                'success': True,
                'message': '登出成功'
            }
        else:
            return {
                'success': False,
                'message': destroy_result['message']
            }
    
    def create_user(self, username: str, password: str, created_by: str = None) -> Dict[str, Any]:
        """
        创建新用户
        
        Args:
            username: 用户名
            password: 密码
            created_by: 创建者
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        result = self.user_manager.create_user(username, password)
        
        if result['success']:
            # 记录用户创建日志
            self._log_auth_event(
                'USER_CREATED',
                username,
                None,
                f'用户由 {created_by or "系统"} 创建'
            )
        
        return result
    
    def delete_user(self, username: str, deleted_by: str = None) -> Dict[str, Any]:
        """
        删除用户
        
        Args:
            username: 用户名
            deleted_by: 删除者
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        # 先获取用户信息
        user_info = self.user_manager.get_user_info(username)
        if not user_info:
            return {
                'success': False,
                'message': '用户不存在'
            }
        
        # 销毁用户的所有会话
        self.session_manager.destroy_user_sessions(user_info['id'])
        
        # 删除用户
        result = self.user_manager.delete_user(username)
        
        if result['success']:
            # 记录用户删除日志
            self._log_auth_event(
                'USER_DELETED',
                username,
                None,
                f'用户由 {deleted_by or "系统"} 删除'
            )
        
        return result
    
    def change_password(self, username: str, new_password: str, changed_by: str = None) -> Dict[str, Any]:
        """
        修改用户密码
        
        Args:
            username: 用户名
            new_password: 新密码
            changed_by: 修改者
            
        Returns:
            Dict[str, Any]: 修改结果
        """
        # 获取用户信息
        user_info = self.user_manager.get_user_info(username)
        if not user_info:
            return {
                'success': False,
                'message': '用户不存在'
            }
        
        # 修改密码
        result = self.user_manager.update_user_password(username, new_password)
        
        if result['success']:
            # 销毁用户的所有现有会话（强制重新登录）
            self.session_manager.destroy_user_sessions(user_info['id'])
            
            # 记录密码修改日志
            self._log_auth_event(
                'PASSWORD_CHANGED',
                username,
                None,
                f'密码由 {changed_by or "系统"} 修改'
            )
        
        return result
    
    def unlock_user(self, username: str, unlocked_by: str = None) -> Dict[str, Any]:
        """
        解锁用户账户
        
        Args:
            username: 用户名
            unlocked_by: 解锁者
            
        Returns:
            Dict[str, Any]: 解锁结果
        """
        try:
            # 重置失败次数和锁定状态
            if self.db_manager.update_user(
                username,
                failed_attempts=0,
                locked_until=None
            ):
                # 记录解锁日志
                self._log_auth_event(
                    'USER_UNLOCKED',
                    username,
                    None,
                    f'用户由 {unlocked_by or "系统"} 解锁'
                )
                
                return {
                    'success': True,
                    'message': '用户已解锁'
                }
            else:
                return {
                    'success': False,
                    'message': '用户不存在或解锁失败'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'解锁用户失败: {str(e)}'
            }
    
    def get_user_list(self) -> List[Dict[str, Any]]:
        """
        获取用户列表
        
        Returns:
            List[Dict[str, Any]]: 用户列表
        """
        return self.user_manager.get_all_users()
    
    def get_active_sessions(self) -> List[Dict[str, Any]]:
        """
        获取活跃会话列表
        
        Returns:
            List[Dict[str, Any]]: 活跃会话列表
        """
        return self.session_manager.get_active_sessions()
    
    def get_auth_statistics(self) -> Dict[str, Any]:
        """
        获取认证统计信息
        
        Returns:
            Dict[str, Any]: 认证统计信息
        """
        with self._lock:
            stats = self.auth_stats.copy()
        
        # 获取会话统计
        session_stats = self.session_manager.get_session_statistics()
        stats.update(session_stats)
        
        # 计算成功率
        if stats['total_attempts'] > 0:
            stats['success_rate'] = (stats['successful_auths'] / stats['total_attempts']) * 100
        else:
            stats['success_rate'] = 0
        
        # 计算运行时间
        start_time = datetime.fromisoformat(stats['start_time'])
        uptime_seconds = int((datetime.now() - start_time).total_seconds())
        stats['uptime_seconds'] = uptime_seconds
        
        return stats
    
    def _record_successful_auth(self, username: str, client_ip: str, session_token: str):
        """
        记录成功认证
        
        Args:
            username: 用户名
            client_ip: 客户端IP
            session_token: 会话令牌
        """
        self._log_auth_event(
            'AUTH_SUCCESS',
            username,
            client_ip,
            f'认证成功，会话: {session_token[:8]}...'
        )
    
    def _record_failed_auth(self, username: str, client_ip: str, reason: str):
        """
        记录失败认证
        
        Args:
            username: 用户名
            client_ip: 客户端IP
            reason: 失败原因
        """
        self._log_auth_event(
            'AUTH_FAILED',
            username,
            client_ip,
            f'认证失败: {reason}'
        )
    
    def _log_auth_event(self, event_type: str, username: str, client_ip: str, message: str):
        """
        记录认证事件到数据库
        
        Args:
            event_type: 事件类型
            username: 用户名
            client_ip: 客户端IP
            message: 消息
        """
        try:
            with self.db_manager.get_connection() as conn:
                conn.execute("""
                    INSERT INTO logs (level, message, client_ip, username)
                    VALUES (?, ?, ?, ?)
                """, ('INFO', f'[{event_type}] {message}', client_ip, username))
                conn.commit()
        except Exception as e:
            print(f"记录认证事件失败: {e}")
    
    def cleanup_expired_sessions(self) -> Dict[str, Any]:
        """
        清理过期会话
        
        Returns:
            Dict[str, Any]: 清理结果
        """
        result = self.session_manager.cleanup_expired_sessions()
        
        if result['success']:
            with self._lock:
                # 更新活跃会话统计
                session_stats = self.session_manager.get_session_statistics()
                self.auth_stats['active_sessions'] = session_stats['active_sessions']
        
        return result
    
    def is_user_locked(self, username: str) -> bool:
        """
        检查用户是否被锁定
        
        Args:
            username: 用户名
            
        Returns:
            bool: 用户被锁定返回True
        """
        user_info = self.user_manager.get_user_info(username)
        return user_info and user_info.get('is_locked', False) if user_info else False
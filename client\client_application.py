"""
客户端主应用程序
整合客户端所有模块，实现完整的网络验证客户端
"""

import sys
import argparse
from typing import Dict, Any

from client.client_gui import ClientGUI
from client.network_client import NetworkClient
from client.auth_result_handler import AuthResultHandler


class ClientApplication:
    """客户端应用程序"""
    
    def __init__(self):
        """初始化客户端应用程序"""
        self.network_client = NetworkClient()
        self.auth_handler = AuthResultHandler()
        self.gui = None
    
    def run_gui(self):
        """运行GUI模式"""
        try:
            print("启动网络验证客户端...")
            self.gui = ClientGUI(self)
            self.gui.run()
            
        except Exception as e:
            print(f"运行GUI失败: {e}")
            sys.exit(1)
    
    def connect(self, host: str = "127.0.0.1", port: int = 8888) -> Dict[str, Any]:
        """连接到服务器"""
        return self.network_client.connect(host, port)
    
    def disconnect(self) -> Dict[str, Any]:
        """断开连接"""
        return self.network_client.disconnect()
    
    def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """用户认证"""
        return self.network_client.authenticate(username, password)
    
    def register_user(self, username: str, password: str, email: str = None) -> Dict[str, Any]:
        """注册用户"""
        return self.network_client.register_user(username, password, email)
    
    def use_recharge_card(self, card_code: str) -> Dict[str, Any]:
        """使用充值卡"""
        return self.network_client.use_recharge_card(card_code)
    
    def get_remaining_time(self) -> Dict[str, Any]:
        """获取剩余时间"""
        return self.network_client.get_remaining_time()
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.network_client.is_connected()
    
    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return self.network_client.is_authenticated()
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return self.network_client.get_connection_info()
    
    def run_console(self, host: str = "127.0.0.1", port: int = 8888):
        """
        运行控制台模式
        
        Args:
            host: 服务器地址
            port: 服务器端口
        """
        try:
            print("网络验证工具客户端 - 控制台模式")
            print(f"目标服务器: {host}:{port}")
            print("输入 'help' 查看可用命令")
            
            while True:
                try:
                    command = input("client> ").strip().lower()
                    
                    if command == 'help':
                        self._show_console_help()
                    elif command == 'connect':
                        self._console_connect(host, port)
                    elif command == 'disconnect':
                        self._console_disconnect()
                    elif command == 'login':
                        self._console_login()
                    elif command == 'logout':
                        self._console_logout()
                    elif command == 'status':
                        self._console_status()
                    elif command == 'ping':
                        self._console_ping()
                    elif command in ['quit', 'exit']:
                        break
                    elif command:
                        print(f"未知命令: {command}")
                        
                except KeyboardInterrupt:
                    break
                except EOFError:
                    break
                except Exception as e:
                    print(f"执行命令失败: {e}")
            
            # 清理连接
            if self.network_client.is_connected():
                self.network_client.disconnect()
            
            print("客户端已退出")
            
        except Exception as e:
            print(f"控制台模式运行失败: {e}")
    
    def _show_console_help(self):
        """显示控制台帮助"""
        help_text = """
可用命令:
  connect    - 连接到服务器
  disconnect - 断开连接
  login      - 用户登录
  logout     - 用户登出
  status     - 显示连接状态
  ping       - 发送心跳测试
  help       - 显示此帮助信息
  quit       - 退出程序
        """
        print(help_text)
    
    def _console_connect(self, host: str, port: int):
        """控制台连接命令"""
        if self.network_client.is_connected():
            print("已经连接到服务器")
            return
        
        print(f"正在连接到 {host}:{port}...")
        result = self.network_client.connect(host, port)
        
        if result['success']:
            print("连接成功")
            if 'welcome_message' in result and result['welcome_message']:
                welcome = result['welcome_message']
                print(f"服务器消息: {welcome.get('message', '')}")
        else:
            print(f"连接失败: {result['message']}")
    
    def _console_disconnect(self):
        """控制台断开连接命令"""
        if not self.network_client.is_connected():
            print("未连接到服务器")
            return
        
        result = self.network_client.disconnect()
        print(result['message'])
    
    def _console_login(self):
        """控制台登录命令"""
        if not self.network_client.is_connected():
            print("请先连接到服务器")
            return
        
        if self.network_client.is_authenticated():
            print("已经登录")
            return
        
        try:
            username = input("用户名: ").strip()
            if not username:
                print("用户名不能为空")
                return
            
            import getpass
            password = getpass.getpass("密码: ")
            if not password:
                print("密码不能为空")
                return
            
            print("正在认证...")
            result = self.network_client.authenticate(username, password)
            
            if result['success']:
                print(f"登录成功，欢迎 {result.get('username', username)}!")
                if 'expires_at' in result:
                    print(f"会话过期时间: {result['expires_at']}")
            else:
                print(f"登录失败: {result['message']}")
                
                # 显示额外信息
                if 'remaining_attempts' in result:
                    print(f"剩余尝试次数: {result['remaining_attempts']}")
                
                if 'remaining_time' in result:
                    print(f"账户锁定剩余时间: {result['remaining_time']}秒")
                
        except Exception as e:
            print(f"登录过程中发生错误: {e}")
    
    def _console_logout(self):
        """控制台登出命令"""
        if not self.network_client.is_authenticated():
            print("未登录")
            return
        
        result = self.network_client.logout()
        print(result.get('message', '已登出'))
    
    def _console_status(self):
        """控制台状态命令"""
        conn_info = self.network_client.get_connection_info()
        stats = self.network_client.get_statistics()
        
        print("客户端状态:")
        print(f"  连接状态: {'已连接' if conn_info['is_connected'] else '未连接'}")
        
        if conn_info['is_connected']:
            print(f"  服务器: {conn_info['host']}:{conn_info['port']}")
            print(f"  认证状态: {'已认证' if conn_info['is_authenticated'] else '未认证'}")
            
            if conn_info['user_info']:
                user_info = conn_info['user_info']
                print(f"  用户名: {user_info.get('username', 'N/A')}")
            
            print(f"  发送消息数: {stats['total_messages_sent']}")
            print(f"  接收消息数: {stats['total_messages_received']}")
            
            if stats['uptime_seconds'] > 0:
                uptime = stats['uptime_seconds']
                hours = uptime // 3600
                minutes = (uptime % 3600) // 60
                seconds = uptime % 60
                print(f"  连接时长: {hours:02d}:{minutes:02d}:{seconds:02d}")
    
    def _console_ping(self):
        """控制台心跳命令"""
        if not self.network_client.is_connected():
            print("请先连接到服务器")
            return
        
        print("发送心跳...")
        result = self.network_client.send_heartbeat()
        
        if result['success']:
            print("心跳响应正常")
            if 'server_time' in result:
                print(f"服务器时间: {result['server_time']}")
            if result.get('session_refreshed'):
                print("会话已刷新")
        else:
            print(f"心跳失败: {result['message']}")
    
    def run_test_mode(self, host: str = "127.0.0.1", port: int = 8888, 
                     username: str = "admin", password: str = "admin123"):
        """
        运行测试模式
        
        Args:
            host: 服务器地址
            port: 服务器端口
            username: 测试用户名
            password: 测试密码
        """
        try:
            print("网络验证工具客户端 - 测试模式")
            print(f"测试参数: {host}:{port}, 用户: {username}")
            
            # 连接测试
            print("\n1. 连接测试...")
            result = self.network_client.connect(host, port)
            if result['success']:
                print("√ 连接成功")
            else:
                print(f"× 连接失败: {result['message']}")
                return
            
            # 认证测试
            print("\n2. 认证测试...")
            result = self.network_client.authenticate(username, password)
            if result['success']:
                print(f"√ 认证成功，用户: {result.get('username')}")
            else:
                print(f"× 认证失败: {result['message']}")
            
            # 心跳测试
            print("\n3. 心跳测试...")
            result = self.network_client.send_heartbeat()
            if result['success']:
                print("√ 心跳正常")
            else:
                print(f"× 心跳失败: {result['message']}")
            
            # 登出测试
            if self.network_client.is_authenticated():
                print("\n4. 登出测试...")
                result = self.network_client.logout()
                print("√ 已登出")
            
            # 断开连接
            print("\n5. 断开连接...")
            result = self.network_client.disconnect()
            print("√ 已断开连接")
            
            print("\n测试完成")
            
        except Exception as e:
            print(f"测试模式运行失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='网络验证工具客户端')
    parser.add_argument('--host', '-H', default='127.0.0.1', help='服务器地址')
    parser.add_argument('--port', '-p', type=int, default=8888, help='服务器端口')
    parser.add_argument('--console', action='store_true', help='使用控制台模式')
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    parser.add_argument('--username', '-u', default='admin', help='测试用户名')
    parser.add_argument('--password', '-P', default='admin123', help='测试密码')
    
    args = parser.parse_args()
    
    try:
        # 创建客户端应用程序
        app = ClientApplication()
        
        if args.test:
            # 测试模式
            app.run_test_mode(args.host, args.port, args.username, args.password)
        elif args.console:
            # 控制台模式
            app.run_console(args.host, args.port)
        else:
            # GUI模式
            app.run_gui()
            
    except Exception as e:
        print(f"启动客户端应用程序失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
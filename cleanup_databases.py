#!/usr/bin/env python3
"""
清理多余的测试数据库文件
"""

import os
import glob
from pathlib import Path


def cleanup_test_databases():
    """清理测试数据库文件"""
    print("=" * 50)
    print("清理多余的测试数据库文件")
    print("=" * 50)
    
    # 定义要保留的数据库文件
    keep_databases = {
        'auth.db',  # 主数据库
        '.gitkeep'  # Git占位文件
    }
    
    # 定义测试数据库模式
    test_patterns = [
        'test_*.db',
        'debug*.db',
        'demo_*.db',
        '*_test.db',
        'temp*.db'
    ]
    
    data_dir = Path('data')
    
    if not data_dir.exists():
        print("data目录不存在")
        return
    
    # 收集所有要删除的文件
    files_to_delete = []
    
    # 扫描所有数据库文件
    for file_path in data_dir.glob('*.db'):
        filename = file_path.name
        
        # 检查是否应该保留
        if filename in keep_databases:
            print(f"保留: {filename}")
            continue
        
        # 检查是否匹配测试模式
        is_test_file = False
        for pattern in test_patterns:
            if file_path.match(pattern):
                is_test_file = True
                break
        
        if is_test_file:
            files_to_delete.append(file_path)
        else:
            print(f"未知文件: {filename} (将保留)")
    
    # 显示要删除的文件
    if files_to_delete:
        print(f"\n找到 {len(files_to_delete)} 个测试数据库文件:")
        for file_path in files_to_delete:
            file_size = file_path.stat().st_size
            print(f"  - {file_path.name} ({file_size} bytes)")
        
        # 确认删除
        response = input(f"\n确定要删除这 {len(files_to_delete)} 个文件吗? (y/N): ")
        
        if response.lower() in ['y', 'yes']:
            deleted_count = 0
            total_size = 0
            
            for file_path in files_to_delete:
                try:
                    file_size = file_path.stat().st_size
                    file_path.unlink()
                    print(f"✓ 已删除: {file_path.name}")
                    deleted_count += 1
                    total_size += file_size
                except Exception as e:
                    print(f"✗ 删除失败: {file_path.name} - {e}")
            
            print(f"\n删除完成:")
            print(f"  - 删除文件数: {deleted_count}")
            print(f"  - 释放空间: {total_size} bytes ({total_size/1024:.1f} KB)")
        else:
            print("取消删除操作")
    else:
        print("\n没有找到需要删除的测试数据库文件")
    
    # 显示剩余文件
    remaining_files = list(data_dir.glob('*'))
    if remaining_files:
        print(f"\ndata目录中剩余文件:")
        for file_path in remaining_files:
            if file_path.is_file():
                file_size = file_path.stat().st_size
                print(f"  - {file_path.name} ({file_size} bytes)")
            else:
                print(f"  - {file_path.name}/ (目录)")


def cleanup_temp_files():
    """清理其他临时文件"""
    print("\n" + "=" * 50)
    print("清理其他临时文件")
    print("=" * 50)
    
    # 定义要清理的临时文件模式
    temp_patterns = [
        'test_config*.ini',
        '*.tmp',
        '*.temp',
        '__pycache__',
        '*.pyc'
    ]
    
    temp_files = []
    
    # 扫描根目录
    for pattern in temp_patterns:
        temp_files.extend(glob.glob(pattern))
    
    if temp_files:
        print(f"找到 {len(temp_files)} 个临时文件:")
        for file_path in temp_files:
            if os.path.isfile(file_path):
                file_size = os.path.getsize(file_path)
                print(f"  - {file_path} ({file_size} bytes)")
            else:
                print(f"  - {file_path}/ (目录)")
        
        response = input(f"\n确定要删除这些临时文件吗? (y/N): ")
        
        if response.lower() in ['y', 'yes']:
            deleted_count = 0
            
            for file_path in temp_files:
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        print(f"✓ 已删除文件: {file_path}")
                    elif os.path.isdir(file_path):
                        import shutil
                        shutil.rmtree(file_path)
                        print(f"✓ 已删除目录: {file_path}")
                    deleted_count += 1
                except Exception as e:
                    print(f"✗ 删除失败: {file_path} - {e}")
            
            print(f"\n删除了 {deleted_count} 个临时文件")
        else:
            print("取消删除操作")
    else:
        print("没有找到临时文件")


def main():
    """主函数"""
    print("开始清理多余文件...")
    
    try:
        # 清理测试数据库
        cleanup_test_databases()
        
        # 清理临时文件
        cleanup_temp_files()
        
        print("\n" + "=" * 50)
        print("✅ 清理完成！")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n❌ 清理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
# 用户账号管理功能设计文档

## 概述

本设计文档描述了网络验证工具的用户账号管理系统，包括用户注册、充值、改密、余额管理等功能的技术实现方案。

## 架构

### 核心组件

1. **UserRegistrationManager** - 用户注册管理器
2. **AccountBalanceManager** - 账户余额管理器
3. **PasswordManager** - 密码管理器
4. **BillingManager** - 计费管理器
5. **UserProfileManager** - 用户信息管理器
6. **AdminUserManager** - 管理员用户管理器

### 组件关系

```mermaid
graph TD
    A[客户端GUI] --> B[UserRegistrationManager]
    A --> C[AccountBalanceManager]
    A --> D[PasswordManager]
    A --> E[UserProfileManager]
    
    F[服务器] --> G[BillingManager]
    F --> H[AdminUserManager]
    
    B --> I[DatabaseManager]
    C --> I
    D --> I
    E --> I
    G --> I
    H --> I
    
    G --> J[SessionManager]
    J --> K[用户会话]
```

## 组件和接口

### 数据库扩展

需要扩展现有数据库结构：

```sql
-- 扩展用户表
ALTER TABLE users ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00;
ALTER TABLE users ADD COLUMN registration_date DATETIME DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT 'active';
ALTER TABLE users ADD COLUMN email VARCHAR(255);

-- 充值记录表
CREATE TABLE recharge_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    recharge_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    recharge_type VARCHAR(20) DEFAULT 'manual',
    operator VARCHAR(50),
    description TEXT,
    FOREIGN KEY (username) REFERENCES users(username)
);

-- 消费记录表
CREATE TABLE consumption_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    consumption_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    service_type VARCHAR(50),
    duration INTEGER,
    description TEXT,
    FOREIGN KEY (username) REFERENCES users(username)
);

-- 密码修改记录表
CREATE TABLE password_change_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL,
    change_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    old_password_hash VARCHAR(255),
    ip_address VARCHAR(45),
    FOREIGN KEY (username) REFERENCES users(username)
);
```

### UserRegistrationManager类

```python
class UserRegistrationManager:
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.password_manager = PasswordManager()
    
    def register_user(self, username: str, password: str, email: str = None) -> Dict[str, Any]
    def validate_registration_data(self, username: str, password: str, email: str = None) -> List[str]
    def check_username_availability(self, username: str) -> bool
    def create_user_account(self, user_data: Dict[str, Any]) -> bool
    def send_welcome_message(self, username: str) -> bool
```

### AccountBalanceManager类

```python
class AccountBalanceManager:
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def get_user_balance(self, username: str) -> Decimal
    def recharge_balance(self, username: str, amount: Decimal, operator: str = None) -> Dict[str, Any]
    def deduct_balance(self, username: str, amount: Decimal, service_type: str) -> Dict[str, Any]
    def get_recharge_history(self, username: str, limit: int = 50) -> List[Dict[str, Any]]
    def get_consumption_history(self, username: str, limit: int = 50) -> List[Dict[str, Any]]
    def validate_balance_operation(self, username: str, amount: Decimal) -> bool
```

### PasswordManager类

```python
class PasswordManager:
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def change_password(self, username: str, old_password: str, new_password: str) -> Dict[str, Any]
    def validate_password_strength(self, password: str) -> Tuple[bool, List[str]]
    def verify_current_password(self, username: str, password: str) -> bool
    def generate_temporary_password(self) -> str
    def reset_password(self, username: str, new_password: str, operator: str) -> Dict[str, Any]
    def record_password_change(self, username: str, old_hash: str, ip_address: str) -> bool
```

### BillingManager类

```python
class BillingManager:
    def __init__(self, db_manager: DatabaseManager, balance_manager: AccountBalanceManager):
        self.db_manager = db_manager
        self.balance_manager = balance_manager
        self.billing_rates = self.load_billing_rates()
    
    def start_billing_session(self, username: str, service_type: str) -> Dict[str, Any]
    def stop_billing_session(self, username: str, session_id: str) -> Dict[str, Any]
    def calculate_session_cost(self, duration: int, service_type: str) -> Decimal
    def check_sufficient_balance(self, username: str, estimated_cost: Decimal) -> bool
    def process_billing(self, username: str, amount: Decimal, service_type: str) -> bool
    def get_billing_rates(self) -> Dict[str, Decimal]
    def update_billing_rates(self, rates: Dict[str, Decimal]) -> bool
```

## 数据模型

### 用户注册数据模型

```python
@dataclass
class UserRegistrationData:
    username: str
    password: str
    email: Optional[str] = None
    initial_balance: Decimal = Decimal('0.00')
    status: str = 'active'
    registration_ip: Optional[str] = None
```

### 充值记录数据模型

```python
@dataclass
class RechargeRecord:
    id: Optional[int] = None
    username: str = ''
    amount: Decimal = Decimal('0.00')
    recharge_time: datetime = field(default_factory=datetime.now)
    recharge_type: str = 'manual'
    operator: Optional[str] = None
    description: Optional[str] = None
```

### 消费记录数据模型

```python
@dataclass
class ConsumptionRecord:
    id: Optional[int] = None
    username: str = ''
    amount: Decimal = Decimal('0.00')
    consumption_time: datetime = field(default_factory=datetime.now)
    service_type: str = 'auth'
    duration: Optional[int] = None
    description: Optional[str] = None
```

## 用户界面设计

### 注册界面

```python
class RegistrationDialog:
    def __init__(self, parent):
        self.parent = parent
        self.registration_manager = UserRegistrationManager()
        
    def create_registration_form(self):
        # 用户名输入
        # 密码输入
        # 确认密码输入
        # 邮箱输入（可选）
        # 注册按钮
        # 返回登录按钮
    
    def validate_and_register(self):
        # 验证输入数据
        # 调用注册管理器
        # 显示结果
```

### 充值界面

```python
class RechargeDialog:
    def __init__(self, parent, username):
        self.parent = parent
        self.username = username
        self.balance_manager = AccountBalanceManager()
    
    def create_recharge_form(self):
        # 当前余额显示
        # 充值金额选择
        # 自定义金额输入
        # 充值确认按钮
        # 充值历史查看
    
    def process_recharge(self, amount):
        # 验证充值金额
        # 处理充值请求
        # 更新余额显示
```

### 改密界面

```python
class ChangePasswordDialog:
    def __init__(self, parent, username):
        self.parent = parent
        self.username = username
        self.password_manager = PasswordManager()
    
    def create_password_form(self):
        # 当前密码输入
        # 新密码输入
        # 确认新密码输入
        # 密码强度指示器
        # 修改密码按钮
    
    def change_password(self):
        # 验证当前密码
        # 验证新密码强度
        # 处理密码修改
        # 强制重新登录
```

## 错误处理

### 注册错误处理

```python
class RegistrationError(Exception):
    pass

class UsernameExistsError(RegistrationError):
    pass

class WeakPasswordError(RegistrationError):
    pass

class InvalidEmailError(RegistrationError):
    pass
```

### 充值错误处理

```python
class RechargeError(Exception):
    pass

class InvalidAmountError(RechargeError):
    pass

class InsufficientPermissionError(RechargeError):
    pass
```

### 计费错误处理

```python
class BillingError(Exception):
    pass

class InsufficientBalanceError(BillingError):
    pass

class BillingSessionError(BillingError):
    pass
```

## 安全考虑

### 密码安全

1. **密码强度验证**
   - 最小长度要求
   - 字符复杂度要求
   - 常见密码检查

2. **密码存储**
   - 使用bcrypt哈希
   - 加盐处理
   - 旧密码哈希记录

### 余额安全

1. **操作验证**
   - 权限检查
   - 操作日志记录
   - 异常操作监控

2. **数据完整性**
   - 事务处理
   - 余额一致性检查
   - 定期余额审计

### 会话安全

1. **计费会话**
   - 会话超时处理
   - 异常断线处理
   - 重复计费防护

## 性能优化

### 数据库优化

1. **索引优化**
   - 用户名索引
   - 时间范围索引
   - 复合索引

2. **查询优化**
   - 分页查询
   - 缓存机制
   - 批量操作

### 计费优化

1. **实时计费**
   - 异步处理
   - 批量扣费
   - 缓存余额

## 测试策略

### 单元测试

1. **注册功能测试**
   - 正常注册流程
   - 用户名重复检查
   - 密码验证测试

2. **充值功能测试**
   - 充值金额验证
   - 余额更新测试
   - 历史记录测试

3. **计费功能测试**
   - 扣费计算测试
   - 余额不足处理
   - 会话管理测试

### 集成测试

1. **完整用户流程测试**
   - 注册→充值→使用→扣费
   - 密码修改流程
   - 管理员操作流程

### 压力测试

1. **并发注册测试**
2. **并发充值测试**
3. **并发计费测试**
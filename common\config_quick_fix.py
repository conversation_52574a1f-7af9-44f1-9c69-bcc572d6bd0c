"""
配置文件快速修复工具
专门用于快速解决常见的配置文件问题
"""

import os
import shutil
from typing import List, Dict, Any

from common.enhanced_config_manager import EnhancedConfigManager, RepairResult


class ConfigQuickFix:
    """配置文件快速修复工具"""
    
    def __init__(self):
        self.enhanced_manager = EnhancedConfigManager()
    
    def quick_fix_client_config(self) -> RepairResult:
        """
        快速修复客户端配置文件问题
        
        Returns:
            RepairResult: 修复结果
        """
        result = RepairResult()
        
        # 检查client_config.ini是否存在
        if not os.path.exists('client_config.ini'):
            if os.path.exists('client_config.ini.example'):
                try:
                    # 直接复制示例文件
                    shutil.copy2('client_config.ini.example', 'client_config.ini')
                    result.created_files.append('client_config.ini')
                    result.actions_taken.append('从示例文件创建 client_config.ini')
                    result.success = True
                    
                    # 确保必要的目录存在
                    self._ensure_directories()
                    result.actions_taken.append('创建必要的目录结构')
                    
                except Exception as e:
                    result.errors.append(f'创建client_config.ini失败: {str(e)}')
                    result.success = False
            else:
                result.errors.append('client_config.ini.example文件不存在')
                result.success = False
        else:
            result.actions_taken.append('client_config.ini已存在')
            result.success = True
        
        return result
    
    def quick_fix_all_configs(self) -> RepairResult:
        """
        快速修复所有配置文件问题
        
        Returns:
            RepairResult: 修复结果
        """
        return self.enhanced_manager.auto_fix_all()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            'logs',
            'data', 
            'backup',
            'backup/config'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get_config_status_summary(self) -> Dict[str, Any]:
        """
        获取配置状态摘要
        
        Returns:
            Dict[str, Any]: 状态摘要
        """
        return self.enhanced_manager.get_summary_status()
    
    def is_config_problem_exists(self) -> bool:
        """
        检查是否存在配置问题
        
        Returns:
            bool: 存在问题返回True
        """
        summary = self.get_config_status_summary()
        return not summary['all_configs_ok']
    
    def get_missing_configs(self) -> List[str]:
        """
        获取缺失的配置文件列表
        
        Returns:
            List[str]: 缺失的配置文件
        """
        summary = self.get_config_status_summary()
        return summary['missing_configs']
    
    def get_invalid_configs(self) -> List[str]:
        """
        获取无效的配置文件列表
        
        Returns:
            List[str]: 无效的配置文件
        """
        summary = self.get_config_status_summary()
        return summary['invalid_configs']


def quick_fix_launcher_config():
    """
    启动器专用的快速配置修复函数
    
    Returns:
        tuple: (成功标志, 消息列表)
    """
    try:
        fixer = ConfigQuickFix()
        
        # 检查是否有配置问题
        if not fixer.is_config_problem_exists():
            return True, ["所有配置文件正常"]
        
        # 执行快速修复
        result = fixer.quick_fix_all_configs()
        
        messages = []
        if result.success:
            messages.append("配置文件修复成功")
            messages.extend(result.actions_taken)
        else:
            messages.append("配置文件修复失败")
            messages.extend(result.errors)
        
        return result.success, messages
        
    except Exception as e:
        return False, [f"配置修复过程中出错: {str(e)}"]


if __name__ == "__main__":
    # 测试快速修复功能
    success, messages = quick_fix_launcher_config()
    
    print("配置修复结果:")
    print(f"成功: {success}")
    print("消息:")
    for msg in messages:
        print(f"  - {msg}")
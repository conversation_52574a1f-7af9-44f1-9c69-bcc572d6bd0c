#!/usr/bin/env python3
"""
充值卡密生成器GUI
管理员工具，用于生成和管理充值卡密
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
from datetime import datetime
import csv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.database_manager import DatabaseManager
from server.card_manager import CardManager


class CardGeneratorGUI:
    """充值卡密生成器GUI"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.root.title("充值卡密生成器 - 管理员工具")
        self.root.geometry("900x700")
        
        # 存储最后生成的卡密
        self.last_generated_cards = []
        
        # 初始化数据库组件
        self.init_database()
        
        # 创建界面
        self.create_widgets()
        
        # 刷新统计信息
        self.refresh_statistics()
    
    def init_database(self):
        """初始化数据库组件"""
        try:
            db_path = "data/auth.db"
            if not os.path.exists(db_path):
                # 创建数据目录
                os.makedirs("data", exist_ok=True)
            
            self.db_manager = DatabaseManager(db_path)
            self.card_manager = CardManager(self.db_manager)
            
            print("✓ 卡密管理器初始化完成")
            
        except Exception as e:
            messagebox.showerror("初始化失败", f"数据库初始化失败：{str(e)}")
            sys.exit(1)
    
    def create_widgets(self):
        """创建界面控件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="充值卡密生成器", font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 卡密生成标签页
        self.create_generation_tab(notebook)
        
        # 卡密管理标签页
        self.create_management_tab(notebook)
        
        # 统计信息标签页
        self.create_statistics_tab(notebook)
    
    def create_generation_tab(self, parent):
        """创建卡密生成标签页"""
        gen_frame = ttk.Frame(parent, padding="20")
        parent.add(gen_frame, text="生成卡密")
        
        # 卡密类型选择
        type_frame = ttk.LabelFrame(gen_frame, text="卡密类型", padding="15")
        type_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.card_type_var = tk.StringVar(value="hour_1")
        
        card_types = [
            ('hour_1', '1小时卡 (1小时) - ¥1.0'),
            ('day_1', '天卡 (24小时) - ¥5.0'),
            ('week_1', '周卡 (7天) - ¥30.0'),
            ('month_1', '月卡 (30天) - ¥100.0'),
            ('season_1', '季卡 (90天) - ¥280.0'),
            ('year_1', '年卡 (365天) - ¥1000.0'),
            ('year_10', '10年卡 (3650天) - ¥8000.0')
        ]
        
        for i, (value, text) in enumerate(card_types):
            ttk.Radiobutton(
                type_frame, 
                text=text, 
                variable=self.card_type_var, 
                value=value
            ).grid(row=i//2, column=i%2, sticky=tk.W, padx=10, pady=5)
        
        # 生成数量
        quantity_frame = ttk.LabelFrame(gen_frame, text="生成设置", padding="15")
        quantity_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(quantity_frame, text="生成数量:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.quantity_var = tk.StringVar(value="10")
        quantity_entry = ttk.Entry(quantity_frame, textvariable=self.quantity_var, width=10)
        quantity_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(quantity_frame, text="批次备注:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.batch_note_var = tk.StringVar()
        batch_entry = ttk.Entry(quantity_frame, textvariable=self.batch_note_var, width=30)
        batch_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 生成按钮
        generate_btn = ttk.Button(
            quantity_frame, 
            text="生成卡密", 
            command=self.generate_cards
        )
        generate_btn.grid(row=2, column=0, columnspan=2, pady=(15, 0))
        
        # 生成结果显示
        result_frame = ttk.LabelFrame(gen_frame, text="生成结果", padding="15")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.result_text = scrolledtext.ScrolledText(
            result_frame, 
            height=15, 
            width=80,
            font=('Courier', 9)
        )
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 导出按钮
        export_frame = ttk.Frame(gen_frame)
        export_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(
            export_frame, 
            text="导出到CSV", 
            command=self.export_cards
        ).pack(side=tk.LEFT)
        
        ttk.Button(
            export_frame, 
            text="清空结果", 
            command=self.clear_results
        ).pack(side=tk.LEFT, padx=(10, 0))
    
    def create_management_tab(self, parent):
        """创建卡密管理标签页"""
        mgmt_frame = ttk.Frame(parent, padding="20")
        parent.add(mgmt_frame, text="卡密管理")
        
        # 查询框架
        query_frame = ttk.LabelFrame(mgmt_frame, text="卡密查询", padding="15")
        query_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(query_frame, text="卡密:").pack(side=tk.LEFT)
        self.query_card_var = tk.StringVar()
        query_entry = ttk.Entry(query_frame, textvariable=self.query_card_var, width=25)
        query_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        ttk.Button(
            query_frame, 
            text="查询", 
            command=self.query_card
        ).pack(side=tk.LEFT, padx=(10, 0))
        
        # 查询结果
        self.query_result = scrolledtext.ScrolledText(mgmt_frame, height=20, width=80)
        self.query_result.pack(fill=tk.BOTH, expand=True)
    
    def create_statistics_tab(self, parent):
        """创建统计信息标签页"""
        stats_frame = ttk.Frame(parent, padding="20")
        parent.add(stats_frame, text="统计信息")
        
        # 刷新按钮
        ttk.Button(
            stats_frame, 
            text="刷新统计", 
            command=self.refresh_statistics
        ).pack(pady=(0, 20))
        
        # 统计信息显示
        self.stats_text = scrolledtext.ScrolledText(
            stats_frame, 
            height=25, 
            width=80,
            font=('Courier', 10)
        )
        self.stats_text.pack(fill=tk.BOTH, expand=True)
    
    def generate_cards(self):
        """生成卡密"""
        try:
            card_type = self.card_type_var.get()
            quantity_str = self.quantity_var.get().strip()
            batch_note = self.batch_note_var.get().strip()
            
            if not quantity_str:
                messagebox.showerror("错误", "请输入生成数量")
                return
            
            try:
                quantity = int(quantity_str)
            except ValueError:
                messagebox.showerror("错误", "生成数量必须是数字")
                return
            
            if quantity <= 0 or quantity > 1000:
                messagebox.showerror("错误", "生成数量必须在1-1000之间")
                return
            
            # 生成卡密
            result = self.card_manager.generate_cards(
                card_type=card_type,
                quantity=quantity,
                generator="admin_gui",
                batch_id=None
            )
            
            if result['success']:
                self.last_generated_cards = result['cards']
                
                # 显示生成结果
                self.result_text.delete(1.0, tk.END)
                
                card_name = self.card_manager.card_types[card_type]['name']
                self.result_text.insert(tk.END, f"=== 生成成功 ===\n")
                self.result_text.insert(tk.END, f"卡密类型: {card_name}\n")
                self.result_text.insert(tk.END, f"生成数量: {len(result['cards'])}\n")
                self.result_text.insert(tk.END, f"批次ID: {result['batch_id']}\n")
                if batch_note:
                    self.result_text.insert(tk.END, f"批次备注: {batch_note}\n")
                self.result_text.insert(tk.END, f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                self.result_text.insert(tk.END, "=== 卡密列表 ===\n")
                for i, card in enumerate(result['cards'], 1):
                    self.result_text.insert(tk.END, f"{i:3d}. {card['card_code']}\n")
                
                messagebox.showinfo("生成成功", f"成功生成 {len(result['cards'])} 张 {card_name}")
                
                # 刷新统计信息
                self.refresh_statistics()
                
            else:
                messagebox.showerror("生成失败", result['message'])
                
        except Exception as e:
            messagebox.showerror("生成失败", f"生成卡密时发生错误：{str(e)}")
    
    def export_cards(self):
        """导出卡密到CSV文件"""
        if not self.last_generated_cards:
            messagebox.showwarning("导出失败", "没有可导出的卡密")
            return
        
        try:
            # 选择保存文件
            filename = filedialog.asksaveasfilename(
                title="导出卡密",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )
            
            if not filename:
                return
            
            # 写入CSV文件
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入标题行
                writer.writerow(['序号', '卡密', '类型', '时长(小时)', '价格'])
                
                # 写入卡密数据
                for i, card in enumerate(self.last_generated_cards, 1):
                    writer.writerow([
                        i,
                        card['card_code'],
                        card['card_name'],
                        card['duration_hours'],
                        card['price']
                    ])
            
            messagebox.showinfo("导出成功", f"已导出 {len(self.last_generated_cards)} 张卡密到：\n{filename}")
            
        except Exception as e:
            messagebox.showerror("导出失败", f"导出卡密时发生错误：{str(e)}")
    
    def clear_results(self):
        """清空生成结果"""
        self.result_text.delete(1.0, tk.END)
        self.last_generated_cards = []
    
    def query_card(self):
        """查询卡密信息"""
        card_code = self.query_card_var.get().strip()
        
        if not card_code:
            messagebox.showerror("错误", "请输入卡密")
            return
        
        try:
            result = self.card_manager.get_card_info(card_code)
            
            self.query_result.delete(1.0, tk.END)
            
            if result['success']:
                card_info = result['card_info']
                
                self.query_result.insert(tk.END, f"=== 卡密信息 ===\n")
                self.query_result.insert(tk.END, f"卡密: {card_info['card_code']}\n")
                self.query_result.insert(tk.END, f"类型: {card_info['card_name']}\n")
                self.query_result.insert(tk.END, f"时长: {card_info['duration_hours']} 小时\n")
                self.query_result.insert(tk.END, f"价格: ¥{card_info['price']}\n")
                self.query_result.insert(tk.END, f"状态: {card_info['status']}\n")
                self.query_result.insert(tk.END, f"创建时间: {card_info['created_at']}\n")
                
                if card_info['status'] == 'used':
                    self.query_result.insert(tk.END, f"使用时间: {card_info['used_at']}\n")
                    self.query_result.insert(tk.END, f"使用者: {card_info['used_by']}\n")
                
                if card_info['description']:
                    self.query_result.insert(tk.END, f"描述: {card_info['description']}\n")
                    
            else:
                self.query_result.insert(tk.END, f"查询失败: {result['message']}\n")
                
        except Exception as e:
            self.query_result.delete(1.0, tk.END)
            self.query_result.insert(tk.END, f"查询错误: {str(e)}\n")
    
    def refresh_statistics(self):
        """刷新统计信息"""
        try:
            stats = self.card_manager.get_card_statistics()
            
            self.stats_text.delete(1.0, tk.END)
            
            if stats:
                self.stats_text.insert(tk.END, "=== 卡密统计信息 ===\n\n")
                
                # 总体统计
                self.stats_text.insert(tk.END, f"总卡密数量: {stats.get('total_cards', 0)}\n")
                self.stats_text.insert(tk.END, f"已使用数量: {stats.get('used_cards', 0)}\n")
                self.stats_text.insert(tk.END, f"未使用数量: {stats.get('unused_cards', 0)}\n\n")
                
                # 按类型统计
                self.stats_text.insert(tk.END, "=== 按类型统计 ===\n\n")
                
                type_stats = stats.get('by_type', {})
                for card_type, type_info in type_stats.items():
                    self.stats_text.insert(tk.END, f"{type_info['name']}:\n")
                    self.stats_text.insert(tk.END, f"  总数: {type_info['total']}\n")
                    self.stats_text.insert(tk.END, f"  已用: {type_info['used']}\n")
                    self.stats_text.insert(tk.END, f"  未用: {type_info['unused']}\n\n")
                
                # 使用率统计
                if stats.get('total_cards', 0) > 0:
                    usage_rate = (stats.get('used_cards', 0) / stats.get('total_cards', 1)) * 100
                    self.stats_text.insert(tk.END, f"总使用率: {usage_rate:.1f}%\n")
                
            else:
                self.stats_text.insert(tk.END, "无法获取统计信息\n")
                
        except Exception as e:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, f"获取统计信息失败: {str(e)}\n")
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = CardGeneratorGUI()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")


if __name__ == "__main__":
    main()
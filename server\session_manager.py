"""
会话管理模块
实现会话创建、验证、清理和令牌管理
"""

import secrets
import threading
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from server.database_manager import DatabaseManager
from common.exceptions import DatabaseError
from common.constants import DEFAULT_CONFIG


class SessionManager:
    """会话管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化会话管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.session_timeout = DEFAULT_CONFIG['server']['session_timeout']
        self._lock = threading.Lock()
        
        # 启动会话清理定时器
        self._start_cleanup_timer()
    
    def _start_cleanup_timer(self):
        """启动会话清理定时器"""
        def cleanup_expired_sessions():
            """清理过期会话"""
            try:
                self.cleanup_expired_sessions()
            except Exception as e:
                print(f"清理过期会话时发生错误: {e}")
            
            # 每5分钟清理一次过期会话
            timer = threading.Timer(300, cleanup_expired_sessions)
            timer.daemon = True
            timer.start()
        
        # 启动定时器
        timer = threading.Timer(300, cleanup_expired_sessions)
        timer.daemon = True
        timer.start()
    
    def generate_session_token(self) -> str:
        """
        生成会话令牌
        
        Returns:
            str: 64字符的十六进制令牌
        """
        return secrets.token_hex(32)
    
    def create_session(self, user_id: int, client_ip: str = None) -> Dict[str, Any]:
        """
        创建新会话
        
        Args:
            user_id: 用户ID
            client_ip: 客户端IP地址
            
        Returns:
            Dict[str, Any]: 会话创建结果
        """
        with self._lock:
            try:
                # 生成会话令牌
                session_token = self.generate_session_token()
                
                # 计算过期时间
                expires_at = datetime.now() + timedelta(seconds=self.session_timeout)
                
                # 将会话信息存储到数据库
                with self.db_manager.get_connection() as conn:
                    conn.execute("""
                        INSERT INTO sessions (user_id, session_token, expires_at, client_ip)
                        VALUES (?, ?, ?, ?)
                    """, (user_id, session_token, expires_at.isoformat(), client_ip))
                    conn.commit()
                
                return {
                    'success': True,
                    'session_token': session_token,
                    'expires_at': expires_at.isoformat(),
                    'timeout': self.session_timeout
                }
                
            except Exception as e:
                return {
                    'success': False,
                    'message': f'创建会话失败: {str(e)}'
                }
    
    def validate_session(self, session_token: str) -> Dict[str, Any]:
        """
        验证会话令牌
        
        Args:
            session_token: 会话令牌
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        with self._lock:
            try:
                with self.db_manager.get_connection() as conn:
                    cursor = conn.execute("""
                        SELECT s.id, s.user_id, s.expires_at, s.client_ip, u.username
                        FROM sessions s
                        JOIN users u ON s.user_id = u.id
                        WHERE s.session_token = ? AND s.expires_at > datetime('now')
                    """, (session_token,))
                    
                    session = cursor.fetchone()
                    
                    if session:
                        return {
                            'valid': True,
                            'session_id': session['id'],
                            'user_id': session['user_id'],
                            'username': session['username'],
                            'expires_at': session['expires_at'],
                            'client_ip': session['client_ip']
                        }
                    else:
                        return {
                            'valid': False,
                            'message': '会话无效或已过期'
                        }
                        
            except Exception as e:
                return {
                    'valid': False,
                    'message': f'验证会话失败: {str(e)}'
                }
    
    def refresh_session(self, session_token: str) -> Dict[str, Any]:
        """
        刷新会话过期时间
        
        Args:
            session_token: 会话令牌
            
        Returns:
            Dict[str, Any]: 刷新结果
        """
        with self._lock:
            try:
                # 先验证会话是否有效
                validation = self.validate_session(session_token)
                if not validation['valid']:
                    return {
                        'success': False,
                        'message': validation['message']
                    }
                
                # 更新过期时间
                new_expires_at = datetime.now() + timedelta(seconds=self.session_timeout)
                
                with self.db_manager.get_connection() as conn:
                    cursor = conn.execute("""
                        UPDATE sessions 
                        SET expires_at = ?
                        WHERE session_token = ?
                    """, (new_expires_at.isoformat(), session_token))
                    conn.commit()
                    
                    if cursor.rowcount > 0:
                        return {
                            'success': True,
                            'expires_at': new_expires_at.isoformat(),
                            'timeout': self.session_timeout
                        }
                    else:
                        return {
                            'success': False,
                            'message': '会话刷新失败'
                        }
                        
            except Exception as e:
                return {
                    'success': False,
                    'message': f'刷新会话失败: {str(e)}'
                }
    
    def destroy_session(self, session_token: str) -> Dict[str, Any]:
        """
        销毁会话
        
        Args:
            session_token: 会话令牌
            
        Returns:
            Dict[str, Any]: 销毁结果
        """
        with self._lock:
            try:
                with self.db_manager.get_connection() as conn:
                    cursor = conn.execute("""
                        DELETE FROM sessions WHERE session_token = ?
                    """, (session_token,))
                    conn.commit()
                    
                    if cursor.rowcount > 0:
                        return {
                            'success': True,
                            'message': '会话已销毁'
                        }
                    else:
                        return {
                            'success': False,
                            'message': '会话不存在'
                        }
                        
            except Exception as e:
                return {
                    'success': False,
                    'message': f'销毁会话失败: {str(e)}'
                }
    
    def destroy_user_sessions(self, user_id: int) -> Dict[str, Any]:
        """
        销毁用户的所有会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 销毁结果
        """
        with self._lock:
            try:
                with self.db_manager.get_connection() as conn:
                    cursor = conn.execute("""
                        DELETE FROM sessions WHERE user_id = ?
                    """, (user_id,))
                    conn.commit()
                    
                    return {
                        'success': True,
                        'message': f'已销毁{cursor.rowcount}个会话',
                        'destroyed_count': cursor.rowcount
                    }
                    
            except Exception as e:
                return {
                    'success': False,
                    'message': f'销毁用户会话失败: {str(e)}'
                }
    
    def cleanup_expired_sessions(self) -> Dict[str, Any]:
        """
        清理过期会话
        
        Returns:
            Dict[str, Any]: 清理结果
        """
        with self._lock:
            try:
                with self.db_manager.get_connection() as conn:
                    cursor = conn.execute("""
                        DELETE FROM sessions WHERE expires_at <= datetime('now')
                    """)
                    conn.commit()
                    
                    return {
                        'success': True,
                        'message': f'已清理{cursor.rowcount}个过期会话',
                        'cleaned_count': cursor.rowcount
                    }
                    
            except Exception as e:
                return {
                    'success': False,
                    'message': f'清理过期会话失败: {str(e)}'
                }
    
    def get_active_sessions(self) -> List[Dict[str, Any]]:
        """
        获取所有活跃会话
        
        Returns:
            List[Dict[str, Any]]: 活跃会话列表
        """
        with self._lock:
            try:
                with self.db_manager.get_connection() as conn:
                    cursor = conn.execute("""
                        SELECT s.id, s.user_id, s.session_token, s.created_at, 
                               s.expires_at, s.client_ip, u.username
                        FROM sessions s
                        JOIN users u ON s.user_id = u.id
                        WHERE s.expires_at > datetime('now')
                        ORDER BY s.created_at DESC
                    """)
                    
                    sessions = []
                    for row in cursor.fetchall():
                        session = dict(row)
                        # 隐藏完整的令牌，只显示前8位
                        session['session_token'] = session['session_token'][:8] + '...'
                        sessions.append(session)
                    
                    return sessions
                    
            except Exception as e:
                print(f"获取活跃会话失败: {e}")
                return []
    
    def get_user_sessions(self, user_id: int) -> List[Dict[str, Any]]:
        """
        获取指定用户的活跃会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            List[Dict[str, Any]]: 用户会话列表
        """
        with self._lock:
            try:
                with self.db_manager.get_connection() as conn:
                    cursor = conn.execute("""
                        SELECT id, session_token, created_at, expires_at, client_ip
                        FROM sessions
                        WHERE user_id = ? AND expires_at > datetime('now')
                        ORDER BY created_at DESC
                    """, (user_id,))
                    
                    sessions = []
                    for row in cursor.fetchall():
                        session = dict(row)
                        # 隐藏完整的令牌，只显示前8位
                        session['session_token'] = session['session_token'][:8] + '...'
                        sessions.append(session)
                    
                    return sessions
                    
            except Exception as e:
                print(f"获取用户会话失败: {e}")
                return []
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """
        获取会话统计信息
        
        Returns:
            Dict[str, Any]: 会话统计信息
        """
        with self._lock:
            try:
                with self.db_manager.get_connection() as conn:
                    # 活跃会话数
                    active_count = conn.execute("""
                        SELECT COUNT(*) FROM sessions 
                        WHERE expires_at > datetime('now')
                    """).fetchone()[0]
                    
                    # 总会话数
                    total_count = conn.execute("""
                        SELECT COUNT(*) FROM sessions
                    """).fetchone()[0]
                    
                    # 过期会话数
                    expired_count = total_count - active_count
                    
                    # 今日创建的会话数
                    today_count = conn.execute("""
                        SELECT COUNT(*) FROM sessions 
                        WHERE date(created_at) = date('now')
                    """).fetchone()[0]
                    
                    return {
                        'active_sessions': active_count,
                        'total_sessions': total_count,
                        'expired_sessions': expired_count,
                        'today_sessions': today_count
                    }
                    
            except Exception as e:
                print(f"获取会话统计失败: {e}")
                return {
                    'active_sessions': 0,
                    'total_sessions': 0,
                    'expired_sessions': 0,
                    'today_sessions': 0
                }
    
    def is_session_valid(self, session_token: str) -> bool:
        """
        简单检查会话是否有效
        
        Args:
            session_token: 会话令牌
            
        Returns:
            bool: 会话有效返回True
        """
        validation = self.validate_session(session_token)
        return validation['valid']
#!/usr/bin/env python3
"""
测试用户注册管理器功能
"""

import os
import sys
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_manager import DatabaseManager
from server.user_registration_manager import UserRegistrationManager


def test_user_registration():
    """测试用户注册功能"""
    print("=" * 60)
    print("测试用户注册管理器功能")
    print("=" * 60)
    
    # 使用测试数据库
    test_db_path = "data/test_registration.db"
    
    # 删除测试数据库（如果存在）
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
        print("已删除旧的测试数据库")
    
    try:
        # 创建数据库管理器和注册管理器
        print("\n1. 初始化管理器...")
        db_manager = DatabaseManager(test_db_path)
        
        # 运行数据库迁移
        from server.database_migrations import DatabaseMigration
        migration = DatabaseMigration(test_db_path)
        migration_success = migration.run_migrations()
        if not migration_success:
            print("✗ 数据库迁移失败")
            return False
        
        registration_manager = UserRegistrationManager(db_manager)
        
        print("✓ 管理器初始化成功")
        
        # 测试用户名验证
        print("\n2. 测试用户名验证...")
        
        test_cases = [
            ("", ["用户名不能为空"]),
            ("ab", ["用户名长度不能少于3个字符"]),
            ("a" * 25, ["用户名长度不能超过20个字符"]),
            ("123user", ["用户名不能以数字开头"]),
            ("user@name", ["用户名只能包含字母、数字、下划线和连字符"]),
            ("admin", ["该用户名为系统保留，请选择其他用户名"]),
            ("validuser", []),
            ("user_123", []),
            ("test-user", [])
        ]
        
        for username, expected_errors in test_cases:
            errors = registration_manager.validate_username(username)
            if errors == expected_errors:
                print(f"✓ 用户名 '{username}' 验证正确")
            else:
                print(f"✗ 用户名 '{username}' 验证失败")
                print(f"  期望错误: {expected_errors}")
                print(f"  实际错误: {errors}")
        
        # 测试密码验证
        print("\n3. 测试密码验证...")
        
        password_test_cases = [
            ("", ["密码不能为空"]),
            ("123", ["密码长度不能少于6个字符"]),
            ("123456", ["密码必须包含至少一个字母"]),
            ("abcdef", ["密码必须包含至少一个数字"]),
            ("password", ["密码过于简单，请使用更复杂的密码", "密码必须包含至少一个数字"]),
            ("123456789", ["密码过于简单，请使用更复杂的密码", "密码必须包含至少一个字母"]),
            ("abc123", []),
            ("password123", ["密码过于简单，请使用更复杂的密码"]),
            ("mypassword123", [])
        ]
        
        for password, expected_errors in password_test_cases:
            errors = registration_manager.validate_password(password)
            if set(errors) == set(expected_errors):
                print(f"✓ 密码 '{password}' 验证正确")
            else:
                print(f"✗ 密码 '{password}' 验证失败")
                print(f"  期望错误: {expected_errors}")
                print(f"  实际错误: {errors}")
        
        # 测试邮箱验证
        print("\n4. 测试邮箱验证...")
        
        email_test_cases = [
            ("", []),
            ("invalid-email", ["邮箱格式不正确"]),
            ("test@", ["邮箱格式不正确"]),
            ("@example.com", ["邮箱格式不正确"]),
            ("test@example", ["邮箱格式不正确"]),
            ("<EMAIL>", []),
            ("<EMAIL>", []),
            ("<EMAIL>", [])
        ]
        
        for email, expected_errors in email_test_cases:
            errors = registration_manager.validate_email(email)
            if errors == expected_errors:
                print(f"✓ 邮箱 '{email}' 验证正确")
            else:
                print(f"✗ 邮箱 '{email}' 验证失败")
                print(f"  期望错误: {expected_errors}")
                print(f"  实际错误: {errors}")
        
        # 测试用户注册
        print("\n5. 测试用户注册...")
        
        # 正常注册
        result = registration_manager.register_user(
            username="testuser",
            password="testpass123",
            confirm_password="testpass123",
            email="<EMAIL>"
        )
        
        if result['success']:
            print("✓ 正常用户注册成功")
            print(f"  注册结果: {result}")
        else:
            print("✗ 正常用户注册失败")
            print(f"  错误信息: {result}")
        
        # 重复用户名注册
        result2 = registration_manager.register_user(
            username="testuser",
            password="anotherpass123",
            confirm_password="anotherpass123"
        )
        
        if not result2['success'] and result2['error'] == 'username_exists':
            print("✓ 重复用户名注册正确被拒绝")
        else:
            print("✗ 重复用户名注册处理失败")
            print(f"  结果: {result2}")
        
        # 密码不一致注册
        result3 = registration_manager.register_user(
            username="testuser2",
            password="testpass123",
            confirm_password="differentpass123"
        )
        
        if not result3['success'] and result3['error'] == 'validation_failed':
            print("✓ 密码不一致注册正确被拒绝")
        else:
            print("✗ 密码不一致注册处理失败")
            print(f"  结果: {result3}")
        
        # 弱密码注册
        result4 = registration_manager.register_user(
            username="testuser3",
            password="123",
            confirm_password="123"
        )
        
        if not result4['success'] and result4['error'] == 'validation_failed':
            print("✓ 弱密码注册正确被拒绝")
        else:
            print("✗ 弱密码注册处理失败")
            print(f"  结果: {result4}")
        
        # 测试密码哈希和验证
        print("\n6. 测试密码哈希和验证...")
        
        password = "testpassword123"
        password_hash = registration_manager.hash_password(password)
        
        # 验证正确密码
        if registration_manager.verify_password(password, password_hash):
            print("✓ 密码哈希和验证功能正常")
        else:
            print("✗ 密码哈希和验证功能异常")
        
        # 验证错误密码
        if not registration_manager.verify_password("wrongpassword", password_hash):
            print("✓ 错误密码验证正确被拒绝")
        else:
            print("✗ 错误密码验证处理异常")
        
        # 测试用户名可用性检查
        print("\n7. 测试用户名可用性检查...")
        
        # 检查已存在用户名
        if not registration_manager.check_username_availability("testuser"):
            print("✓ 已存在用户名正确识别为不可用")
        else:
            print("✗ 已存在用户名识别失败")
        
        # 检查不存在用户名
        if registration_manager.check_username_availability("newuser"):
            print("✓ 不存在用户名正确识别为可用")
        else:
            print("✗ 不存在用户名识别失败")
        
        # 测试注册统计
        print("\n8. 测试注册统计...")
        
        stats = registration_manager.get_registration_statistics()
        print(f"注册统计信息: {stats}")
        
        if stats['total_users'] >= 1:
            print("✓ 注册统计功能正常")
        else:
            print("✗ 注册统计功能异常")
        
        # 测试配置管理
        print("\n9. 测试配置管理...")
        
        # 获取当前配置
        config = registration_manager.get_registration_config()
        print(f"当前配置: {config}")
        
        # 更新配置
        success = registration_manager.update_registration_config({
            'min_password_length': 8,
            'require_email': True
        })
        
        if success:
            print("✓ 配置更新成功")
            
            # 验证配置是否生效
            new_config = registration_manager.get_registration_config()
            if new_config['min_password_length'] == 8 and new_config['require_email']:
                print("✓ 配置更新生效")
            else:
                print("✗ 配置更新未生效")
        else:
            print("✗ 配置更新失败")
        
        # 测试更新后的验证规则
        print("\n10. 测试更新后的验证规则...")
        
        # 测试新的密码长度要求
        errors = registration_manager.validate_password("abc123")  # 6位密码
        if "密码长度不能少于8个字符" in errors:
            print("✓ 新密码长度要求生效")
        else:
            print("✗ 新密码长度要求未生效")
        
        # 测试邮箱必填要求
        result5 = registration_manager.register_user(
            username="testuser4",
            password="testpass123456",
            confirm_password="testpass123456"
            # 不提供邮箱
        )
        
        if not result5['success'] and 'validation_failed' in result5.get('error', ''):
            print("✓ 邮箱必填要求生效")
        else:
            print("✗ 邮箱必填要求未生效")
            print(f"  结果: {result5}")
        
        print("\n" + "=" * 60)
        print("✅ 用户注册管理器测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试数据库
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
            print("\n已清理测试数据库")


def main():
    """主函数"""
    print("开始用户注册管理器测试...")
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 运行测试
    success = test_user_registration()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
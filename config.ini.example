# 网络验证工具服务器配置文件示例
# 复制此文件为 config.ini 并根据需要修改配置

[server]
# 服务器监听地址
# 0.0.0.0 - 监听所有网络接口
# 127.0.0.1 - 仅监听本地回环接口
host = 0.0.0.0

# 服务器监听端口
port = 8888

# 最大并发连接数
max_connections = 50

# 连接超时时间（秒）
connection_timeout = 30

[database]
# 数据库文件路径
path = data/auth.db

# 数据库连接池大小
pool_size = 10

# 数据库超时时间（秒）
timeout = 30

[logging]
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
level = INFO

# 日志文件路径
file_path = logs/server.log

# 日志文件最大大小（字节）
# 5242880 = 5MB
max_file_size = 5242880

# 日志文件备份数量
backup_count = 3

# 是否启用控制台输出
console_output = true

# 日志格式
# 可选值: simple, detailed, json
format = detailed

[security]
# 会话超时时间（秒）
# 3600 = 1小时
session_timeout = 3600

# 最大失败登录尝试次数
max_failed_attempts = 5

# 账户锁定持续时间（秒）
# 1800 = 30分钟
lockout_duration = 1800

# 密码最小长度
password_min_length = 6

# 密码复杂度要求
password_require_uppercase = false
password_require_lowercase = false
password_require_numbers = false
password_require_symbols = false

# 会话令牌长度
session_token_length = 32

# 是否启用IP白名单
enable_ip_whitelist = false

# IP白名单（逗号分隔）
# 示例: ***********/24,10.0.0.0/8,**********/12
allowed_ips = 

# 速率限制
rate_limit_enabled = true
rate_limit_requests = 10
rate_limit_window = 60

[performance]
# 线程池大小
thread_pool_size = 20

# 是否启用性能监控
enable_monitoring = true

# 性能监控间隔（秒）
monitoring_interval = 60

# 是否启用缓存
enable_cache = true

# 缓存大小
cache_size = 1000

# 缓存过期时间（秒）
cache_ttl = 300

[backup]
# 是否启用自动备份
auto_backup = true

# 备份间隔（小时）
backup_interval = 24

# 备份保留天数
backup_retention_days = 7

# 备份目录
backup_directory = backup

[monitoring]
# 是否启用系统监控
system_monitoring = true

# 监控数据保留时间（小时）
monitoring_retention_hours = 168

# CPU使用率警告阈值（百分比）
cpu_warning_threshold = 70

# CPU使用率严重阈值（百分比）
cpu_critical_threshold = 90

# 内存使用率警告阈值（百分比）
memory_warning_threshold = 70

# 内存使用率严重阈值（百分比）
memory_critical_threshold = 90

# 响应时间警告阈值（秒）
response_time_warning_threshold = 1.0

# 响应时间严重阈值（秒）
response_time_critical_threshold = 5.0

[notifications]
# 是否启用通知
enable_notifications = false

# 通知方式: email, webhook, log
notification_methods = log

# 邮件通知配置（如果启用）
email_smtp_server = 
email_smtp_port = 587
email_username = 
email_password = 
email_from = 
email_to = 

# Webhook通知配置（如果启用）
webhook_url = 
webhook_timeout = 10

[advanced]
# 是否启用调试模式
debug_mode = false

# 调试日志文件
debug_log_file = logs/debug.log

# 是否启用详细错误信息
verbose_errors = false

# 自定义错误页面
custom_error_pages = false

# 是否启用API模式
api_mode = false

# API认证密钥
api_key = 

# 是否启用统计收集
collect_statistics = true

# 统计数据保留天数
statistics_retention_days = 30
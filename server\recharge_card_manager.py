#!/usr/bin/env python3
"""
充值卡密管理器
处理充值卡的生成、验证和使用
"""

import sqlite3
import secrets
import string
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from contextlib import contextmanager

from server.database_manager import DatabaseManager


class RechargeCardManager:
    """充值卡密管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化卡密管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self._lock = threading.Lock()
        
        # 卡密类型配置
        self.card_types = {
            'hour_1': {'name': '1小时卡', 'duration_hours': 1, 'price': 1.0},
            'day_1': {'name': '天卡', 'duration_hours': 24, 'price': 5.0},
            'week_1': {'name': '周卡', 'duration_hours': 24 * 7, 'price': 30.0},
            'month_1': {'name': '月卡', 'duration_hours': 24 * 30, 'price': 100.0},
            'season_1': {'name': '季卡', 'duration_hours': 24 * 90, 'price': 280.0},
            'year_1': {'name': '年卡', 'duration_hours': 24 * 365, 'price': 1000.0},
            'year_10': {'name': '10年卡', 'duration_hours': 24 * 365 * 10, 'price': 8000.0}
        }
        
        # 初始化数据库表
        self._init_tables()
    
    def _init_tables(self):
        """初始化充值卡相关表"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建充值卡表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS recharge_cards (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        card_code VARCHAR(20) NOT NULL UNIQUE,
                        card_type VARCHAR(20) NOT NULL,
                        duration_hours INTEGER NOT NULL,
                        price DECIMAL(10,2) NOT NULL,
                        status VARCHAR(20) DEFAULT 'unused',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        used_at DATETIME,
                        used_by VARCHAR(50),
                        generator VARCHAR(50),
                        batch_id VARCHAR(50),
                        description TEXT
                    )
                """)
                
                # 创建用户时间记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS user_time_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username VARCHAR(50) NOT NULL,
                        card_code VARCHAR(20),
                        duration_hours INTEGER NOT NULL,
                        added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        expires_at DATETIME,
                        description TEXT,
                        FOREIGN KEY (username) REFERENCES users(username)
                    )
                """)
                
                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_cards_code ON recharge_cards(card_code)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_cards_status ON recharge_cards(status)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_time_records_username ON user_time_records(username)")
                
                conn.commit()
                print("✓ 充值卡表初始化完成")
                
        except Exception as e:
            print(f"初始化充值卡表失败: {e}")
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接"""
        with self.db_manager.get_connection() as conn:
            yield conn
    
    def generate_card_code(self, length: int = 16) -> str:
        """生成卡密"""
        # 使用大写字母和数字，排除容易混淆的字符
        chars = string.ascii_uppercase + string.digits
        chars = chars.replace('0', '').replace('O', '').replace('1', '').replace('I', '')
        
        # 生成随机卡密
        code = ''.join(secrets.choice(chars) for _ in range(length))
        
        # 格式化为 XXXX-XXXX-XXXX-XXXX 格式
        formatted_code = '-'.join([code[i:i+4] for i in range(0, len(code), 4)])
        
        return formatted_code
    
    def generate_cards(self, card_type: str, quantity: int, 
                      generator: str = 'system', batch_id: str = None) -> Dict[str, Any]:
        """
        批量生成充值卡
        
        Args:
            card_type: 卡密类型
            quantity: 生成数量
            generator: 生成者
            batch_id: 批次ID
            
        Returns:
            Dict: 生成结果
        """
        if card_type not in self.card_types:
            return {
                'success': False,
                'message': f'不支持的卡密类型: {card_type}'
            }
        
        if quantity <= 0 or quantity > 1000:
            return {
                'success': False,
                'message': '生成数量必须在1-1000之间'
            }
        
        card_config = self.card_types[card_type]
        
        if not batch_id:
            batch_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        generated_cards = []
        
        with self._lock:
            try:
                with self._get_connection() as conn:
                    cursor = conn.cursor()
                    
                    for i in range(quantity):
                        # 生成唯一卡密
                        max_attempts = 10
                        for attempt in range(max_attempts):
                            card_code = self.generate_card_code()
                            
                            # 检查卡密是否已存在
                            cursor.execute("SELECT id FROM recharge_cards WHERE card_code = ?", (card_code,))
                            if not cursor.fetchone():
                                break
                        else:
                            return {
                                'success': False,
                                'message': f'生成第{i+1}张卡时无法生成唯一卡密'
                            }
                        
                        # 插入卡密记录
                        cursor.execute("""
                            INSERT INTO recharge_cards 
                            (card_code, card_type, duration_hours, price, generator, batch_id, description)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        """, (
                            card_code,
                            card_type,
                            card_config['duration_hours'],
                            card_config['price'],
                            generator,
                            batch_id,
                            f"{card_config['name']} - 批次{batch_id}"
                        ))
                        
                        generated_cards.append({
                            'card_code': card_code,
                            'card_type': card_type,
                            'card_name': card_config['name'],
                            'duration_hours': card_config['duration_hours'],
                            'price': card_config['price']
                        })
                    
                    conn.commit()
                    
                    print(f"成功生成 {len(generated_cards)} 张 {card_config['name']}")
                    
                    return {
                        'success': True,
                        'message': f'成功生成 {len(generated_cards)} 张 {card_config["name"]}',
                        'batch_id': batch_id,
                        'cards': generated_cards
                    }
                    
            except Exception as e:
                print(f"生成充值卡失败: {e}")
                return {
                    'success': False,
                    'message': f'生成充值卡失败: {str(e)}'
                }
    
    def use_card(self, card_code: str, username: str) -> Dict[str, Any]:
        """
        使用充值卡
        
        Args:
            card_code: 卡密
            username: 用户名
            
        Returns:
            Dict: 使用结果
        """
        card_code = card_code.strip().upper()
        
        with self._lock:
            try:
                with self._get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # 查找卡密
                    cursor.execute("""
                        SELECT id, card_type, duration_hours, price, status, used_by
                        FROM recharge_cards WHERE card_code = ?
                    """, (card_code,))
                    
                    card_info = cursor.fetchone()
                    
                    if not card_info:
                        return {
                            'success': False,
                            'error': 'card_not_found',
                            'message': '卡密不存在或无效'
                        }
                    
                    card_id, card_type, duration_hours, price, status, used_by = card_info
                    
                    if status != 'unused':
                        if status == 'used':
                            return {
                                'success': False,
                                'error': 'card_used',
                                'message': f'卡密已被使用（使用者: {used_by})'
                            }
                        else:
                            return {
                                'success': False,
                                'error': 'card_invalid',
                                'message': f'卡密状态异常: {status}'
                            }
                    
                    # 检查用户是否存在
                    cursor.execute("SELECT username FROM users WHERE username = ?", (username,))
                    if not cursor.fetchone():
                        return {
                            'success': False,
                            'error': 'user_not_found',
                            'message': f'用户 "{username}" 不存在'
                        }
                    
                    # 计算到期时间
                    current_time = datetime.now()
                    
                    # 获取用户当前的到期时间
                    cursor.execute("""
                        SELECT MAX(expires_at) FROM user_time_records 
                        WHERE username = ? AND expires_at > ?
                    """, (username, current_time))
                    
                    current_expires = cursor.fetchone()[0]
                    
                    if current_expires:
                        # 如果用户还有剩余时间，从当前到期时间开始累加
                        start_time = datetime.fromisoformat(current_expires)
                    else:
                        # 如果用户没有剩余时间，从现在开始
                        start_time = current_time
                    
                    new_expires_at = start_time + timedelta(hours=duration_hours)
                    
                    # 标记卡密为已使用
                    cursor.execute("""
                        UPDATE recharge_cards 
                        SET status = 'used', used_at = CURRENT_TIMESTAMP, used_by = ?
                        WHERE id = ?
                    """, (username, card_id))
                    
                    # 添加时间记录
                    cursor.execute("""
                        INSERT INTO user_time_records 
                        (username, card_code, duration_hours, expires_at, description)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        username,
                        card_code,
                        duration_hours,
                        new_expires_at.isoformat(),
                        f"使用{self.card_types.get(card_type, {}).get('name', card_type)}"
                    ))
                    
                    conn.commit()
                    
                    card_name = self.card_types.get(card_type, {}).get('name', card_type)
                    
                    print(f"用户 {username} 成功使用 {card_name} (卡密: {card_code})")
                    
                    return {
                        'success': True,
                        'message': f'成功使用 {card_name}',
                        'card_type': card_type,
                        'card_name': card_name,
                        'duration_hours': duration_hours,
                        'expires_at': new_expires_at.isoformat(),
                        'added_time': f'{duration_hours}小时'
                    }
                    
            except Exception as e:
                print(f"使用充值卡失败: {e}")
                return {
                    'success': False,
                    'error': 'use_failed',
                    'message': f'使用充值卡失败: {str(e)}'
                }
    
    def get_user_remaining_time(self, username: str) -> Dict[str, Any]:
        """
        获取用户剩余时间
        
        Args:
            username: 用户名
            
        Returns:
            Dict: 剩余时间信息
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                current_time = datetime.now()
                
                # 获取用户最新的到期时间
                cursor.execute("""
                    SELECT MAX(expires_at) FROM user_time_records 
                    WHERE username = ? AND expires_at > ?
                """, (username, current_time))
                
                expires_at = cursor.fetchone()[0]
                
                if not expires_at:
                    return {
                        'success': True,
                        'has_time': False,
                        'remaining_seconds': 0,
                        'remaining_hours': 0,
                        'expires_at': None,
                        'status': 'expired'
                    }
                
                expires_datetime = datetime.fromisoformat(expires_at)
                remaining_delta = expires_datetime - current_time
                
                if remaining_delta.total_seconds() <= 0:
                    return {
                        'success': True,
                        'has_time': False,
                        'remaining_seconds': 0,
                        'remaining_hours': 0,
                        'expires_at': expires_at,
                        'status': 'expired'
                    }
                
                remaining_seconds = int(remaining_delta.total_seconds())
                remaining_hours = remaining_seconds / 3600
                
                return {
                    'success': True,
                    'has_time': True,
                    'remaining_seconds': remaining_seconds,
                    'remaining_hours': remaining_hours,
                    'expires_at': expires_at,
                    'status': 'active'
                }
                
        except Exception as e:
            print(f"获取用户剩余时间失败: {e}")
            return {
                'success': False,
                'message': f'获取剩余时间失败: {str(e)}'
            }
    
    def get_card_info(self, card_code: str) -> Dict[str, Any]:
        """
        获取卡密信息
        
        Args:
            card_code: 卡密
            
        Returns:
            Dict: 卡密信息
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT card_code, card_type, duration_hours, price, status, 
                           created_at, used_at, used_by, description
                    FROM recharge_cards WHERE card_code = ?
                """, (card_code.strip().upper(),))
                
                card_info = cursor.fetchone()
                
                if not card_info:
                    return {
                        'success': False,
                        'message': '卡密不存在'
                    }
                
                card_data = {
                    'card_code': card_info[0],
                    'card_type': card_info[1],
                    'card_name': self.card_types.get(card_info[1], {}).get('name', card_info[1]),
                    'duration_hours': card_info[2],
                    'price': float(card_info[3]),
                    'status': card_info[4],
                    'created_at': card_info[5],
                    'used_at': card_info[6],
                    'used_by': card_info[7],
                    'description': card_info[8]
                }
                
                return {
                    'success': True,
                    'card_info': card_data
                }
                
        except Exception as e:
            print(f"获取卡密信息失败: {e}")
            return {
                'success': False,
                'message': f'获取卡密信息失败: {str(e)}'
            }
    
    def get_user_time_history(self, username: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取用户时间充值历史
        
        Args:
            username: 用户名
            limit: 返回记录数限制
            
        Returns:
            List[Dict]: 时间充值历史
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT card_code, duration_hours, added_at, expires_at, description
                    FROM user_time_records 
                    WHERE username = ?
                    ORDER BY added_at DESC
                    LIMIT ?
                """, (username, limit))
                
                records = []
                for row in cursor.fetchall():
                    records.append({
                        'card_code': row[0],
                        'duration_hours': row[1],
                        'added_at': row[2],
                        'expires_at': row[3],
                        'description': row[4]
                    })
                
                return records
                
        except Exception as e:
            print(f"获取用户时间历史失败: {e}")
            return []
    
    def get_card_statistics(self) -> Dict[str, Any]:
        """
        获取卡密统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # 总卡密数
                cursor.execute("SELECT COUNT(*) FROM recharge_cards")
                stats['total_cards'] = cursor.fetchone()[0]
                
                # 已使用卡密数
                cursor.execute("SELECT COUNT(*) FROM recharge_cards WHERE status = 'used'")
                stats['used_cards'] = cursor.fetchone()[0]
                
                # 未使用卡密数
                cursor.execute("SELECT COUNT(*) FROM recharge_cards WHERE status = 'unused'")
                stats['unused_cards'] = cursor.fetchone()[0]
                
                # 按类型统计
                cursor.execute("""
                    SELECT card_type, COUNT(*) as count, 
                           SUM(CASE WHEN status = 'used' THEN 1 ELSE 0 END) as used_count
                    FROM recharge_cards 
                    GROUP BY card_type
                """)
                
                type_stats = {}
                for row in cursor.fetchall():
                    card_type, total, used = row
                    type_stats[card_type] = {
                        'name': self.card_types.get(card_type, {}).get('name', card_type),
                        'total': total,
                        'used': used,
                        'unused': total - used
                    }
                
                stats['by_type'] = type_stats
                
                return stats
                
        except Exception as e:
            print(f"获取卡密统计失败: {e}")
            return {}
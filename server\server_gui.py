"""
服务器端GUI主界面
使用Tkinter创建服务器管理界面，实现服务器启动/停止控制和实时状态显示
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional

from server.database_manager import DatabaseManager
from server.authentication_handler import Authentication<PERSON>andler
from server.socket_listener import SocketListener
from server.thread_pool_manager import ThreadPoolManager
from server.protocol_handler import ProtocolHandler
from server.user_management_gui import UserManagementGUI
from common.constants import DEFAULT_CONFIG


class ServerGUI:
    """服务器GUI主界面"""
    
    def __init__(self):
        """初始化服务器GUI"""
        self.root = tk.Tk()
        self.root.title("网络验证工具 - 服务器管理")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 服务器组件
        self.db_manager = None
        self.auth_handler = None
        self.socket_listener = None
        self.thread_pool = None
        self.protocol_handler = None
        
        # 服务器状态
        self.server_running = False
        self.server_config = DEFAULT_CONFIG.copy()
        
        # GUI组件
        self.status_vars = {}
        self.log_text = None
        self.connection_tree = None
        self.stats_labels = {}
        
        # 更新线程
        self.update_thread = None
        self.update_running = False
        
        self._create_widgets()
        self._setup_layout()
        self._initialize_server_components()
        self._start_update_thread()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 服务器控制标签页
        self._create_server_control_tab(notebook)
        
        # 连接监控标签页
        self._create_connection_monitor_tab(notebook)
        
        # 系统日志标签页
        self._create_log_tab(notebook)
        
        # 统计信息标签页
        self._create_statistics_tab(notebook)
        
        # 用户管理标签页
        self._create_user_management_tab(notebook)
    
    def _create_server_control_tab(self, parent):
        """创建服务器控制标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="服务器控制")
        
        # 服务器状态框架
        status_frame = ttk.LabelFrame(frame, text="服务器状态", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 状态指示器
        status_row = ttk.Frame(status_frame)
        status_row.pack(fill=tk.X)
        
        ttk.Label(status_row, text="服务器状态:").pack(side=tk.LEFT)
        self.status_vars['server_status'] = tk.StringVar(value="已停止")
        status_label = ttk.Label(status_row, textvariable=self.status_vars['server_status'], 
                                foreground="red", font=("Arial", 10, "bold"))
        status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 服务器信息
        info_frame = ttk.Frame(status_frame)
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 监听地址
        ttk.Label(info_frame, text="监听地址:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.status_vars['listen_address'] = tk.StringVar(value="未启动")
        ttk.Label(info_frame, textvariable=self.status_vars['listen_address']).grid(row=0, column=1, sticky=tk.W)
        
        # 当前连接数
        ttk.Label(info_frame, text="当前连接:").grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        self.status_vars['connection_count'] = tk.StringVar(value="0")
        ttk.Label(info_frame, textvariable=self.status_vars['connection_count']).grid(row=0, column=3, sticky=tk.W)
        
        # 运行时间
        ttk.Label(info_frame, text="运行时间:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.status_vars['uptime'] = tk.StringVar(value="00:00:00")
        ttk.Label(info_frame, textvariable=self.status_vars['uptime']).grid(row=1, column=1, sticky=tk.W)
        
        # 总连接数
        ttk.Label(info_frame, text="总连接数:").grid(row=1, column=2, sticky=tk.W, padx=(20, 10))
        self.status_vars['total_connections'] = tk.StringVar(value="0")
        ttk.Label(info_frame, textvariable=self.status_vars['total_connections']).grid(row=1, column=3, sticky=tk.W)
        
        # 控制按钮框架
        control_frame = ttk.LabelFrame(frame, text="服务器控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack()
        
        # 启动按钮
        self.start_button = ttk.Button(button_frame, text="启动服务器", 
                                      command=self._start_server, width=15)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 停止按钮
        self.stop_button = ttk.Button(button_frame, text="停止服务器", 
                                     command=self._stop_server, width=15, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 重启按钮
        self.restart_button = ttk.Button(button_frame, text="重启服务器", 
                                        command=self._restart_server, width=15, state=tk.DISABLED)
        self.restart_button.pack(side=tk.LEFT)
        
        # 配置框架
        config_frame = ttk.LabelFrame(frame, text="服务器配置", padding=10)
        config_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置表格
        config_tree = ttk.Treeview(config_frame, columns=("value",), show="tree headings", height=8)
        config_tree.heading("#0", text="配置项")
        config_tree.heading("value", text="值")
        config_tree.column("#0", width=200)
        config_tree.column("value", width=300)
        
        # 添加滚动条
        config_scrollbar = ttk.Scrollbar(config_frame, orient=tk.VERTICAL, command=config_tree.yview)
        config_tree.configure(yscrollcommand=config_scrollbar.set)
        
        config_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        config_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.config_tree = config_tree
        self._populate_config_tree()
    
    def _create_connection_monitor_tab(self, parent):
        """创建连接监控标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="连接监控")
        
        # 连接列表框架
        list_frame = ttk.LabelFrame(frame, text="活跃连接", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 连接表格
        columns = ("conn_id", "address", "connected_at", "last_activity")
        self.connection_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        self.connection_tree.heading("conn_id", text="连接ID")
        self.connection_tree.heading("address", text="客户端地址")
        self.connection_tree.heading("connected_at", text="连接时间")
        self.connection_tree.heading("last_activity", text="最后活动")
        
        # 设置列宽
        self.connection_tree.column("conn_id", width=150)
        self.connection_tree.column("address", width=150)
        self.connection_tree.column("connected_at", width=200)
        self.connection_tree.column("last_activity", width=200)
        
        # 添加滚动条
        conn_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.connection_tree.yview)
        self.connection_tree.configure(yscrollcommand=conn_scrollbar.set)
        
        self.connection_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        conn_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 连接操作按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="刷新连接列表", 
                  command=self._refresh_connections).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="断开选中连接", 
                  command=self._disconnect_selected).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="断开所有连接", 
                  command=self._disconnect_all).pack(side=tk.LEFT)
    
    def _create_log_tab(self, parent):
        """创建系统日志标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="系统日志")
        
        # 日志控制框架
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(control_frame, text="清空日志", 
                  command=self._clear_logs).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="保存日志", 
                  command=self._save_logs).pack(side=tk.LEFT, padx=(0, 10))
        
        # 自动滚动选项
        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="自动滚动", 
                       variable=self.auto_scroll_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # 日志文本框
        log_frame = ttk.LabelFrame(frame, text="日志内容", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, 
                                                 font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 配置日志颜色标签
        self.log_text.tag_config("INFO", foreground="black")
        self.log_text.tag_config("WARNING", foreground="orange")
        self.log_text.tag_config("ERROR", foreground="red")
        self.log_text.tag_config("SUCCESS", foreground="green")
    
    def _create_statistics_tab(self, parent):
        """创建统计信息标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="统计信息")
        
        # 认证统计框架
        auth_frame = ttk.LabelFrame(frame, text="认证统计", padding=10)
        auth_frame.pack(fill=tk.X, pady=(0, 10))
        
        auth_grid = ttk.Frame(auth_frame)
        auth_grid.pack()
        
        # 认证统计标签
        auth_stats = [
            ("总认证次数:", "total_auths"),
            ("成功认证:", "successful_auths"),
            ("失败认证:", "failed_auths"),
            ("成功率:", "success_rate"),
            ("锁定账户:", "locked_accounts"),
            ("活跃会话:", "active_sessions")
        ]
        
        for i, (label, key) in enumerate(auth_stats):
            row, col = i // 3, (i % 3) * 2
            ttk.Label(auth_grid, text=label).grid(row=row, column=col, sticky=tk.W, padx=(0, 10))
            self.stats_labels[key] = ttk.Label(auth_grid, text="0", font=("Arial", 9, "bold"))
            self.stats_labels[key].grid(row=row, column=col+1, sticky=tk.W, padx=(0, 20))
        
        # 系统统计框架
        system_frame = ttk.LabelFrame(frame, text="系统统计", padding=10)
        system_frame.pack(fill=tk.X, pady=(0, 10))
        
        system_grid = ttk.Frame(system_frame)
        system_grid.pack()
        
        # 系统统计标签
        system_stats = [
            ("数据库用户数:", "user_count"),
            ("数据库大小:", "db_size"),
            ("活跃线程:", "active_threads"),
            ("内存使用:", "memory_usage")
        ]
        
        for i, (label, key) in enumerate(system_stats):
            row, col = i // 2, (i % 2) * 2
            ttk.Label(system_grid, text=label).grid(row=row, column=col, sticky=tk.W, padx=(0, 10))
            self.stats_labels[key] = ttk.Label(system_grid, text="0", font=("Arial", 9, "bold"))
            self.stats_labels[key].grid(row=row, column=col+1, sticky=tk.W, padx=(0, 20))
    
    def _setup_layout(self):
        """设置布局"""
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")  # 可以添加图标文件
            pass
        except:
            pass
        
        # 居中显示窗口
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")
    
    def _initialize_server_components(self):
        """初始化服务器组件"""
        try:
            # 初始化数据库管理器
            self.db_manager = DatabaseManager()
            
            # 初始化认证处理器
            self.auth_handler = AuthenticationHandler(self.db_manager)
            
            # 初始化线程池
            self.thread_pool = ThreadPoolManager(
                max_threads=self.server_config['server']['max_connections']
            )
            
            # 初始化协议处理器
            self.protocol_handler = ProtocolHandler(
                self.auth_handler.user_manager,
                self.auth_handler.session_manager
            )
            
            self._log_message("服务器组件初始化完成", "SUCCESS")
            
        except Exception as e:
            self._log_message(f"初始化服务器组件失败: {str(e)}", "ERROR")
            messagebox.showerror("初始化错误", f"服务器组件初始化失败:\n{str(e)}")
    
    def _populate_config_tree(self):
        """填充配置树"""
        if not self.config_tree:
            return
        
        # 清空现有项目
        for item in self.config_tree.get_children():
            self.config_tree.delete(item)
        
        # 添加配置项
        for section, configs in self.server_config.items():
            section_item = self.config_tree.insert("", tk.END, text=section.upper(), values=("",))
            
            for key, value in configs.items():
                self.config_tree.insert(section_item, tk.END, text=key, values=(str(value),))
        
        # 展开所有项目
        for item in self.config_tree.get_children():
            self.config_tree.item(item, open=True)
    
    def _start_server(self):
        """启动服务器"""
        try:
            if self.server_running:
                return
            
            self._log_message("正在启动服务器...", "INFO")
            
            # 创建Socket监听器
            self.socket_listener = SocketListener(
                host=self.server_config['server']['host'],
                port=self.server_config['server']['port'],
                max_connections=self.server_config['server']['max_connections']
            )
            
            # 设置客户端处理器
            self.socket_listener.set_client_handler(self._handle_client_connection)
            
            # 启动监听
            result = self.socket_listener.start_listening()
            
            if result['success']:
                self.server_running = True
                self._update_server_status()
                self._log_message(f"服务器启动成功: {result['message']}", "SUCCESS")
                
                # 更新按钮状态
                self.start_button.config(state=tk.DISABLED)
                self.stop_button.config(state=tk.NORMAL)
                self.restart_button.config(state=tk.NORMAL)
                
            else:
                self._log_message(f"服务器启动失败: {result['message']}", "ERROR")
                messagebox.showerror("启动失败", result['message'])
                
        except Exception as e:
            self._log_message(f"启动服务器时发生异常: {str(e)}", "ERROR")
            messagebox.showerror("启动异常", f"启动服务器时发生异常:\n{str(e)}")
    
    def _stop_server(self):
        """停止服务器"""
        try:
            if not self.server_running:
                return
            
            self._log_message("正在停止服务器...", "INFO")
            
            # 停止Socket监听器
            if self.socket_listener:
                result = self.socket_listener.stop_listening()
                if result['success']:
                    self._log_message(f"服务器停止成功: {result['message']}", "SUCCESS")
                else:
                    self._log_message(f"停止服务器时出现问题: {result['message']}", "WARNING")
            
            self.server_running = False
            self._update_server_status()
            
            # 更新按钮状态
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.restart_button.config(state=tk.DISABLED)
            
        except Exception as e:
            self._log_message(f"停止服务器时发生异常: {str(e)}", "ERROR")
            messagebox.showerror("停止异常", f"停止服务器时发生异常:\n{str(e)}")
    
    def _restart_server(self):
        """重启服务器"""
        self._log_message("正在重启服务器...", "INFO")
        self._stop_server()
        time.sleep(1)  # 等待1秒
        self._start_server()
    
    def _handle_client_connection(self, conn_id: str, client_socket, address):
        """处理客户端连接"""
        client_ip = address[0]
        self._log_message(f"新客户端连接: {client_ip} (ID: {conn_id})", "INFO")
        
        try:
            # 发送欢迎消息
            welcome_msg = self.protocol_handler.create_welcome_message()
            self.socket_listener.send_message(conn_id, welcome_msg)
            
            # 处理客户端消息
            while self.server_running:
                try:
                    # 接收消息
                    message = self.socket_listener.receive_message(client_socket, timeout=30.0)
                    
                    if message is None:
                        break  # 连接断开或超时
                    
                    # 处理消息
                    response = self.protocol_handler.process_message(conn_id, message, client_ip)
                    
                    # 发送响应
                    if not self.socket_listener.send_message(conn_id, response):
                        break  # 发送失败，连接可能断开
                    
                    # 记录认证尝试
                    if message.get('type') == 'auth_request':
                        username = message.get('username', 'unknown')
                        if response.get('success'):
                            self._log_message(f"用户 {username} 认证成功 (来自 {client_ip})", "SUCCESS")
                        else:
                            self._log_message(f"用户 {username} 认证失败 (来自 {client_ip})", "WARNING")
                    
                except Exception as e:
                    self._log_message(f"处理客户端 {client_ip} 消息时发生错误: {str(e)}", "ERROR")
                    break
        
        except Exception as e:
            self._log_message(f"处理客户端连接 {client_ip} 时发生异常: {str(e)}", "ERROR")
        
        finally:
            self._log_message(f"客户端断开连接: {client_ip} (ID: {conn_id})", "INFO")
    
    def _update_server_status(self):
        """更新服务器状态显示"""
        if self.server_running and self.socket_listener:
            self.status_vars['server_status'].set("运行中")
            self.status_vars['listen_address'].set(f"{self.socket_listener.host}:{self.socket_listener.port}")
            
            # 更新状态标签颜色
            for widget in self.root.winfo_children():
                self._find_and_update_status_label(widget, "green")
        else:
            self.status_vars['server_status'].set("已停止")
            self.status_vars['listen_address'].set("未启动")
            
            # 更新状态标签颜色
            for widget in self.root.winfo_children():
                self._find_and_update_status_label(widget, "red")
    
    def _find_and_update_status_label(self, widget, color):
        """递归查找并更新状态标签颜色"""
        try:
            if isinstance(widget, ttk.Label) and widget.cget('textvariable') == str(self.status_vars['server_status']):
                widget.config(foreground=color)
        except:
            pass
        
        for child in widget.winfo_children():
            self._find_and_update_status_label(child, color)
    
    def _refresh_connections(self):
        """刷新连接列表"""
        if not self.connection_tree or not self.socket_listener:
            return
        
        # 清空现有项目
        for item in self.connection_tree.get_children():
            self.connection_tree.delete(item)
        
        # 获取连接列表
        connections = self.socket_listener.get_connections()
        
        # 添加连接项目
        for conn in connections:
            self.connection_tree.insert("", tk.END, values=(
                conn['conn_id'],
                conn['address'],
                conn['connected_at'],
                conn['last_activity']
            ))
    
    def _disconnect_selected(self):
        """断开选中的连接"""
        if not self.connection_tree:
            return
        
        selected = self.connection_tree.selection()
        if not selected:
            messagebox.showwarning("未选择", "请先选择要断开的连接")
            return
        
        for item in selected:
            values = self.connection_tree.item(item, 'values')
            if values:
                conn_id = values[0]
                # 断开指定连接
                if self.server_app and self.server_app.socket_listener:
                    success = self.server_app.socket_listener.disconnect_client(conn_id)
                    if success:
                        self._log_message(f"已断开连接: {conn_id}", "INFO")
                    else:
                        self._log_message(f"断开连接失败: {conn_id}", "WARNING")
                else:
                    self._log_message("服务器未运行，无法断开连接", "WARNING")
        
        # 刷新连接列表
        self._refresh_connections()
    
    def _disconnect_all(self):
        """断开所有连接"""
        if not self.server_app or not self.server_app.socket_listener:
            messagebox.showwarning("警告", "服务器未运行")
            return
        
        # 确认操作
        if not messagebox.askyesno("确认", "确定要断开所有客户端连接吗？"):
            return
        
        try:
            # 断开所有连接
            disconnected_count = self.server_app.socket_listener.disconnect_all_clients()
            
            if disconnected_count > 0:
                self._log_message(f"已断开所有连接，共 {disconnected_count} 个", "INFO")
                messagebox.showinfo("完成", f"已断开 {disconnected_count} 个连接")
            else:
                self._log_message("当前没有活跃连接", "INFO")
                messagebox.showinfo("提示", "当前没有活跃连接")
            
            # 刷新连接列表
            self._refresh_connections()
            
        except Exception as e:
            error_msg = f"断开所有连接时发生错误: {str(e)}"
            self._log_message(error_msg, "ERROR")
            messagebox.showerror("错误", error_msg)
    
    def _clear_logs(self):
        """清空日志"""
        if self.log_text:
            self.log_text.delete(1.0, tk.END)
    
    def _save_logs(self):
        """保存日志到文件"""
        if not self.log_text:
            return
        
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".log",
            filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("保存成功", f"日志已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存日志失败:\n{str(e)}")
    
    def _log_message(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        if not self.log_text:
            return
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        # 在GUI线程中更新日志
        def update_log():
            self.log_text.insert(tk.END, log_entry, level)
            
            # 自动滚动到底部
            if self.auto_scroll_var.get():
                self.log_text.see(tk.END)
        
        if threading.current_thread() == threading.main_thread():
            update_log()
        else:
            self.root.after(0, update_log)
    
    def _start_update_thread(self):
        """启动更新线程"""
        self.update_running = True
        self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
        self.update_thread.start()
    
    def _update_loop(self):
        """更新循环"""
        while self.update_running:
            try:
                self._update_statistics()
                time.sleep(2)  # 每2秒更新一次
            except Exception as e:
                print(f"更新统计信息时发生错误: {e}")
                time.sleep(5)
    
    def _update_statistics(self):
        """更新统计信息"""
        try:
            # 更新连接统计
            if self.socket_listener:
                stats = self.socket_listener.get_statistics()
                self.status_vars['connection_count'].set(str(stats['current_connections']))
                self.status_vars['total_connections'].set(str(stats['total_connections']))
                
                # 更新运行时间
                if stats['uptime_seconds'] > 0:
                    hours = stats['uptime_seconds'] // 3600
                    minutes = (stats['uptime_seconds'] % 3600) // 60
                    seconds = stats['uptime_seconds'] % 60
                    self.status_vars['uptime'].set(f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            
            # 更新认证统计
            if self.auth_handler:
                auth_stats = self.auth_handler.get_auth_statistics()
                self.stats_labels['total_auths'].config(text=str(auth_stats['total_attempts']))
                self.stats_labels['successful_auths'].config(text=str(auth_stats['successful_auths']))
                self.stats_labels['failed_auths'].config(text=str(auth_stats['failed_auths']))
                self.stats_labels['success_rate'].config(text=f"{auth_stats['success_rate']:.1f}%")
                self.stats_labels['locked_accounts'].config(text=str(auth_stats['locked_accounts']))
                self.stats_labels['active_sessions'].config(text=str(auth_stats['active_sessions']))
            
            # 更新数据库统计
            if self.db_manager:
                db_info = self.db_manager.get_database_info()
                self.stats_labels['user_count'].config(text=str(db_info['user_count']))
                
                # 格式化数据库大小
                db_size = db_info['database_size']
                if db_size < 1024:
                    size_str = f"{db_size} B"
                elif db_size < 1024 * 1024:
                    size_str = f"{db_size / 1024:.1f} KB"
                else:
                    size_str = f"{db_size / (1024 * 1024):.1f} MB"
                
                self.stats_labels['db_size'].config(text=size_str)
            
            # 更新线程统计
            if self.thread_pool:
                thread_stats = self.thread_pool.get_statistics()
                self.stats_labels['active_threads'].config(text=str(thread_stats['active_tasks']))
            
            # 自动刷新连接列表
            if self.server_running:
                self.root.after(0, self._refresh_connections)
                
        except Exception as e:
            print(f"更新统计信息失败: {e}")
    
    def _on_closing(self):
        """窗口关闭事件处理"""
        if self.server_running:
            if messagebox.askokcancel("确认退出", "服务器正在运行，确定要退出吗？"):
                self._stop_server()
                self.update_running = False
                self.root.destroy()
        else:
            self.update_running = False
            self.root.destroy()
    
    def _create_user_management_tab(self, parent):
        """创建用户管理标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="用户管理")
        
        # 初始化用户管理GUI
        if self.auth_handler:
            self.user_management_gui = UserManagementGUI(frame, self.auth_handler)
        else:
            ttk.Label(frame, text="用户管理功能需要先初始化认证处理器").pack(expand=True)
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = ServerGUI()
        app.run()
    except Exception as e:
        print(f"启动GUI失败: {e}")
        messagebox.showerror("启动失败", f"启动GUI失败:\n{str(e)}")


if __name__ == "__main__":
    main()
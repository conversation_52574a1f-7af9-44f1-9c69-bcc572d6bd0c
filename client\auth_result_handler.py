"""
认证结果处理模块
实现认证成功和失败的界面显示、错误信息解析和连接状态监控
"""

import tkinter as tk
from tkinter import messagebox
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timedelta
import threading
import time

from common.constants import AUTH_STATUS


class AuthResultHandler:
    """认证结果处理器"""
    
    def __init__(self, gui_callback: Optional[Callable] = None):
        """
        初始化认证结果处理器
        
        Args:
            gui_callback: GUI回调函数
        """
        self.gui_callback = gui_callback
        self.last_auth_result = None
        self.auth_history = []
        self.max_history_size = 100
        
        # 状态监控
        self.connection_monitor = ConnectionMonitor()
        self.error_analyzer = ErrorAnalyzer()
    
    def handle_auth_success(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理认证成功结果
        
        Args:
            result: 认证结果
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 记录认证历史
            self._record_auth_attempt(result, True)
            
            # 解析用户信息
            user_info = self._parse_user_info(result)
            
            # 解析会话信息
            session_info = self._parse_session_info(result)
            
            # 创建成功消息
            success_message = self._create_success_message(user_info, session_info)
            
            # 更新最后认证结果
            self.last_auth_result = {
                'success': True,
                'timestamp': datetime.now().isoformat(),
                'user_info': user_info,
                'session_info': session_info,
                'message': success_message
            }
            
            # 调用GUI回调
            if self.gui_callback:
                self.gui_callback('auth_success', self.last_auth_result)
            
            return {
                'success': True,
                'message': success_message,
                'user_info': user_info,
                'session_info': session_info
            }
            
        except Exception as e:
            error_msg = f"处理认证成功结果时发生错误: {str(e)}"
            return {
                'success': False,
                'message': error_msg
            }
    
    def handle_auth_failure(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理认证失败结果
        
        Args:
            result: 认证结果
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 记录认证历史
            self._record_auth_attempt(result, False)
            
            # 分析错误类型
            error_analysis = self.error_analyzer.analyze_auth_error(result)
            
            # 创建失败消息
            failure_message = self._create_failure_message(result, error_analysis)
            
            # 更新最后认证结果
            self.last_auth_result = {
                'success': False,
                'timestamp': datetime.now().isoformat(),
                'error_analysis': error_analysis,
                'message': failure_message,
                'original_result': result
            }
            
            # 调用GUI回调
            if self.gui_callback:
                self.gui_callback('auth_failure', self.last_auth_result)
            
            return {
                'success': False,
                'message': failure_message,
                'error_analysis': error_analysis,
                'suggestions': error_analysis.get('suggestions', [])
            }
            
        except Exception as e:
            error_msg = f"处理认证失败结果时发生错误: {str(e)}"
            return {
                'success': False,
                'message': error_msg
            }
    
    def _parse_user_info(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析用户信息
        
        Args:
            result: 认证结果
            
        Returns:
            Dict[str, Any]: 用户信息
        """
        user_info = result.get('user_info', {})
        
        return {
            'username': user_info.get('username', result.get('username', 'N/A')),
            'user_id': user_info.get('user_id', result.get('user_id', 'N/A')),
            'last_login': user_info.get('last_login', result.get('last_login')),
            'display_name': user_info.get('display_name', user_info.get('username', 'N/A'))
        }
    
    def _parse_session_info(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析会话信息
        
        Args:
            result: 认证结果
            
        Returns:
            Dict[str, Any]: 会话信息
        """
        session_token = result.get('session_token', '')
        expires_at = result.get('expires_at')
        timeout = result.get('timeout', 3600)
        
        # 计算过期时间
        if expires_at:
            try:
                expire_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                remaining_seconds = int((expire_time - datetime.now()).total_seconds())
            except:
                remaining_seconds = timeout
        else:
            remaining_seconds = timeout
        
        return {
            'session_token': session_token,
            'session_token_short': session_token[:8] + '...' if session_token else 'N/A',
            'expires_at': expires_at,
            'timeout_seconds': timeout,
            'remaining_seconds': max(0, remaining_seconds),
            'is_valid': remaining_seconds > 0
        }
    
    def _create_success_message(self, user_info: Dict[str, Any], session_info: Dict[str, Any]) -> str:
        """
        创建成功消息
        
        Args:
            user_info: 用户信息
            session_info: 会话信息
            
        Returns:
            str: 成功消息
        """
        username = user_info.get('username', 'N/A')
        remaining_time = session_info.get('remaining_seconds', 0)
        
        # 格式化剩余时间
        if remaining_time > 3600:
            time_str = f"{remaining_time // 3600}小时{(remaining_time % 3600) // 60}分钟"
        elif remaining_time > 60:
            time_str = f"{remaining_time // 60}分钟"
        else:
            time_str = f"{remaining_time}秒"
        
        return f"欢迎, {username}! 会话有效期: {time_str}"
    
    def _create_failure_message(self, result: Dict[str, Any], error_analysis: Dict[str, Any]) -> str:
        """
        创建失败消息
        
        Args:
            result: 认证结果
            error_analysis: 错误分析
            
        Returns:
            str: 失败消息
        """
        base_message = result.get('message', '认证失败')
        error_type = error_analysis.get('error_type', 'unknown')
        
        # 根据错误类型添加额外信息
        if error_type == 'invalid_credentials':
            if 'remaining_attempts' in result:
                base_message += f"\n剩余尝试次数: {result['remaining_attempts']}"
        
        elif error_type == 'account_locked':
            if 'remaining_time' in result:
                remaining_time = result['remaining_time']
                if remaining_time > 60:
                    time_str = f"{remaining_time // 60}分钟{remaining_time % 60}秒"
                else:
                    time_str = f"{remaining_time}秒"
                base_message += f"\n账户锁定剩余时间: {time_str}"
        
        elif error_type == 'network_error':
            base_message += "\n请检查网络连接"
        
        elif error_type == 'server_error':
            base_message += "\n服务器暂时不可用，请稍后重试"
        
        return base_message
    
    def _record_auth_attempt(self, result: Dict[str, Any], success: bool):
        """
        记录认证尝试
        
        Args:
            result: 认证结果
            success: 是否成功
        """
        attempt = {
            'timestamp': datetime.now().isoformat(),
            'success': success,
            'username': result.get('username', result.get('user_info', {}).get('username', 'N/A')),
            'message': result.get('message', ''),
            'status': result.get('status', 'unknown')
        }
        
        self.auth_history.append(attempt)
        
        # 限制历史记录大小
        if len(self.auth_history) > self.max_history_size:
            self.auth_history = self.auth_history[-self.max_history_size:]
    
    def get_auth_history(self, limit: int = 10) -> list:
        """
        获取认证历史
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            list: 认证历史记录
        """
        return self.auth_history[-limit:] if limit > 0 else self.auth_history
    
    def get_auth_statistics(self) -> Dict[str, Any]:
        """
        获取认证统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        if not self.auth_history:
            return {
                'total_attempts': 0,
                'successful_attempts': 0,
                'failed_attempts': 0,
                'success_rate': 0,
                'last_attempt': None
            }
        
        total = len(self.auth_history)
        successful = sum(1 for attempt in self.auth_history if attempt['success'])
        failed = total - successful
        success_rate = (successful / total) * 100 if total > 0 else 0
        
        return {
            'total_attempts': total,
            'successful_attempts': successful,
            'failed_attempts': failed,
            'success_rate': round(success_rate, 2),
            'last_attempt': self.auth_history[-1] if self.auth_history else None
        }
    
    def clear_history(self):
        """清除认证历史"""
        self.auth_history.clear()
        self.last_auth_result = None


class ErrorAnalyzer:
    """错误分析器"""
    
    def analyze_auth_error(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析认证错误
        
        Args:
            result: 认证结果
            
        Returns:
            Dict[str, Any]: 错误分析结果
        """
        message = result.get('message', '').lower()
        status = result.get('status', '')
        
        # 分析错误类型
        error_type = self._classify_error(message, status, result)
        
        # 生成建议
        suggestions = self._generate_suggestions(error_type, result)
        
        # 评估严重程度
        severity = self._assess_severity(error_type, result)
        
        return {
            'error_type': error_type,
            'severity': severity,
            'suggestions': suggestions,
            'analysis_time': datetime.now().isoformat()
        }
    
    def _classify_error(self, message: str, status: str, result: Dict[str, Any]) -> str:
        """
        分类错误类型
        
        Args:
            message: 错误消息
            status: 状态
            result: 完整结果
            
        Returns:
            str: 错误类型
        """
        if status == AUTH_STATUS['LOCKED'] or '锁定' in message:
            return 'account_locked'
        
        if '用户名' in message and '密码' in message:
            return 'invalid_credentials'
        
        if '网络' in message or '连接' in message or '超时' in message:
            return 'network_error'
        
        if '服务器' in message or '系统' in message:
            return 'server_error'
        
        if '格式' in message or '验证' in message:
            return 'input_validation'
        
        if '会话' in message or '令牌' in message:
            return 'session_error'
        
        return 'unknown'
    
    def _generate_suggestions(self, error_type: str, result: Dict[str, Any]) -> list:
        """
        生成建议
        
        Args:
            error_type: 错误类型
            result: 认证结果
            
        Returns:
            list: 建议列表
        """
        suggestions = []
        
        if error_type == 'invalid_credentials':
            suggestions.extend([
                "请检查用户名和密码是否正确",
                "确认大小写是否正确",
                "如果忘记密码，请联系管理员重置"
            ])
            
            if 'remaining_attempts' in result:
                remaining = result['remaining_attempts']
                if remaining <= 2:
                    suggestions.append(f"注意：仅剩{remaining}次尝试机会，请谨慎输入")
        
        elif error_type == 'account_locked':
            suggestions.extend([
                "账户已被锁定，请等待锁定时间结束",
                "如需立即解锁，请联系系统管理员",
                "避免频繁的错误尝试"
            ])
        
        elif error_type == 'network_error':
            suggestions.extend([
                "检查网络连接是否正常",
                "确认服务器地址和端口是否正确",
                "尝试重新连接服务器",
                "检查防火墙设置"
            ])
        
        elif error_type == 'server_error':
            suggestions.extend([
                "服务器可能暂时不可用，请稍后重试",
                "联系系统管理员检查服务器状态",
                "确认服务器服务是否正常运行"
            ])
        
        elif error_type == 'input_validation':
            suggestions.extend([
                "检查输入格式是否正确",
                "用户名应为3-50位字母数字下划线",
                "密码应符合复杂度要求"
            ])
        
        elif error_type == 'session_error':
            suggestions.extend([
                "会话可能已过期，请重新登录",
                "检查系统时间是否正确",
                "清除本地会话信息后重试"
            ])
        
        else:
            suggestions.extend([
                "请检查输入信息是否正确",
                "尝试重新连接服务器",
                "如问题持续，请联系技术支持"
            ])
        
        return suggestions
    
    def _assess_severity(self, error_type: str, result: Dict[str, Any]) -> str:
        """
        评估错误严重程度
        
        Args:
            error_type: 错误类型
            result: 认证结果
            
        Returns:
            str: 严重程度 (low, medium, high, critical)
        """
        if error_type == 'account_locked':
            return 'high'
        
        if error_type == 'network_error':
            return 'medium'
        
        if error_type == 'server_error':
            return 'high'
        
        if error_type == 'invalid_credentials':
            remaining = result.get('remaining_attempts', 5)
            if remaining <= 1:
                return 'high'
            elif remaining <= 2:
                return 'medium'
            else:
                return 'low'
        
        return 'low'


class ConnectionMonitor:
    """连接状态监控器"""
    
    def __init__(self):
        """初始化连接监控器"""
        self.is_monitoring = False
        self.monitor_thread = None
        self.callbacks = []
        self.check_interval = 5.0  # 检查间隔（秒）
        
    def start_monitoring(self, network_client, callback: Callable = None):
        """
        开始监控连接状态
        
        Args:
            network_client: 网络客户端实例
            callback: 状态变化回调函数
        """
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        if callback:
            self.callbacks.append(callback)
        
        def monitor_loop():
            """监控循环"""
            last_connected = None
            last_authenticated = None
            
            while self.is_monitoring:
                try:
                    current_connected = network_client.is_connected()
                    current_authenticated = network_client.is_authenticated()
                    
                    # 检查连接状态变化
                    if last_connected is not None and current_connected != last_connected:
                        self._notify_callbacks('connection_changed', {
                            'connected': current_connected,
                            'timestamp': datetime.now().isoformat()
                        })
                    
                    # 检查认证状态变化
                    if last_authenticated is not None and current_authenticated != last_authenticated:
                        self._notify_callbacks('auth_changed', {
                            'authenticated': current_authenticated,
                            'timestamp': datetime.now().isoformat()
                        })
                    
                    last_connected = current_connected
                    last_authenticated = current_authenticated
                    
                    time.sleep(self.check_interval)
                    
                except Exception as e:
                    print(f"连接监控错误: {e}")
                    time.sleep(self.check_interval)
        
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        self.callbacks.clear()
    
    def _notify_callbacks(self, event_type: str, data: Dict[str, Any]):
        """通知回调函数"""
        for callback in self.callbacks:
            try:
                callback(event_type, data)
            except Exception as e:
                print(f"回调函数错误: {e}")
    
    def add_callback(self, callback: Callable):
        """添加回调函数"""
        if callback not in self.callbacks:
            self.callbacks.append(callback)
"""
用户管理GUI模块
实现用户列表显示、添加、删除、修改用户的界面和用户信息验证
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from typing import Dict, Any, Optional, List
from datetime import datetime

from server.authentication_handler import AuthenticationHandler
from server.security_utils import SecurityUtils


class UserManagementGUI:
    """用户管理GUI"""
    
    def __init__(self, parent_frame: ttk.Frame, auth_handler: AuthenticationHandler):
        """
        初始化用户管理GUI
        
        Args:
            parent_frame: 父框架
            auth_handler: 认证处理器
        """
        self.parent_frame = parent_frame
        self.auth_handler = auth_handler
        
        # GUI组件
        self.user_tree = None
        self.search_var = None
        self.filter_var = None
        
        # 用户数据
        self.users_data = []
        self.filtered_users = []
        
        self._create_widgets()
        self._refresh_user_list()
    
    def _create_widgets(self):
        """创建GUI组件"""
        # 工具栏框架
        toolbar_frame = ttk.Frame(self.parent_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 搜索框架
        search_frame = ttk.Frame(toolbar_frame)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(search_frame, text="搜索用户:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self._on_search_changed)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        # 过滤器
        ttk.Label(search_frame, text="状态过滤:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.filter_var = tk.StringVar(value="全部")
        filter_combo = ttk.Combobox(search_frame, textvariable=self.filter_var, 
                                   values=["全部", "活跃", "禁用", "锁定"], 
                                   state="readonly", width=10)
        filter_combo.pack(side=tk.LEFT, padx=(0, 10))
        filter_combo.bind('<<ComboboxSelected>>', self._on_filter_changed)
        
        # 操作按钮框架
        button_frame = ttk.Frame(toolbar_frame)
        button_frame.pack(side=tk.RIGHT)
        
        ttk.Button(button_frame, text="添加用户", 
                  command=self._add_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="编辑用户", 
                  command=self._edit_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="删除用户", 
                  command=self._delete_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="重置密码", 
                  command=self._reset_password).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="刷新列表", 
                  command=self._refresh_user_list).pack(side=tk.LEFT)
        
        # 用户列表框架
        list_frame = ttk.LabelFrame(self.parent_frame, text="用户列表", padding=5)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 用户表格
        columns = ("username", "created_at", "last_login", "status", "failed_attempts", "is_locked")
        self.user_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        headers = {
            "username": ("用户名", 120),
            "created_at": ("创建时间", 150),
            "last_login": ("最后登录", 150),
            "status": ("状态", 80),
            "failed_attempts": ("失败次数", 80),
            "is_locked": ("锁定状态", 80)
        }
        
        for col, (header, width) in headers.items():
            self.user_tree.heading(col, text=header, command=lambda c=col: self._sort_by_column(c))
            self.user_tree.column(col, width=width, anchor=tk.CENTER if col in ["status", "failed_attempts", "is_locked"] else tk.W)
        
        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.user_tree.yview)
        scrollbar_h = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.user_tree.xview)
        self.user_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # 布局
        self.user_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar_v.grid(row=0, column=1, sticky="ns")
        scrollbar_h.grid(row=1, column=0, sticky="ew")
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定双击事件
        self.user_tree.bind("<Double-1>", lambda e: self._edit_user())
        
        # 右键菜单
        self._create_context_menu()
        
        # 状态栏
        status_frame = ttk.Frame(self.parent_frame)
        status_frame.pack(fill=tk.X)
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)
        
        self.user_count_label = ttk.Label(status_frame, text="用户数: 0")
        self.user_count_label.pack(side=tk.RIGHT)
    
    def _create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.user_tree, tearoff=0)
        self.context_menu.add_command(label="编辑用户", command=self._edit_user)
        self.context_menu.add_command(label="重置密码", command=self._reset_password)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="启用/禁用", command=self._toggle_user_status)
        self.context_menu.add_command(label="解锁用户", command=self._unlock_user)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除用户", command=self._delete_user)
        self.context_menu.add_command(label="查看详情", command=self._view_user_details)
        
        # 绑定右键事件
        self.user_tree.bind("<Button-3>", self._show_context_menu)
    
    def _show_context_menu(self, event):
        """显示右键菜单"""
        # 选择点击的项目
        item = self.user_tree.identify_row(event.y)
        if item:
            self.user_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def _refresh_user_list(self):
        """刷新用户列表"""
        try:
            self.status_label.config(text="正在加载用户列表...")
            
            # 获取用户列表
            self.users_data = self.auth_handler.get_user_list()
            
            # 应用过滤器
            self._apply_filters()
            
            self.status_label.config(text="用户列表加载完成")
            
        except Exception as e:
            self.status_label.config(text=f"加载失败: {str(e)}")
            messagebox.showerror("加载失败", f"加载用户列表失败:\n{str(e)}")
    
    def _apply_filters(self):
        """应用搜索和过滤器"""
        search_text = self.search_var.get().lower()
        filter_status = self.filter_var.get()
        
        # 过滤用户数据
        self.filtered_users = []
        
        for user in self.users_data:
            # 搜索过滤
            if search_text and search_text not in user['username'].lower():
                continue
            
            # 状态过滤
            if filter_status != "全部":
                if filter_status == "活跃" and not user['is_active']:
                    continue
                elif filter_status == "禁用" and user['is_active']:
                    continue
                elif filter_status == "锁定" and not user.get('is_locked', False):
                    continue
            
            self.filtered_users.append(user)
        
        # 更新表格显示
        self._update_user_tree()
    
    def _update_user_tree(self):
        """更新用户表格显示"""
        # 清空现有项目
        for item in self.user_tree.get_children():
            self.user_tree.delete(item)
        
        # 添加过滤后的用户
        for user in self.filtered_users:
            # 格式化显示数据
            username = user['username']
            created_at = self._format_datetime(user['created_at'])
            last_login = self._format_datetime(user['last_login']) if user['last_login'] else "从未登录"
            status = "活跃" if user['is_active'] else "禁用"
            failed_attempts = str(user['failed_attempts'])
            is_locked = "是" if user.get('is_locked', False) else "否"
            
            # 插入行
            item = self.user_tree.insert("", tk.END, values=(
                username, created_at, last_login, status, failed_attempts, is_locked
            ))
            
            # 设置行颜色
            if not user['is_active']:
                self.user_tree.set(item, "status", "禁用")
                # 可以设置不同的标签颜色
            elif user.get('is_locked', False):
                self.user_tree.set(item, "is_locked", "是")
        
        # 更新用户计数
        self.user_count_label.config(text=f"用户数: {len(self.filtered_users)}")
    
    def _format_datetime(self, datetime_str: str) -> str:
        """格式化日期时间字符串"""
        if not datetime_str:
            return ""
        
        try:
            dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return datetime_str
    
    def _on_search_changed(self, *args):
        """搜索框内容变化事件"""
        self._apply_filters()
    
    def _on_filter_changed(self, event=None):
        """过滤器变化事件"""
        self._apply_filters()
    
    def _sort_by_column(self, column):
        """按列排序"""
        # 获取当前数据
        data = [(self.user_tree.set(item, column), item) for item in self.user_tree.get_children()]
        
        # 排序
        data.sort(reverse=False)
        
        # 重新排列
        for index, (val, item) in enumerate(data):
            self.user_tree.move(item, '', index)
    
    def _get_selected_user(self) -> Optional[str]:
        """获取选中的用户名"""
        selection = self.user_tree.selection()
        if not selection:
            return None
        
        item = selection[0]
        return self.user_tree.item(item, 'values')[0]  # 用户名在第一列
    
    def _add_user(self):
        """添加用户"""
        dialog = UserEditDialog(self.parent_frame, "添加用户")
        result = dialog.show()
        
        if result:
            username = result['username']
            password = result['password']
            
            # 创建用户
            create_result = self.auth_handler.create_user(username, password, "管理员")
            
            if create_result['success']:
                messagebox.showinfo("成功", f"用户 {username} 创建成功")
                self._refresh_user_list()
            else:
                messagebox.showerror("失败", create_result['message'])
    
    def _edit_user(self):
        """编辑用户"""
        username = self._get_selected_user()
        if not username:
            messagebox.showwarning("未选择", "请先选择要编辑的用户")
            return
        
        # 获取用户详细信息
        user_info = self.auth_handler.user_manager.get_user_info(username)
        if not user_info:
            messagebox.showerror("错误", "获取用户信息失败")
            return
        
        dialog = UserEditDialog(self.parent_frame, "编辑用户", user_info)
        result = dialog.show()
        
        if result:
            # 这里可以实现更新用户信息的逻辑
            # 目前只支持重置密码
            if 'password' in result and result['password']:
                self._reset_user_password(username, result['password'])
    
    def _delete_user(self):
        """删除用户"""
        username = self._get_selected_user()
        if not username:
            messagebox.showwarning("未选择", "请先选择要删除的用户")
            return
        
        # 确认删除
        if not messagebox.askyesno("确认删除", f"确定要删除用户 {username} 吗？\n此操作不可撤销！"):
            return
        
        # 删除用户
        delete_result = self.auth_handler.delete_user(username, "管理员")
        
        if delete_result['success']:
            messagebox.showinfo("成功", f"用户 {username} 删除成功")
            self._refresh_user_list()
        else:
            messagebox.showerror("失败", delete_result['message'])
    
    def _reset_password(self):
        """重置密码"""
        username = self._get_selected_user()
        if not username:
            messagebox.showwarning("未选择", "请先选择要重置密码的用户")
            return
        
        # 输入新密码
        dialog = PasswordDialog(self.parent_frame, f"重置用户 {username} 的密码")
        new_password = dialog.show()
        
        if new_password:
            self._reset_user_password(username, new_password)
    
    def _reset_user_password(self, username: str, new_password: str):
        """重置用户密码"""
        change_result = self.auth_handler.change_password(username, new_password, "管理员")
        
        if change_result['success']:
            messagebox.showinfo("成功", f"用户 {username} 的密码重置成功")
            self._refresh_user_list()
        else:
            messagebox.showerror("失败", change_result['message'])
    
    def _toggle_user_status(self):
        """切换用户状态"""
        username = self._get_selected_user()
        if not username:
            messagebox.showwarning("未选择", "请先选择要操作的用户")
            return
        
        # 切换用户状态
        toggle_result = self.auth_handler.user_manager.toggle_user_status(username)
        
        if toggle_result['success']:
            status = "启用" if toggle_result['is_active'] else "禁用"
            messagebox.showinfo("成功", f"用户 {username} 已{status}")
            self._refresh_user_list()
        else:
            messagebox.showerror("失败", toggle_result['message'])
    
    def _unlock_user(self):
        """解锁用户"""
        username = self._get_selected_user()
        if not username:
            messagebox.showwarning("未选择", "请先选择要解锁的用户")
            return
        
        # 解锁用户
        unlock_result = self.auth_handler.unlock_user(username, "管理员")
        
        if unlock_result['success']:
            messagebox.showinfo("成功", f"用户 {username} 解锁成功")
            self._refresh_user_list()
        else:
            messagebox.showerror("失败", unlock_result['message'])
    
    def _view_user_details(self):
        """查看用户详情"""
        username = self._get_selected_user()
        if not username:
            messagebox.showwarning("未选择", "请先选择要查看的用户")
            return
        
        # 获取用户详细信息
        user_info = self.auth_handler.user_manager.get_user_info(username)
        if not user_info:
            messagebox.showerror("错误", "获取用户信息失败")
            return
        
        # 显示详情对话框
        UserDetailsDialog(self.parent_frame, user_info).show()


class UserEditDialog:
    """用户编辑对话框"""
    
    def __init__(self, parent, title: str, user_info: Dict[str, Any] = None):
        """
        初始化用户编辑对话框
        
        Args:
            parent: 父窗口
            title: 对话框标题
            user_info: 用户信息（编辑模式）
        """
        self.parent = parent
        self.title = title
        self.user_info = user_info
        self.result = None
        
        self.dialog = None
        self.username_var = None
        self.password_var = None
        self.confirm_password_var = None
    
    def show(self) -> Optional[Dict[str, Any]]:
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
        self._create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
        
        return self.result
    
    def _create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 用户名
        ttk.Label(main_frame, text="用户名:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.username_var = tk.StringVar()
        username_entry = ttk.Entry(main_frame, textvariable=self.username_var, width=30)
        username_entry.grid(row=0, column=1, sticky=tk.W, pady=(0, 10))
        
        if self.user_info:
            self.username_var.set(self.user_info['username'])
            username_entry.config(state='readonly')  # 编辑模式下用户名不可修改
        
        # 密码
        ttk.Label(main_frame, text="密码:").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        self.password_var = tk.StringVar()
        password_entry = ttk.Entry(main_frame, textvariable=self.password_var, show="*", width=30)
        password_entry.grid(row=1, column=1, sticky=tk.W, pady=(0, 10))
        
        # 确认密码
        ttk.Label(main_frame, text="确认密码:").grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        self.confirm_password_var = tk.StringVar()
        confirm_entry = ttk.Entry(main_frame, textvariable=self.confirm_password_var, show="*", width=30)
        confirm_entry.grid(row=2, column=1, sticky=tk.W, pady=(0, 10))
        
        # 密码强度提示
        if not self.user_info:  # 只在添加用户时显示
            strength_frame = ttk.LabelFrame(main_frame, text="密码要求", padding=10)
            strength_frame.grid(row=3, column=0, columnspan=2, sticky=tk.EW, pady=(10, 20))
            
            requirements = [
                "• 长度至少8位",
                "• 包含大小写字母",
                "• 包含数字",
                "• 建议包含特殊字符"
            ]
            
            for req in requirements:
                ttk.Label(strength_frame, text=req, font=("Arial", 8)).pack(anchor=tk.W)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(20, 0))
        
        ttk.Button(button_frame, text="确定", command=self._on_ok).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.LEFT)
        
        # 设置焦点
        if not self.user_info:
            username_entry.focus()
        else:
            password_entry.focus()
    
    def _on_ok(self):
        """确定按钮事件"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        confirm_password = self.confirm_password_var.get()
        
        # 验证输入
        if not self.user_info and not username:
            messagebox.showerror("错误", "用户名不能为空")
            return
        
        if not password:
            if not self.user_info:
                messagebox.showerror("错误", "密码不能为空")
                return
            else:
                # 编辑模式下，密码为空表示不修改密码
                self.result = {'username': username}
                self.dialog.destroy()
                return
        
        if password != confirm_password:
            messagebox.showerror("错误", "两次输入的密码不一致")
            return
        
        # 验证用户名格式（仅添加用户时）
        if not self.user_info:
            username_validation = SecurityUtils.validate_username(username)
            if not username_validation['is_valid']:
                messagebox.showerror("错误", username_validation['message'])
                return
        
        # 验证密码强度
        password_validation = SecurityUtils.validate_password_strength(password)
        if not password_validation['is_valid']:
            messagebox.showerror("错误", "密码不符合要求:\n" + "\n".join(password_validation['messages']))
            return
        
        # 返回结果
        self.result = {
            'username': username,
            'password': password
        }
        
        self.dialog.destroy()
    
    def _on_cancel(self):
        """取消按钮事件"""
        self.dialog.destroy()


class PasswordDialog:
    """密码输入对话框"""
    
    def __init__(self, parent, title: str):
        """
        初始化密码对话框
        
        Args:
            parent: 父窗口
            title: 对话框标题
        """
        self.parent = parent
        self.title = title
        self.result = None
    
    def show(self) -> Optional[str]:
        """显示对话框"""
        dialog = tk.Toplevel(self.parent)
        dialog.title(self.title)
        dialog.geometry("350x200")
        dialog.resizable(False, False)
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        main_frame = ttk.Frame(dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 密码输入
        ttk.Label(main_frame, text="新密码:").pack(anchor=tk.W, pady=(0, 5))
        password_var = tk.StringVar()
        password_entry = ttk.Entry(main_frame, textvariable=password_var, show="*", width=40)
        password_entry.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(main_frame, text="确认密码:").pack(anchor=tk.W, pady=(0, 5))
        confirm_var = tk.StringVar()
        confirm_entry = ttk.Entry(main_frame, textvariable=confirm_var, show="*", width=40)
        confirm_entry.pack(fill=tk.X, pady=(0, 20))
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack()
        
        def on_ok():
            password = password_var.get()
            confirm = confirm_var.get()
            
            if not password:
                messagebox.showerror("错误", "密码不能为空")
                return
            
            if password != confirm:
                messagebox.showerror("错误", "两次输入的密码不一致")
                return
            
            # 验证密码强度
            validation = SecurityUtils.validate_password_strength(password)
            if not validation['is_valid']:
                messagebox.showerror("错误", "密码不符合要求:\n" + "\n".join(validation['messages']))
                return
            
            self.result = password
            dialog.destroy()
        
        def on_cancel():
            dialog.destroy()
        
        ttk.Button(button_frame, text="确定", command=on_ok).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=on_cancel).pack(side=tk.LEFT)
        
        password_entry.focus()
        dialog.wait_window()
        
        return self.result


class UserDetailsDialog:
    """用户详情对话框"""
    
    def __init__(self, parent, user_info: Dict[str, Any]):
        """
        初始化用户详情对话框
        
        Args:
            parent: 父窗口
            user_info: 用户信息
        """
        self.parent = parent
        self.user_info = user_info
    
    def show(self):
        """显示对话框"""
        dialog = tk.Toplevel(self.parent)
        dialog.title(f"用户详情 - {self.user_info['username']}")
        dialog.geometry("500x400")
        dialog.resizable(False, False)
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        main_frame = ttk.Frame(dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 用户信息
        info_frame = ttk.LabelFrame(main_frame, text="基本信息", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        info_data = [
            ("用户ID:", str(self.user_info['id'])),
            ("用户名:", self.user_info['username']),
            ("创建时间:", self._format_datetime(self.user_info['created_at'])),
            ("最后登录:", self._format_datetime(self.user_info['last_login']) if self.user_info['last_login'] else "从未登录"),
            ("账户状态:", "活跃" if self.user_info['is_active'] else "禁用"),
            ("失败次数:", str(self.user_info['failed_attempts'])),
            ("锁定状态:", "是" if self.user_info.get('is_locked', False) else "否")
        ]
        
        for i, (label, value) in enumerate(info_data):
            ttk.Label(info_frame, text=label).grid(row=i, column=0, sticky=tk.W, padx=(0, 10), pady=2)
            ttk.Label(info_frame, text=value, font=("Arial", 9, "bold")).grid(row=i, column=1, sticky=tk.W, pady=2)
        
        # 关闭按钮
        ttk.Button(main_frame, text="关闭", command=dialog.destroy).pack(pady=(20, 0))
    
    def _format_datetime(self, datetime_str: str) -> str:
        """格式化日期时间"""
        if not datetime_str:
            return ""
        
        try:
            dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return datetime_str
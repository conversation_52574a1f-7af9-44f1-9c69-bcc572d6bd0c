#!/usr/bin/env python3
"""
用户注册管理器
处理用户注册相关的所有逻辑
"""

import re
import hashlib
import secrets
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal

from server.database_manager import DatabaseManager


@dataclass
class UserRegistrationData:
    """用户注册数据模型"""
    username: str
    password: str
    email: Optional[str] = None
    initial_balance: Decimal = Decimal('0.00')
    status: str = 'active'
    registration_ip: Optional[str] = None


class RegistrationError(Exception):
    """注册相关异常基类"""
    pass


class UsernameExistsError(RegistrationError):
    """用户名已存在异常"""
    pass


class WeakPasswordError(RegistrationError):
    """密码强度不足异常"""
    pass


class InvalidEmailError(RegistrationError):
    """邮箱格式无效异常"""
    pass


class InvalidUsernameError(RegistrationError):
    """用户名格式无效异常"""
    pass


class UserRegistrationManager:
    """用户注册管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        
        # 注册配置
        self.config = {
            'min_username_length': 3,
            'max_username_length': 20,
            'min_password_length': 6,
            'max_password_length': 50,
            'initial_balance': Decimal('0.00'),
            'require_email': False,
            'username_pattern': r'^[a-zA-Z0-9_-]+$',
            'email_pattern': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        }
    
    def register_user(self, username: str, password: str, confirm_password: str = None, 
                     email: str = None, registration_ip: str = None) -> Dict[str, Any]:
        """
        注册新用户
        
        Args:
            username: 用户名
            password: 密码
            confirm_password: 确认密码
            email: 邮箱地址（可选）
            registration_ip: 注册IP地址
            
        Returns:
            Dict: 注册结果
        """
        try:
            # 验证注册数据
            validation_errors = self.validate_registration_data(
                username, password, confirm_password, email
            )
            
            if validation_errors:
                return {
                    'success': False,
                    'error': 'validation_failed',
                    'message': '注册数据验证失败',
                    'errors': validation_errors
                }
            
            # 检查用户名是否可用
            if not self.check_username_availability(username):
                return {
                    'success': False,
                    'error': 'username_exists',
                    'message': f'用户名 "{username}" 已存在'
                }
            
            # 创建用户注册数据
            registration_data = UserRegistrationData(
                username=username,
                password=password,
                email=email,
                initial_balance=self.config['initial_balance'],
                registration_ip=registration_ip
            )
            
            # 创建用户账户
            success = self.create_user_account(registration_data)
            
            if success:
                # 发送欢迎消息（可选）
                self.send_welcome_message(username)
                
                return {
                    'success': True,
                    'message': f'用户 "{username}" 注册成功',
                    'username': username,
                    'initial_balance': float(self.config['initial_balance'])
                }
            else:
                return {
                    'success': False,
                    'error': 'creation_failed',
                    'message': '用户账户创建失败'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': 'system_error',
                'message': f'注册过程中发生错误: {str(e)}'
            }
    
    def validate_registration_data(self, username: str, password: str, 
                                 confirm_password: str = None, email: str = None) -> List[str]:
        """
        验证注册数据
        
        Args:
            username: 用户名
            password: 密码
            confirm_password: 确认密码
            email: 邮箱地址
            
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        
        # 验证用户名
        username_errors = self.validate_username(username)
        errors.extend(username_errors)
        
        # 验证密码
        password_errors = self.validate_password(password)
        errors.extend(password_errors)
        
        # 验证确认密码
        if confirm_password is not None and password != confirm_password:
            errors.append('两次输入的密码不一致')
        
        # 验证邮箱
        if email:
            email_errors = self.validate_email(email)
            errors.extend(email_errors)
        elif self.config['require_email']:
            errors.append('邮箱地址是必填项')
        
        return errors
    
    def validate_username(self, username: str) -> List[str]:
        """
        验证用户名格式
        
        Args:
            username: 用户名
            
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        
        if not username:
            errors.append('用户名不能为空')
            return errors
        
        # 检查长度
        if len(username) < self.config['min_username_length']:
            errors.append(f'用户名长度不能少于{self.config["min_username_length"]}个字符')
        
        if len(username) > self.config['max_username_length']:
            errors.append(f'用户名长度不能超过{self.config["max_username_length"]}个字符')
        
        # 检查格式
        if not re.match(self.config['username_pattern'], username):
            errors.append('用户名只能包含字母、数字、下划线和连字符')
        
        # 检查是否以数字开头
        if username[0].isdigit():
            errors.append('用户名不能以数字开头')
        
        # 检查保留用户名
        reserved_usernames = ['admin', 'root', 'system', 'test', 'guest', 'anonymous']
        if username.lower() in reserved_usernames:
            errors.append('该用户名为系统保留，请选择其他用户名')
        
        return errors
    
    def validate_password(self, password: str) -> List[str]:
        """
        验证密码强度
        
        Args:
            password: 密码
            
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        
        if not password:
            errors.append('密码不能为空')
            return errors
        
        # 检查长度
        if len(password) < self.config['min_password_length']:
            errors.append(f'密码长度不能少于{self.config["min_password_length"]}个字符')
        
        if len(password) > self.config['max_password_length']:
            errors.append(f'密码长度不能超过{self.config["max_password_length"]}个字符')
        
        # 检查密码强度
        strength_errors = self.check_password_strength(password)
        errors.extend(strength_errors)
        
        return errors
    
    def check_password_strength(self, password: str) -> List[str]:
        """
        检查密码强度
        
        Args:
            password: 密码
            
        Returns:
            List[str]: 强度检查错误列表
        """
        errors = []
        
        # 检查是否包含数字
        if not re.search(r'\d', password):
            errors.append('密码必须包含至少一个数字')
        
        # 检查是否包含字母
        if not re.search(r'[a-zA-Z]', password):
            errors.append('密码必须包含至少一个字母')
        
        # 检查是否包含特殊字符（可选，根据安全要求）
        if len(password) >= 8 and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            # 对于8位以上密码，建议包含特殊字符
            pass  # 暂时不强制要求特殊字符
        
        # 检查常见弱密码
        weak_passwords = [
            '123456', 'password', '123456789', '12345678', '12345',
            '1234567', '1234567890', 'qwerty', 'abc123', 'password123'
        ]
        
        if password.lower() in weak_passwords:
            errors.append('密码过于简单，请使用更复杂的密码')
        
        # 检查是否为纯数字或纯字母
        if password.isdigit():
            errors.append('密码不能为纯数字')
        
        if password.isalpha():
            errors.append('密码不能为纯字母')
        
        return errors
    
    def validate_email(self, email: str) -> List[str]:
        """
        验证邮箱格式
        
        Args:
            email: 邮箱地址
            
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        
        if not email:
            return errors
        
        # 检查邮箱格式
        if not re.match(self.config['email_pattern'], email):
            errors.append('邮箱格式不正确')
        
        # 检查邮箱长度
        if len(email) > 255:
            errors.append('邮箱地址过长')
        
        return errors
    
    def check_username_availability(self, username: str) -> bool:
        """
        检查用户名是否可用
        
        Args:
            username: 用户名
            
        Returns:
            bool: True表示可用，False表示已存在
        """
        try:
            user_info = self.db_manager.get_user_info(username)
            is_available = user_info is None
            print(f"用户名 {username} 可用性检查: {'可用' if is_available else '已存在'}")
            return is_available
        except Exception as e:
            print(f"检查用户名可用性时出错: {e}")
            # 如果出错，为了安全起见，假设用户名可用，让数据库约束处理
            return True
    
    def create_user_account(self, registration_data: UserRegistrationData) -> bool:
        """
        创建用户账户
        
        Args:
            registration_data: 用户注册数据
            
        Returns:
            bool: 创建成功返回True
        """
        try:
            # 生成密码哈希
            password_hash = self.hash_password(registration_data.password)
            
            # 注册用户
            success = self.db_manager.register_user(
                username=registration_data.username,
                password_hash=password_hash,
                email=registration_data.email,
                initial_balance=float(registration_data.initial_balance)
            )
            
            if success:
                print(f"用户 {registration_data.username} 注册成功")
                
                # 如果有初始余额，记录充值记录
                if registration_data.initial_balance > 0:
                    self.db_manager.add_recharge_record(
                        username=registration_data.username,
                        amount=float(registration_data.initial_balance),
                        recharge_type='initial',
                        description='注册初始余额'
                    )
                
                return True
            else:
                print(f"用户 {registration_data.username} 注册失败")
                return False
                
        except Exception as e:
            print(f"创建用户账户时出错: {e}")
            return False
    
    def hash_password(self, password: str) -> str:
        """
        生成密码哈希
        
        Args:
            password: 明文密码
            
        Returns:
            str: 密码哈希值
        """
        # 生成随机盐
        salt = secrets.token_hex(16)
        
        # 使用SHA-256哈希密码
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        
        # 返回盐和哈希值的组合
        return f"{salt}:{password_hash}"
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """
        验证密码
        
        Args:
            password: 明文密码
            password_hash: 存储的密码哈希
            
        Returns:
            bool: 验证成功返回True
        """
        try:
            # 分离盐和哈希值
            salt, stored_hash = password_hash.split(':', 1)
            
            # 计算输入密码的哈希值
            calculated_hash = hashlib.sha256((password + salt).encode()).hexdigest()
            
            # 比较哈希值
            return calculated_hash == stored_hash
            
        except Exception as e:
            print(f"密码验证时出错: {e}")
            return False
    
    def send_welcome_message(self, username: str) -> bool:
        """
        发送欢迎消息
        
        Args:
            username: 用户名
            
        Returns:
            bool: 发送成功返回True
        """
        try:
            # 这里可以实现发送欢迎邮件或系统消息的逻辑
            print(f"欢迎新用户: {username}")
            
            # 可以在这里添加更多欢迎逻辑，比如：
            # - 发送欢迎邮件
            # - 创建系统消息
            # - 记录欢迎日志
            
            return True
            
        except Exception as e:
            print(f"发送欢迎消息时出错: {e}")
            return False
    
    def get_registration_statistics(self) -> Dict[str, Any]:
        """
        获取注册统计信息
        
        Returns:
            Dict: 注册统计数据
        """
        try:
            # 获取所有用户
            users = self.db_manager.get_all_users(limit=1000)
            
            # 统计信息
            total_users = len(users)
            active_users = len([u for u in users if u.get('status') == 'active'])
            
            # 按日期统计注册数量
            registration_by_date = {}
            for user in users:
                reg_date = user.get('registration_date', '')
                if reg_date:
                    date_key = reg_date.split(' ')[0]  # 只取日期部分
                    registration_by_date[date_key] = registration_by_date.get(date_key, 0) + 1
            
            return {
                'total_users': total_users,
                'active_users': active_users,
                'inactive_users': total_users - active_users,
                'registration_by_date': registration_by_date
            }
            
        except Exception as e:
            print(f"获取注册统计信息时出错: {e}")
            return {
                'total_users': 0,
                'active_users': 0,
                'inactive_users': 0,
                'registration_by_date': {}
            }
    
    def update_registration_config(self, config_updates: Dict[str, Any]) -> bool:
        """
        更新注册配置
        
        Args:
            config_updates: 配置更新项
            
        Returns:
            bool: 更新成功返回True
        """
        try:
            # 验证配置项
            valid_keys = set(self.config.keys())
            update_keys = set(config_updates.keys())
            
            invalid_keys = update_keys - valid_keys
            if invalid_keys:
                print(f"无效的配置项: {invalid_keys}")
                return False
            
            # 更新配置
            self.config.update(config_updates)
            print(f"注册配置已更新: {config_updates}")
            
            return True
            
        except Exception as e:
            print(f"更新注册配置时出错: {e}")
            return False
    
    def get_registration_config(self) -> Dict[str, Any]:
        """
        获取当前注册配置
        
        Returns:
            Dict: 注册配置
        """
        return self.config.copy()


def main():
    """测试函数"""
    from server.database_manager import DatabaseManager
    
    # 创建测试数据库管理器
    db_manager = DatabaseManager("data/test_registration.db")
    
    # 创建注册管理器
    registration_manager = UserRegistrationManager(db_manager)
    
    # 测试注册功能
    print("测试用户注册功能...")
    
    # 测试正常注册
    result = registration_manager.register_user(
        username="testuser123",
        password="password123",
        confirm_password="password123",
        email="<EMAIL>"
    )
    
    print(f"注册结果: {result}")
    
    # 测试重复注册
    result2 = registration_manager.register_user(
        username="testuser123",
        password="password456",
        confirm_password="password456"
    )
    
    print(f"重复注册结果: {result2}")
    
    # 测试弱密码
    result3 = registration_manager.register_user(
        username="testuser456",
        password="123",
        confirm_password="123"
    )
    
    print(f"弱密码注册结果: {result3}")
    
    # 获取统计信息
    stats = registration_manager.get_registration_statistics()
    print(f"注册统计: {stats}")


if __name__ == "__main__":
    main()
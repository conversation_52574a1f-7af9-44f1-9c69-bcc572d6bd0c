#!/usr/bin/env python3
"""
Windows服务集成模块
将网络验证工具注册为Windows服务
"""

import sys
import os
import subprocess
import time
from pathlib import Path


class WindowsServiceManager:
    """Windows服务管理器"""
    
    def __init__(self, service_name: str = "NetworkAuthService"):
        """
        初始化Windows服务管理器
        
        Args:
            service_name: 服务名称
        """
        self.service_name = service_name
        self.service_display_name = "Network Authentication Service"
        self.service_description = "网络验证工具服务器端服务"
        self.project_root = Path(__file__).parent.parent
        self.python_exe = sys.executable
        self.server_script = self.project_root / "server_main.py"
    
    def is_windows(self) -> bool:
        """检查是否为Windows系统"""
        return os.name == 'nt'
    
    def check_admin_privileges(self) -> bool:
        """检查是否有管理员权限"""
        if not self.is_windows():
            return False
        
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def create_service_script(self) -> str:
        """
        创建Windows服务脚本
        
        Returns:
            str: 服务脚本路径
        """
        service_script_content = f'''#!/usr/bin/env python3
"""
网络验证工具Windows服务脚本
"""

import sys
import os
import time
import threading
import servicemanager
import win32event
import win32service
import win32serviceutil

# 添加项目路径
sys.path.insert(0, r"{self.project_root}")

from server.server_application import ServerApplication


class NetworkAuthService(win32serviceutil.ServiceFramework):
    """网络验证服务"""
    
    _svc_name_ = "{self.service_name}"
    _svc_display_name_ = "{self.service_display_name}"
    _svc_description_ = "{self.service_description}"
    
    def __init__(self, args):
        """初始化服务"""
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.server_app = None
        self.is_running = False
    
    def SvcStop(self):
        """停止服务"""
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        
        # 停止服务器应用
        if self.server_app:
            try:
                self.server_app.shutdown()
                servicemanager.LogMsg(
                    servicemanager.EVENTLOG_INFORMATION_TYPE,
                    servicemanager.PYS_SERVICE_STOPPED,
                    (self._svc_name_, "服务已停止")
                )
            except Exception as e:
                servicemanager.LogErrorMsg(f"停止服务时发生错误: {{e}}")
        
        self.is_running = False
        win32event.SetEvent(self.hWaitStop)
    
    def SvcDoRun(self):
        """运行服务"""
        try:
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STARTED,
                (self._svc_name_, "服务正在启动")
            )
            
            # 创建并启动服务器应用
            self.server_app = ServerApplication()
            
            # 启动服务器
            start_result = self.server_app.start()
            
            if start_result['success']:
                self.is_running = True
                servicemanager.LogMsg(
                    servicemanager.EVENTLOG_INFORMATION_TYPE,
                    servicemanager.PYS_SERVICE_STARTED,
                    (self._svc_name_, f"服务已启动: {{start_result['message']}}")
                )
                
                # 等待停止信号
                win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)
                
            else:
                servicemanager.LogErrorMsg(f"启动服务失败: {{start_result['message']}}")
                
        except Exception as e:
            servicemanager.LogErrorMsg(f"服务运行时发生错误: {{e}}")
            self.SvcStop()


if __name__ == '__main__':
    if len(sys.argv) == 1:
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(NetworkAuthService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        win32serviceutil.HandleCommandLine(NetworkAuthService)
'''
        
        script_path = self.project_root / "service_runner.py"
        
        try:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(service_script_content)
            
            print(f"√ 服务脚本已创建: {script_path}")
            return str(script_path)
            
        except Exception as e:
            print(f"× 创建服务脚本失败: {e}")
            return None
    
    def install_service(self) -> bool:
        """
        安装Windows服务
        
        Returns:
            bool: 安装成功返回True
        """
        if not self.is_windows():
            print("× 此功能仅支持Windows系统")
            return False
        
        if not self.check_admin_privileges():
            print("× 需要管理员权限来安装服务")
            print("请以管理员身份运行此脚本")
            return False
        
        try:
            # 检查是否已安装pywin32
            try:
                import win32serviceutil
                import win32service
                import servicemanager
            except ImportError:
                print("× 缺少pywin32模块，正在尝试安装...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pywin32'])
                print("√ pywin32安装完成")
            
            # 创建服务脚本
            script_path = self.create_service_script()
            if not script_path:
                return False
            
            # 安装服务
            cmd = [
                self.python_exe,
                script_path,
                '--startup=auto',
                'install'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"√ 服务 {self.service_name} 安装成功")
                print(f"  显示名称: {self.service_display_name}")
                print(f"  描述: {self.service_description}")
                return True
            else:
                print(f"× 服务安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"× 安装服务时发生错误: {e}")
            return False
    
    def uninstall_service(self) -> bool:
        """
        卸载Windows服务
        
        Returns:
            bool: 卸载成功返回True
        """
        if not self.is_windows():
            print("× 此功能仅支持Windows系统")
            return False
        
        if not self.check_admin_privileges():
            print("× 需要管理员权限来卸载服务")
            return False
        
        try:
            script_path = self.project_root / "service_runner.py"
            
            if not script_path.exists():
                print("× 服务脚本不存在")
                return False
            
            # 停止服务
            self.stop_service()
            
            # 卸载服务
            cmd = [
                self.python_exe,
                str(script_path),
                'remove'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"√ 服务 {self.service_name} 卸载成功")
                
                # 删除服务脚本
                try:
                    script_path.unlink()
                    print("√ 服务脚本已删除")
                except:
                    pass
                
                return True
            else:
                print(f"× 服务卸载失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"× 卸载服务时发生错误: {e}")
            return False
    
    def start_service(self) -> bool:
        """
        启动Windows服务
        
        Returns:
            bool: 启动成功返回True
        """
        if not self.is_windows():
            print("× 此功能仅支持Windows系统")
            return False
        
        try:
            # 使用sc命令启动服务
            result = subprocess.run(
                ['sc', 'start', self.service_name],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print(f"√ 服务 {self.service_name} 启动成功")
                return True
            else:
                print(f"× 服务启动失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"× 启动服务时发生错误: {e}")
            return False
    
    def stop_service(self) -> bool:
        """
        停止Windows服务
        
        Returns:
            bool: 停止成功返回True
        """
        if not self.is_windows():
            print("× 此功能仅支持Windows系统")
            return False
        
        try:
            # 使用sc命令停止服务
            result = subprocess.run(
                ['sc', 'stop', self.service_name],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print(f"√ 服务 {self.service_name} 停止成功")
                return True
            else:
                # 检查服务是否已经停止
                if "服务未启动" in result.stderr or "service has not been started" in result.stderr:
                    print(f"√ 服务 {self.service_name} 已经停止")
                    return True
                else:
                    print(f"× 服务停止失败: {result.stderr}")
                    return False
                
        except Exception as e:
            print(f"× 停止服务时发生错误: {e}")
            return False
    
    def restart_service(self) -> bool:
        """
        重启Windows服务
        
        Returns:
            bool: 重启成功返回True
        """
        print(f"重启服务 {self.service_name}...")
        
        # 停止服务
        if not self.stop_service():
            return False
        
        # 等待服务完全停止
        time.sleep(2)
        
        # 启动服务
        return self.start_service()
    
    def get_service_status(self) -> dict:
        """
        获取服务状态
        
        Returns:
            dict: 服务状态信息
        """
        if not self.is_windows():
            return {"error": "此功能仅支持Windows系统"}
        
        try:
            # 使用sc命令查询服务状态
            result = subprocess.run(
                ['sc', 'query', self.service_name],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                output = result.stdout
                
                # 解析状态信息
                status_info = {
                    "service_name": self.service_name,
                    "exists": True,
                    "state": "UNKNOWN",
                    "win32_exit_code": "UNKNOWN",
                    "service_exit_code": "UNKNOWN"
                }
                
                for line in output.split('\n'):
                    line = line.strip()
                    if 'STATE' in line:
                        parts = line.split()
                        if len(parts) >= 4:
                            status_info["state"] = parts[3]
                    elif 'WIN32_EXIT_CODE' in line:
                        parts = line.split()
                        if len(parts) >= 2:
                            status_info["win32_exit_code"] = parts[-1]
                    elif 'SERVICE_EXIT_CODE' in line:
                        parts = line.split()
                        if len(parts) >= 2:
                            status_info["service_exit_code"] = parts[-1]
                
                return status_info
            else:
                return {
                    "service_name": self.service_name,
                    "exists": False,
                    "error": result.stderr
                }
                
        except Exception as e:
            return {
                "service_name": self.service_name,
                "error": str(e)
            }
    
    def create_service_installer(self) -> bool:
        """
        创建服务安装批处理文件
        
        Returns:
            bool: 创建成功返回True
        """
        try:
            # 安装脚本
            install_script = f'''@echo off
echo 安装网络验证工具Windows服务...
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，继续安装...
) else (
    echo 错误: 需要管理员权限来安装服务
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 安装服务
echo 正在安装服务...
"{self.python_exe}" "{self.project_root}\\tools\\windows_service.py" install

if %errorLevel% == 0 (
    echo.
    echo 服务安装成功！
    echo 服务名称: {self.service_name}
    echo 显示名称: {self.service_display_name}
    echo.
    echo 您可以通过以下方式管理服务:
    echo 1. 使用Windows服务管理器 (services.msc)
    echo 2. 使用命令行: sc start {self.service_name}
    echo 3. 运行 start_service.bat
    echo.
) else (
    echo.
    echo 服务安装失败！
    echo 请检查错误信息并重试。
    echo.
)

pause
'''
            
            # 卸载脚本
            uninstall_script = f'''@echo off
echo 卸载网络验证工具Windows服务...
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，继续卸载...
) else (
    echo 错误: 需要管理员权限来卸载服务
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 卸载服务
echo 正在卸载服务...
"{self.python_exe}" "{self.project_root}\\tools\\windows_service.py" uninstall

if %errorLevel% == 0 (
    echo.
    echo 服务卸载成功！
    echo.
) else (
    echo.
    echo 服务卸载失败！
    echo 请检查错误信息并重试。
    echo.
)

pause
'''
            
            # 启动服务脚本
            start_script = f'''@echo off
echo 启动网络验证工具服务...
sc start {self.service_name}
if %errorLevel% == 0 (
    echo 服务启动成功！
) else (
    echo 服务启动失败！
)
pause
'''
            
            # 停止服务脚本
            stop_script = f'''@echo off
echo 停止网络验证工具服务...
sc stop {self.service_name}
if %errorLevel% == 0 (
    echo 服务停止成功！
) else (
    echo 服务停止失败！
)
pause
'''
            
            # 写入文件
            scripts = [
                ("install_service.bat", install_script),
                ("uninstall_service.bat", uninstall_script),
                ("start_service.bat", start_script),
                ("stop_service.bat", stop_script)
            ]
            
            for filename, content in scripts:
                file_path = self.project_root / filename
                with open(file_path, 'w', encoding='gbk') as f:
                    f.write(content)
                print(f"√ 创建脚本: {filename}")
            
            return True
            
        except Exception as e:
            print(f"× 创建服务安装脚本失败: {e}")
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Windows服务管理工具')
    parser.add_argument('action', choices=['install', 'uninstall', 'start', 'stop', 'restart', 'status', 'create-scripts'],
                       help='要执行的操作')
    parser.add_argument('--service-name', default='NetworkAuthService',
                       help='服务名称')
    
    args = parser.parse_args()
    
    try:
        print("网络验证工具 - Windows服务管理器")
        print("=" * 50)
        
        # 创建服务管理器
        service_manager = WindowsServiceManager(args.service_name)
        
        # 执行相应操作
        if args.action == 'install':
            success = service_manager.install_service()
        elif args.action == 'uninstall':
            success = service_manager.uninstall_service()
        elif args.action == 'start':
            success = service_manager.start_service()
        elif args.action == 'stop':
            success = service_manager.stop_service()
        elif args.action == 'restart':
            success = service_manager.restart_service()
        elif args.action == 'status':
            status = service_manager.get_service_status()
            print(f"服务状态: {status}")
            success = True
        elif args.action == 'create-scripts':
            success = service_manager.create_service_installer()
        else:
            print(f"未知操作: {args.action}")
            success = False
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return 1
    except Exception as e:
        print(f"操作失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
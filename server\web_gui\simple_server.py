#!/usr/bin/env python3
"""
简单的Web服务器版本的管理界面
当pywebview有问题时的备用方案
"""

import sys
import os
import json
import threading
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import webbrowser
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from server.web_gui.api import WebGUIAPI


class WebGUIHandler(SimpleHTTPRequestHandler):
    """Web GUI HTTP请求处理器"""
    
    def __init__(self, *args, api=None, **kwargs):
        self.api = api
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            # 返回主页面
            self.serve_index()
        elif parsed_path.path.startswith('/api/'):
            # 处理API请求
            self.handle_api_request(parsed_path)
        else:
            # 处理静态文件
            self.serve_static_file()
    
    def do_POST(self):
        """处理POST请求"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path.startswith('/api/'):
            self.handle_api_request(parsed_path, method='POST')
        else:
            self.send_error(404)
    
    def serve_index(self):
        """提供主页面"""
        try:
            template_path = Path(__file__).parent / 'templates' / 'index.html'
            
            if template_path.exists():
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 修改静态文件路径
                content = content.replace('href="static/', 'href="/static/')
                content = content.replace('src="static/', 'src="/static/')
                
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(content.encode('utf-8'))
            else:
                self.send_simple_page()
                
        except Exception as e:
            print(f"提供主页面失败: {e}")
            self.send_error(500)
    
    def send_simple_page(self):
        """发送简单的管理页面"""
        html = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>网络验证工具 - 简单管理界面</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .container { max-width: 800px; margin: 0 auto; }
                .card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0; }
                .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
                .btn-primary { background: #007bff; color: white; }
                .btn-success { background: #28a745; color: white; }
                .btn-danger { background: #dc3545; color: white; }
                .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
                .status.success { background: #d4edda; color: #155724; }
                .status.error { background: #f8d7da; color: #721c24; }
                #output { background: #f8f9fa; padding: 15px; border-radius: 4px; min-height: 200px; font-family: monospace; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>网络验证工具 - 简单管理界面</h1>
                
                <div class="card">
                    <h3>服务器控制</h3>
                    <button class="btn btn-success" onclick="callApi('start_server')">启动服务器</button>
                    <button class="btn btn-danger" onclick="callApi('stop_server')">停止服务器</button>
                    <button class="btn btn-primary" onclick="callApi('get_server_stats')">获取状态</button>
                </div>
                
                <div class="card">
                    <h3>系统信息</h3>
                    <button class="btn btn-primary" onclick="callApi('get_about_info')">关于信息</button>
                    <button class="btn btn-primary" onclick="callApi('get_system_info')">系统信息</button>
                </div>
                
                <div class="card">
                    <h3>输出</h3>
                    <div id="output">点击上面的按钮查看结果...</div>
                </div>
            </div>
            
            <script>
                async function callApi(method) {
                    const output = document.getElementById('output');
                    output.innerHTML = '正在调用 ' + method + '...';
                    
                    try {
                        const response = await fetch('/api/' + method, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        const result = await response.json();
                        output.innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                    } catch (error) {
                        output.innerHTML = '<div class="status error">错误: ' + error.message + '</div>';
                    }
                }
            </script>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_static_file(self):
        """提供静态文件"""
        try:
            # 移除开头的斜杠
            file_path = self.path.lstrip('/')
            
            # 构建完整路径
            static_root = Path(__file__).parent
            full_path = static_root / file_path
            
            if full_path.exists() and full_path.is_file():
                # 确定MIME类型
                if file_path.endswith('.css'):
                    content_type = 'text/css'
                elif file_path.endswith('.js'):
                    content_type = 'application/javascript'
                elif file_path.endswith('.html'):
                    content_type = 'text/html'
                else:
                    content_type = 'application/octet-stream'
                
                with open(full_path, 'rb') as f:
                    content = f.read()
                
                self.send_response(200)
                self.send_header('Content-type', content_type)
                self.end_headers()
                self.wfile.write(content)
            else:
                self.send_error(404)
                
        except Exception as e:
            print(f"提供静态文件失败: {e}")
            self.send_error(500)
    
    def handle_api_request(self, parsed_path, method='GET'):
        """处理API请求"""
        try:
            # 提取API方法名
            api_method = parsed_path.path.replace('/api/', '')
            
            if not hasattr(self.api, api_method):
                self.send_error(404, f"API方法不存在: {api_method}")
                return
            
            # 调用API方法
            api_func = getattr(self.api, api_method)
            result = api_func()
            
            # 返回JSON响应
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            
            json_data = json.dumps(result, ensure_ascii=False, indent=2)
            self.wfile.write(json_data.encode('utf-8'))
            
        except Exception as e:
            print(f"API请求处理失败: {e}")
            error_response = {'error': str(e)}
            
            self.send_response(500)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            
            json_data = json.dumps(error_response, ensure_ascii=False)
            self.wfile.write(json_data.encode('utf-8'))


def create_handler_class(api):
    """创建带API的处理器类"""
    class Handler(WebGUIHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, api=api, **kwargs)
    
    return Handler


def start_simple_web_gui(host='127.0.0.1', port=8080):
    """启动简单的Web GUI"""
    try:
        print("启动简单Web管理界面...")
        
        # 初始化API
        api = WebGUIAPI()
        print("✓ API初始化成功")
        
        # 创建HTTP服务器
        handler_class = create_handler_class(api)
        server = HTTPServer((host, port), handler_class)
        
        print(f"✓ HTTP服务器创建成功")
        print(f"✓ 服务器地址: http://{host}:{port}")
        
        # 在新线程中启动服务器
        server_thread = threading.Thread(target=server.serve_forever, daemon=True)
        server_thread.start()
        
        print("✓ 服务器已启动")
        
        # 自动打开浏览器
        try:
            webbrowser.open(f'http://{host}:{port}')
            print("✓ 已在浏览器中打开管理界面")
        except Exception as e:
            print(f"无法自动打开浏览器: {e}")
            print(f"请手动访问: http://{host}:{port}")
        
        print("\n按 Ctrl+C 停止服务器")
        
        # 保持运行
        try:
            while True:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在关闭服务器...")
            server.shutdown()
            print("✓ 服务器已关闭")
        
        return True
        
    except Exception as e:
        print(f"✗ 启动简单Web GUI失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='简单Web管理界面')
    parser.add_argument('--host', default='127.0.0.1', help='监听地址')
    parser.add_argument('--port', type=int, default=8080, help='监听端口')
    
    args = parser.parse_args()
    
    success = start_simple_web_gui(args.host, args.port)
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
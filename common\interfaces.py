"""
核心接口定义
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

class IDatabaseManager(ABC):
    """数据库管理器接口"""
    
    @abstractmethod
    def create_tables(self) -> None:
        """创建数据库表"""
        pass
    
    @abstractmethod
    def add_user(self, username: str, password_hash: str, salt: str) -> bool:
        """添加用户"""
        pass
    
    @abstractmethod
    def get_user(self, username: str) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        pass
    
    @abstractmethod
    def delete_user(self, username: str) -> bool:
        """删除用户"""
        pass
    
    @abstractmethod
    def update_user(self, username: str, **kwargs) -> bool:
        """更新用户信息"""
        pass

class IAuthenticationHandler(ABC):
    """认证处理器接口"""
    
    @abstractmethod
    def authenticate_user(self, username: str, password: str) -> Dict[str, Any]:
        """用户认证"""
        pass
    
    @abstractmethod
    def hash_password(self, password: str, salt: str) -> str:
        """密码哈希"""
        pass
    
    @abstractmethod
    def generate_session_token(self) -> str:
        """生成会话令牌"""
        pass
    
    @abstractmethod
    def validate_session(self, token: str) -> bool:
        """验证会话"""
        pass

class IConfigManager(ABC):
    """配置管理器接口"""
    
    @abstractmethod
    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        pass
    
    @abstractmethod
    def save_config(self, config: Dict[str, Any]) -> None:
        """保存配置"""
        pass
    
    @abstractmethod
    def get_value(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值"""
        pass
    
    @abstractmethod
    def set_value(self, section: str, key: str, value: Any) -> None:
        """设置配置值"""
        pass

class ILogManager(ABC):
    """日志管理器接口"""
    
    @abstractmethod
    def log_info(self, message: str, **kwargs) -> None:
        """记录信息日志"""
        pass
    
    @abstractmethod
    def log_warning(self, message: str, **kwargs) -> None:
        """记录警告日志"""
        pass
    
    @abstractmethod
    def log_error(self, message: str, **kwargs) -> None:
        """记录错误日志"""
        pass
    
    @abstractmethod
    def log_debug(self, message: str, **kwargs) -> None:
        """记录调试日志"""
        pass
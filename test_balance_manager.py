#!/usr/bin/env python3
"""
测试账户余额管理器功能
"""

import os
import sys
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_manager import DatabaseManager
from server.account_balance_manager import AccountBalanceManager
from server.user_registration_manager import UserRegistrationManager


def test_balance_manager():
    """测试余额管理器功能"""
    print("=" * 60)
    print("测试账户余额管理器功能")
    print("=" * 60)
    
    # 使用测试数据库
    test_db_path = "data/test_balance.db"
    
    # 删除测试数据库（如果存在）
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
        print("已删除旧的测试数据库")
    
    try:
        # 初始化组件
        print("\n1. 初始化组件...")
        db_manager = DatabaseManager(test_db_path)
        balance_manager = AccountBalanceManager(db_manager)
        registration_manager = UserRegistrationManager(db_manager)
        
        print("✓ 组件初始化完成")
        
        # 创建测试用户
        print("\n2. 创建测试用户...")
        
        users_to_create = [
            {'username': 'testuser1', 'password': 'testpass123', 'email': '<EMAIL>'},
            {'username': 'testuser2', 'password': 'testpass456', 'email': '<EMAIL>'},
        ]
        
        for user_data in users_to_create:
            result = registration_manager.register_user(**user_data)
            if result['success']:
                print(f"✓ 用户 {user_data['username']} 创建成功")
            else:
                print(f"✗ 用户 {user_data['username']} 创建失败: {result['message']}")
        
        # 测试余额查询
        print("\n3. 测试余额查询...")
        
        for username in ['testuser1', 'testuser2']:
            balance = balance_manager.get_user_balance(username)
            if balance is not None:
                print(f"✓ 用户 {username} 当前余额: {balance}")
            else:
                print(f"✗ 获取用户 {username} 余额失败")
        
        # 测试不存在用户的余额查询
        balance = balance_manager.get_user_balance('nonexistent')
        if balance is None:
            print("✓ 不存在用户的余额查询正确返回None")
        
        # 测试充值功能
        print("\n4. 测试充值功能...")
        
        # 正常充值
        result = balance_manager.recharge_balance('testuser1', Decimal('100.50'), description='测试充值')
        if result['success']:
            print(f"✓ 用户 testuser1 充值成功: {result}")
        else:
            print(f"✗ 用户 testuser1 充值失败: {result}")
        
        # 管理员充值
        result = balance_manager.recharge_balance(
            'testuser2', Decimal('50.25'), 
            operator='admin', 
            description='管理员充值',
            recharge_type='admin'
        )
        if result['success']:
            print(f"✓ 管理员为 testuser2 充值成功: {result}")
        else:
            print(f"✗ 管理员为 testuser2 充值失败: {result}")
        
        # 测试无效充值
        invalid_amounts = [Decimal('0'), Decimal('-10'), Decimal('10000.001')]
        for amount in invalid_amounts:
            result = balance_manager.recharge_balance('testuser1', amount)
            if not result['success']:
                print(f"✓ 无效充值金额 {amount} 正确被拒绝: {result['message']}")
            else:
                print(f"✗ 无效充值金额 {amount} 处理异常")
        
        # 测试扣费功能
        print("\n5. 测试扣费功能...")
        
        # 正常扣费
        result = balance_manager.deduct_balance(
            'testuser1', Decimal('10.25'), 
            service_type='auth', 
            session_id='test_session_1',
            description='测试扣费'
        )
        if result['success']:
            print(f"✓ 用户 testuser1 扣费成功: {result}")
        else:
            print(f"✗ 用户 testuser1 扣费失败: {result}")
        
        # 测试余额不足的扣费
        result = balance_manager.deduct_balance('testuser1', Decimal('200.00'))
        if not result['success'] and result['error'] == 'insufficient_balance':
            print(f"✓ 余额不足的扣费正确被拒绝: {result['message']}")
        else:
            print(f"✗ 余额不足的扣费处理异常: {result}")
        
        # 测试余额充足性检查
        print("\n6. 测试余额充足性检查...")
        
        check_result = balance_manager.check_sufficient_balance('testuser1', Decimal('50.00'))
        print(f"用户 testuser1 余额检查 (需要50.00): {check_result}")
        
        check_result = balance_manager.check_sufficient_balance('testuser1', Decimal('200.00'))
        print(f"用户 testuser1 余额检查 (需要200.00): {check_result}")
        
        # 测试历史记录查询
        print("\n7. 测试历史记录查询...")
        
        # 充值历史
        recharge_history = balance_manager.get_recharge_history('testuser1')
        print(f"✓ 用户 testuser1 充值历史 ({len(recharge_history)} 条记录):")
        for record in recharge_history:
            print(f"  - {record['recharge_time']}: +{record['amount']} ({record['description']})")
        
        # 消费历史
        consumption_history = balance_manager.get_consumption_history('testuser1')
        print(f"✓ 用户 testuser1 消费历史 ({len(consumption_history)} 条记录):")
        for record in consumption_history:
            print(f"  - {record['consumption_time']}: -{record['amount']} ({record['description']})")
        
        # 测试余额汇总
        print("\n8. 测试余额汇总...")
        
        summary = balance_manager.get_balance_summary('testuser1')
        if summary['success']:
            print(f"✓ 用户 testuser1 余额汇总:")
            print(f"  - 当前余额: {summary['current_balance']}")
            print(f"  - 总充值: {summary['total_recharged']}")
            print(f"  - 总消费: {summary['total_consumed']}")
            print(f"  - 最后充值: {summary['last_recharge_time']}")
            print(f"  - 最后消费: {summary['last_consumption_time']}")
        else:
            print(f"✗ 获取余额汇总失败: {summary}")
        
        # 测试用户统计
        print("\n9. 测试用户统计...")
        
        stats = balance_manager.get_user_statistics()
        if stats:
            print("✓ 用户统计信息:")
            print(f"  - 总用户数: {stats['total_users']}")
            print(f"  - 有余额用户数: {stats['users_with_balance']}")
            print(f"  - 总余额: {stats['total_balance']}")
            print(f"  - 平均余额: {stats['average_balance']}")
            print(f"  - 今日充值: {stats['today_recharge']}")
            print(f"  - 今日消费: {stats['today_consumption']}")
        else:
            print("✗ 获取用户统计失败")
        
        print("\n" + "=" * 60)
        print("✅ 账户余额管理器测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试数据库
        try:
            if os.path.exists(test_db_path):
                import time
                time.sleep(0.1)  # 等待文件释放
                os.remove(test_db_path)
                print("\n已清理测试数据库")
        except PermissionError:
            print(f"\n注意: 无法删除测试数据库 {test_db_path}，请手动删除")


def main():
    """主函数"""
    print("开始账户余额管理器测试...")
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 运行测试
    success = test_balance_manager()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
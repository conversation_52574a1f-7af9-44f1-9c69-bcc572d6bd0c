# 网络验证工具

基于Python的网络认证系统，提供安全的用户认证和会话管理功能。

## 功能特性

- 基于TCP的客户端-服务器架构
- 安全的用户认证和密码加密
- 会话管理和令牌验证
- 多线程并发处理
- 用户管理和权限控制
- 实时日志记录和监控
- 现代化的GUI界面
- Web管理界面

## 系统要求

- Python 3.7+
- Windows/Linux/macOS
- SQLite3 (内置)
- Tkin<PERSON> (GUI界面)

## 快速开始

### 1. 安装

```bash
# 克隆项目
git clone <repository-url>
cd network-auth-tool

# 运行安装脚本
python install.py
```

### 2. 启动服务器

```bash
# 使用Python脚本
python network_auth_tool.py server

# 或使用批处理文件 (Windows)
start_server.bat
```

### 3. 启动客户端

```bash
# 使用Python脚本
python network_auth_tool.py client

# 或使用批处理文件 (Windows)
start_client.bat
```

### 4. Web管理界面

```bash
# 启动Web管理界面
python network_auth_tool.py webgui

# 或使用批处理文件 (Windows)
start_webgui.bat
```

## 配置

### 服务器配置 (config.ini)

```ini
[server]
host = 0.0.0.0
port = 8888
max_connections = 50

[database]
path = data/auth.db

[security]
session_timeout = 3600
max_failed_attempts = 5
lockout_duration = 1800

[logging]
level = INFO
file_path = logs/server.log
```

### 客户端配置 (client_config.ini)

```ini
[server]
host = localhost
port = 8888
timeout = 30

[client]
auto_reconnect = true
reconnect_interval = 5
```

## 使用说明

### 默认管理员账户

- 用户名: `admin`
- 密码: `admin123`

**重要**: 请在首次登录后立即修改默认密码！

### 服务器管理

1. **启动服务器**: 运行 `python network_auth_tool.py server`
2. **Web管理**: 访问Web管理界面进行用户管理
3. **日志监控**: 查看 `logs/` 目录下的日志文件

### 客户端使用

1. **连接服务器**: 启动客户端并输入服务器地址
2. **用户认证**: 输入用户名和密码进行认证
3. **会话管理**: 认证成功后自动管理会话

## 开发

### 项目结构

```
network-auth-tool/
├── server/                 # 服务器端代码
│   ├── server_application.py
│   ├── database_manager.py
│   ├── user_manager.py
│   ├── session_manager.py
│   ├── authentication_handler.py
│   ├── protocol_handler.py
│   ├── socket_listener.py
│   ├── thread_pool_manager.py
│   ├── security_utils.py
│   └── web_gui/           # Web管理界面
├── client/                # 客户端代码
│   ├── client_application.py
│   ├── network_client.py
│   ├── client_gui.py
│   └── auth_result_handler.py
├── common/                # 共用模块
│   ├── config_manager.py
│   ├── log_manager.py
│   ├── constants.py
│   ├── interfaces.py
│   └── exceptions.py
├── tests/                 # 测试代码
├── docs/                  # 文档
├── data/                  # 数据文件
├── logs/                  # 日志文件
└── tools/                 # 工具脚本
```

### 运行测试

```bash
# 运行完整测试套件
python network_auth_tool.py test

# 运行特定测试
python run_tests.py
```

## API文档

### 认证协议

客户端和服务器之间使用JSON格式进行通信：

#### 认证请求
```json
{
    "type": "auth_request",
    "username": "user123",
    "password": "password123",
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 认证响应
```json
{
    "type": "auth_response",
    "success": true,
    "message": "认证成功",
    "session_token": "abc123...",
    "expires_at": "2024-01-01T13:00:00Z",
    "user_info": {
        "username": "user123",
        "user_id": 1
    }
}
```

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查端口是否被占用
   - 确认配置文件格式正确
   - 查看日志文件获取详细错误信息

2. **客户端连接失败**
   - 确认服务器已启动
   - 检查网络连接和防火墙设置
   - 验证服务器地址和端口配置

3. **认证失败**
   - 确认用户名和密码正确
   - 检查账户是否被锁定
   - 查看服务器日志了解详细信息

### 日志文件

- 服务器日志: `logs/server.log`
- 客户端日志: `logs/client.log`
- 错误日志: `logs/error.log`

## 安全注意事项

1. **密码安全**
   - 使用强密码
   - 定期更换密码
   - 不要使用默认密码

2. **网络安全**
   - 在生产环境中使用HTTPS
   - 配置防火墙规则
   - 限制服务器访问权限

3. **数据保护**
   - 定期备份数据库
   - 保护配置文件安全
   - 监控系统日志

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: [<EMAIL>]

---

## 快速命令参考

```bash
# 系统管理
python network_auth_tool.py status    # 查看系统状态
python network_auth_tool.py test      # 运行测试

# 服务器管理
python network_auth_tool.py server    # 启动服务器
python network_auth_tool.py webgui    # 启动Web管理界面

# 客户端
python network_auth_tool.py client    # 启动客户端

# 测试和验证
python test_complete_auth_flow.py     # 完整认证流程测试
python verify_project.py             # 项目验证
```
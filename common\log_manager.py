"""
日志管理模块
实现多级别日志记录、文件轮转和日志格式化过滤功能
"""

import logging
import logging.handlers
import os
import threading
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from common.interfaces import ILogManager
from common.exceptions import ConfigurationError
from common.constants import DEFAULT_CONFIG, LOG_LEVELS


class LogManager(ILogManager):
    """日志管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化日志管理器
        
        Args:
            config: 日志配置
        """
        self.config = config or DEFAULT_CONFIG['logging']
        self.loggers = {}
        self._lock = threading.Lock()
        
        # 日志统计
        self.log_stats = {
            'total_logs': 0,
            'debug_logs': 0,
            'info_logs': 0,
            'warning_logs': 0,
            'error_logs': 0,
            'critical_logs': 0,
            'start_time': datetime.now().isoformat()
        }
        
        # 初始化主日志记录器
        self._setup_main_logger()
    
    def _setup_main_logger(self):
        """设置主日志记录器"""
        try:
            # 创建日志目录
            log_file = self.config.get('file_path', 'logs/server.log')
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            
            # 创建主日志记录器
            logger = logging.getLogger('NetworkAuthTool')
            logger.setLevel(getattr(logging, self.config.get('level', 'INFO')))
            
            # 清除现有处理器
            logger.handlers.clear()
            
            # 创建文件处理器（带轮转）
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=self.config.get('max_file_size', 10485760),
                backupCount=self.config.get('backup_count', 5),
                encoding='utf-8'
            )
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            
            # 创建格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
            
            # 添加自定义过滤器
            logger.addFilter(self._create_log_filter())
            
            self.loggers['main'] = logger
            
            # 记录启动日志
            logger.info("日志系统已初始化")
            
        except Exception as e:
            print(f"初始化日志系统失败: {e}")
            # 创建基本的控制台日志记录器
            self._setup_fallback_logger()
    
    def _setup_fallback_logger(self):
        """设置备用日志记录器"""
        logger = logging.getLogger('NetworkAuthTool')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        self.loggers['main'] = logger
    
    def _create_log_filter(self):
        """创建日志过滤器"""
        class LogFilter(logging.Filter):
            def __init__(self, log_manager):
                super().__init__()
                self.log_manager = log_manager
            
            def filter(self, record):
                # 更新统计
                self.log_manager._update_stats(record.levelname)
                return True
        
        return LogFilter(self)
    
    def _update_stats(self, level: str):
        """更新日志统计"""
        with self._lock:
            self.log_stats['total_logs'] += 1
            
            level_key = f"{level.lower()}_logs"
            if level_key in self.log_stats:
                self.log_stats[level_key] += 1
    
    def get_logger(self, name: str = 'main') -> logging.Logger:
        """
        获取日志记录器
        
        Args:
            name: 日志记录器名称
            
        Returns:
            logging.Logger: 日志记录器
        """
        if name in self.loggers:
            return self.loggers[name]
        
        # 创建新的日志记录器
        logger = logging.getLogger(f'NetworkAuthTool.{name}')
        logger.setLevel(getattr(logging, self.config.get('level', 'INFO')))
        
        # 如果没有处理器，添加默认处理器
        if not logger.handlers:
            # 继承主日志记录器的处理器
            main_logger = self.loggers.get('main')
            if main_logger:
                for handler in main_logger.handlers:
                    logger.addHandler(handler)
        
        self.loggers[name] = logger
        return logger
    
    def log_info(self, message: str, **kwargs) -> None:
        """
        记录信息日志
        
        Args:
            message: 日志消息
            **kwargs: 额外参数
        """
        logger = self.get_logger()
        extra_info = self._format_extra_info(kwargs)
        logger.info(f"{message}{extra_info}")
    
    def log_warning(self, message: str, **kwargs) -> None:
        """
        记录警告日志
        
        Args:
            message: 日志消息
            **kwargs: 额外参数
        """
        logger = self.get_logger()
        extra_info = self._format_extra_info(kwargs)
        logger.warning(f"{message}{extra_info}")
    
    def log_error(self, message: str, **kwargs) -> None:
        """
        记录错误日志
        
        Args:
            message: 日志消息
            **kwargs: 额外参数
        """
        logger = self.get_logger()
        extra_info = self._format_extra_info(kwargs)
        logger.error(f"{message}{extra_info}")
    
    def log_debug(self, message: str, **kwargs) -> None:
        """
        记录调试日志
        
        Args:
            message: 日志消息
            **kwargs: 额外参数
        """
        logger = self.get_logger()
        extra_info = self._format_extra_info(kwargs)
        logger.debug(f"{message}{extra_info}")
    
    def log_critical(self, message: str, **kwargs) -> None:
        """
        记录严重错误日志
        
        Args:
            message: 日志消息
            **kwargs: 额外参数
        """
        logger = self.get_logger()
        extra_info = self._format_extra_info(kwargs)
        logger.critical(f"{message}{extra_info}")
    
    def _format_extra_info(self, kwargs: Dict[str, Any]) -> str:
        """
        格式化额外信息
        
        Args:
            kwargs: 额外参数
            
        Returns:
            str: 格式化后的信息
        """
        if not kwargs:
            return ""
        
        # 过滤和格式化参数
        formatted_parts = []
        
        for key, value in kwargs.items():
            if key in ['client_ip', 'username', 'user_id', 'session_id']:
                formatted_parts.append(f"{key}={value}")
            elif key == 'exception' and isinstance(value, Exception):
                formatted_parts.append(f"exception={type(value).__name__}: {str(value)}")
            elif key == 'data' and isinstance(value, dict):
                try:
                    formatted_parts.append(f"data={json.dumps(value, ensure_ascii=False)}")
                except:
                    formatted_parts.append(f"data={str(value)}")
        
        return f" [{', '.join(formatted_parts)}]" if formatted_parts else ""
    
    def log_auth_event(self, event_type: str, username: str, client_ip: str = None, 
                      success: bool = True, details: str = None):
        """
        记录认证事件
        
        Args:
            event_type: 事件类型
            username: 用户名
            client_ip: 客户端IP
            success: 是否成功
            details: 详细信息
        """
        level = 'info' if success else 'warning'
        message = f"[{event_type}] 用户: {username}"
        
        if details:
            message += f" - {details}"
        
        kwargs = {}
        if client_ip:
            kwargs['client_ip'] = client_ip
        kwargs['username'] = username
        kwargs['event_type'] = event_type
        
        if level == 'info':
            self.log_info(message, **kwargs)
        else:
            self.log_warning(message, **kwargs)
    
    def log_system_event(self, event_type: str, message: str, level: str = 'info', **kwargs):
        """
        记录系统事件
        
        Args:
            event_type: 事件类型
            message: 消息
            level: 日志级别
            **kwargs: 额外参数
        """
        formatted_message = f"[{event_type}] {message}"
        kwargs['event_type'] = event_type
        
        if level == 'debug':
            self.log_debug(formatted_message, **kwargs)
        elif level == 'info':
            self.log_info(formatted_message, **kwargs)
        elif level == 'warning':
            self.log_warning(formatted_message, **kwargs)
        elif level == 'error':
            self.log_error(formatted_message, **kwargs)
        elif level == 'critical':
            self.log_critical(formatted_message, **kwargs)
    
    def log_network_event(self, event_type: str, client_ip: str, message: str, **kwargs):
        """
        记录网络事件
        
        Args:
            event_type: 事件类型
            client_ip: 客户端IP
            message: 消息
            **kwargs: 额外参数
        """
        kwargs['client_ip'] = client_ip
        self.log_system_event(event_type, message, **kwargs)
    
    def set_log_level(self, level: str):
        """
        设置日志级别
        
        Args:
            level: 日志级别
        """
        try:
            log_level = getattr(logging, level.upper())
            
            for logger in self.loggers.values():
                logger.setLevel(log_level)
            
            self.config['level'] = level.upper()
            self.log_info(f"日志级别已设置为: {level.upper()}")
            
        except AttributeError:
            self.log_error(f"无效的日志级别: {level}")
    
    def rotate_logs(self):
        """手动轮转日志文件"""
        try:
            for logger in self.loggers.values():
                for handler in logger.handlers:
                    if isinstance(handler, logging.handlers.RotatingFileHandler):
                        handler.doRollover()
            
            self.log_info("日志文件已轮转")
            
        except Exception as e:
            self.log_error(f"轮转日志文件失败: {str(e)}")
    
    def get_log_files(self) -> List[str]:
        """
        获取日志文件列表
        
        Returns:
            List[str]: 日志文件路径列表
        """
        log_files = []
        
        try:
            log_file = self.config.get('file_path', 'logs/server.log')
            log_dir = os.path.dirname(log_file)
            log_name = os.path.basename(log_file)
            
            if os.path.exists(log_dir):
                for file in os.listdir(log_dir):
                    if file.startswith(log_name.split('.')[0]):
                        log_files.append(os.path.join(log_dir, file))
            
            log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
        except Exception as e:
            self.log_error(f"获取日志文件列表失败: {str(e)}")
        
        return log_files
    
    def read_log_file(self, file_path: str, lines: int = 100) -> List[str]:
        """
        读取日志文件内容
        
        Args:
            file_path: 日志文件路径
            lines: 读取行数
            
        Returns:
            List[str]: 日志行列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if lines > 0 else all_lines
                
        except Exception as e:
            self.log_error(f"读取日志文件失败: {str(e)}")
            return []
    
    def search_logs(self, keyword: str, file_path: str = None, 
                   start_time: datetime = None, end_time: datetime = None) -> List[Dict[str, Any]]:
        """
        搜索日志
        
        Args:
            keyword: 搜索关键词
            file_path: 日志文件路径，为None时搜索主日志文件
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            List[Dict[str, Any]]: 匹配的日志条目
        """
        results = []
        
        try:
            if not file_path:
                file_path = self.config.get('file_path', 'logs/server.log')
            
            if not os.path.exists(file_path):
                return results
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if keyword.lower() in line.lower():
                        # 解析日志时间
                        log_time = self._parse_log_time(line)
                        
                        # 时间过滤
                        if start_time and log_time and log_time < start_time:
                            continue
                        if end_time and log_time and log_time > end_time:
                            continue
                        
                        results.append({
                            'line_number': line_num,
                            'content': line.strip(),
                            'timestamp': log_time.isoformat() if log_time else None,
                            'file': file_path
                        })
            
        except Exception as e:
            self.log_error(f"搜索日志失败: {str(e)}")
        
        return results
    
    def _parse_log_time(self, log_line: str) -> Optional[datetime]:
        """
        解析日志时间
        
        Args:
            log_line: 日志行
            
        Returns:
            Optional[datetime]: 解析的时间
        """
        try:
            # 假设日志格式为: 2024-01-01 12:00:00 - ...
            time_part = log_line.split(' - ')[0]
            return datetime.strptime(time_part, '%Y-%m-%d %H:%M:%S')
        except:
            return None
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """
        获取日志统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            stats = self.log_stats.copy()
        
        # 计算运行时间
        start_time = datetime.fromisoformat(stats['start_time'])
        uptime_seconds = int((datetime.now() - start_time).total_seconds())
        stats['uptime_seconds'] = uptime_seconds
        
        # 计算日志文件信息
        try:
            log_file = self.config.get('file_path', 'logs/server.log')
            if os.path.exists(log_file):
                stats['log_file_size'] = os.path.getsize(log_file)
                stats['log_file_modified'] = datetime.fromtimestamp(
                    os.path.getmtime(log_file)
                ).isoformat()
            else:
                stats['log_file_size'] = 0
                stats['log_file_modified'] = None
        except:
            stats['log_file_size'] = 0
            stats['log_file_modified'] = None
        
        # 添加配置信息
        stats['current_level'] = self.config.get('level', 'INFO')
        stats['max_file_size'] = self.config.get('max_file_size', 10485760)
        stats['backup_count'] = self.config.get('backup_count', 5)
        
        return stats
    
    def cleanup_old_logs(self, days: int = 30):
        """
        清理旧日志文件
        
        Args:
            days: 保留天数
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            log_files = self.get_log_files()
            
            cleaned_count = 0
            for log_file in log_files:
                try:
                    file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                    if file_time < cutoff_time:
                        os.remove(log_file)
                        cleaned_count += 1
                except:
                    continue
            
            self.log_info(f"已清理 {cleaned_count} 个旧日志文件")
            
        except Exception as e:
            self.log_error(f"清理旧日志文件失败: {str(e)}")
    
    def export_logs(self, output_file: str, start_time: datetime = None, 
                   end_time: datetime = None, level_filter: str = None) -> bool:
        """
        导出日志
        
        Args:
            output_file: 输出文件路径
            start_time: 开始时间
            end_time: 结束时间
            level_filter: 级别过滤
            
        Returns:
            bool: 导出成功返回True
        """
        try:
            log_file = self.config.get('file_path', 'logs/server.log')
            if not os.path.exists(log_file):
                return False
            
            with open(log_file, 'r', encoding='utf-8') as input_f, \
                 open(output_file, 'w', encoding='utf-8') as output_f:
                
                for line in input_f:
                    # 时间过滤
                    log_time = self._parse_log_time(line)
                    if start_time and log_time and log_time < start_time:
                        continue
                    if end_time and log_time and log_time > end_time:
                        continue
                    
                    # 级别过滤
                    if level_filter and level_filter.upper() not in line:
                        continue
                    
                    output_f.write(line)
            
            self.log_info(f"日志已导出到: {output_file}")
            return True
            
        except Exception as e:
            self.log_error(f"导出日志失败: {str(e)}")
            return False
    
    def shutdown(self):
        """关闭日志系统"""
        try:
            self.log_info("日志系统正在关闭")
            
            # 关闭所有处理器
            for logger in self.loggers.values():
                for handler in logger.handlers[:]:
                    handler.close()
                    logger.removeHandler(handler)
            
            self.loggers.clear()
            
        except Exception as e:
            print(f"关闭日志系统时发生错误: {e}")
#!/usr/bin/env python3
"""
网络通信模块单元测试
"""

import sys
import os
import unittest
import tempfile
import shutil
import threading
import time
import socket
import json
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.socket_listener import SocketListener
from server.thread_pool_manager import ThreadPoolManager
from server.protocol_handler import ProtocolHandler
from server.database_manager import DatabaseManager
from server.user_manager import UserManager
from server.session_manager import SessionManager
from client.network_client import NetworkClient


class TestSocketListener(unittest.TestCase):
    """Socket监听器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.host = '127.0.0.1'
        self.port = 0  # 让系统自动分配端口
        self.socket_listener = SocketListener(self.host, self.port, max_connections=5)
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'socket_listener'):
            self.socket_listener.stop_listening()
    
    def test_start_listening(self):
        """测试开始监听"""
        result = self.socket_listener.start_listening()
        
        self.assertTrue(result['success'])
        self.assertIn('port', result)
        self.assertTrue(self.socket_listener.is_listening())
        
        # 获取实际分配的端口
        self.actual_port = result['port']
    
    def test_stop_listening(self):
        """测试停止监听"""
        # 先开始监听
        self.socket_listener.start_listening()
        self.assertTrue(self.socket_listener.is_listening())
        
        # 停止监听
        result = self.socket_listener.stop_listening()
        self.assertTrue(result['success'])
        self.assertFalse(self.socket_listener.is_listening())
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        stats = self.socket_listener.get_statistics()
        
        self.assertIn('is_listening', stats)
        self.assertIn('total_connections', stats)
        self.assertIn('active_connections', stats)
        self.assertIn('start_time', stats)
    
    def test_client_handler_callback(self):
        """测试客户端处理回调"""
        handler_called = threading.Event()
        received_data = {}
        
        def mock_handler(conn_id, client_socket, address):
            received_data['conn_id'] = conn_id
            received_data['address'] = address
            handler_called.set()
        
        self.socket_listener.set_client_handler(mock_handler)
        
        # 开始监听
        start_result = self.socket_listener.start_listening()
        self.assertTrue(start_result['success'])
        
        port = start_result['port']
        
        # 创建客户端连接
        def create_client_connection():
            try:
                client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                client_socket.connect((self.host, port))
                time.sleep(0.1)  # 给处理器一些时间
                client_socket.close()
            except Exception as e:
                print(f"客户端连接错误: {e}")
        
        # 在单独线程中创建连接
        client_thread = threading.Thread(target=create_client_connection)
        client_thread.start()
        
        # 等待处理器被调用
        handler_called.wait(timeout=5)
        client_thread.join(timeout=2)
        
        # 验证处理器被调用
        self.assertTrue(handler_called.is_set())
        self.assertIn('conn_id', received_data)
        self.assertIn('address', received_data)


class TestThreadPoolManager(unittest.TestCase):
    """线程池管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.thread_pool = ThreadPoolManager(max_threads=3)
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=True, timeout=5)
    
    def test_submit_task(self):
        """测试提交任务"""
        result_container = []
        
        def test_task(value):
            result_container.append(value * 2)
            return value * 2
        
        # 提交任务
        future = self.thread_pool.submit_task(test_task, 5)
        
        # 等待结果
        result = future.result(timeout=5)
        
        self.assertEqual(result, 10)
        self.assertEqual(result_container[0], 10)
    
    def test_multiple_tasks(self):
        """测试多个任务"""
        results = []
        
        def test_task(value):
            time.sleep(0.1)  # 模拟工作
            return value * 2
        
        # 提交多个任务
        futures = []
        for i in range(5):
            future = self.thread_pool.submit_task(test_task, i)
            futures.append(future)
        
        # 收集结果
        for i, future in enumerate(futures):
            result = future.result(timeout=5)
            self.assertEqual(result, i * 2)
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        stats = self.thread_pool.get_statistics()
        
        self.assertIn('max_threads', stats)
        self.assertIn('active_threads', stats)
        self.assertIn('pending_tasks', stats)
        self.assertIn('completed_tasks', stats)
        
        self.assertEqual(stats['max_threads'], 3)
    
    def test_shutdown(self):
        """测试关闭线程池"""
        # 提交一个任务
        def test_task():
            time.sleep(0.1)
            return "completed"
        
        future = self.thread_pool.submit_task(test_task)
        
        # 关闭线程池
        result = self.thread_pool.shutdown(wait=True, timeout=5)
        self.assertTrue(result['success'])
        
        # 验证任务完成
        self.assertEqual(future.result(), "completed")


class TestProtocolHandler(unittest.TestCase):
    """协议处理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.test_dir, 'test_auth.db')
        self.db_manager = DatabaseManager(self.db_path)
        
        self.user_manager = UserManager(self.db_manager)
        self.session_manager = SessionManager(self.db_manager)
        self.protocol_handler = ProtocolHandler(self.user_manager, self.session_manager)
        
        # 创建测试用户
        self.user_manager.create_user('testuser', 'testpass123')
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'db_manager'):
            del self.db_manager
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_create_welcome_message(self):
        """测试创建欢迎消息"""
        message = self.protocol_handler.create_welcome_message()
        
        self.assertIn('type', message)
        self.assertEqual(message['type'], 'welcome')
        self.assertIn('message', message)
        self.assertIn('server_info', message)
    
    def test_process_auth_request(self):
        """测试处理认证请求"""
        auth_message = {
            'type': 'auth_request',
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        response = self.protocol_handler.process_message('conn_1', auth_message, '127.0.0.1')
        
        self.assertIn('type', response)
        self.assertEqual(response['type'], 'auth_response')
        self.assertTrue(response['success'])
        self.assertIn('session_token', response)
        self.assertIn('user_info', response)
    
    def test_process_invalid_auth_request(self):
        """测试处理无效认证请求"""
        auth_message = {
            'type': 'auth_request',
            'username': 'testuser',
            'password': 'wrongpass'
        }
        
        response = self.protocol_handler.process_message('conn_1', auth_message, '127.0.0.1')
        
        self.assertEqual(response['type'], 'auth_response')
        self.assertFalse(response['success'])
        self.assertIn('message', response)
    
    def test_process_session_validate_request(self):
        """测试处理会话验证请求"""
        # 先进行认证获取会话令牌
        auth_message = {
            'type': 'auth_request',
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        auth_response = self.protocol_handler.process_message('conn_1', auth_message, '127.0.0.1')
        token = auth_response['session_token']
        
        # 验证会话
        validate_message = {
            'type': 'session_validate',
            'session_token': token
        }
        
        response = self.protocol_handler.process_message('conn_1', validate_message, '127.0.0.1')
        
        self.assertEqual(response['type'], 'session_response')
        self.assertTrue(response['success'])
        self.assertIn('user_info', response)
    
    def test_process_logout_request(self):
        """测试处理注销请求"""
        # 先进行认证
        auth_message = {
            'type': 'auth_request',
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        auth_response = self.protocol_handler.process_message('conn_1', auth_message, '127.0.0.1')
        token = auth_response['session_token']
        
        # 注销
        logout_message = {
            'type': 'logout',
            'session_token': token
        }
        
        response = self.protocol_handler.process_message('conn_1', logout_message, '127.0.0.1')
        
        self.assertEqual(response['type'], 'logout_response')
        self.assertTrue(response['success'])
    
    def test_process_invalid_message(self):
        """测试处理无效消息"""
        invalid_message = {
            'type': 'unknown_type',
            'data': 'some data'
        }
        
        response = self.protocol_handler.process_message('conn_1', invalid_message, '127.0.0.1')
        
        self.assertEqual(response['type'], 'error')
        self.assertFalse(response['success'])
        self.assertIn('message', response)
    
    def test_handle_client_disconnect(self):
        """测试处理客户端断开连接"""
        # 先进行认证
        auth_message = {
            'type': 'auth_request',
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        auth_response = self.protocol_handler.process_message('conn_1', auth_message, '127.0.0.1')
        token = auth_response['session_token']
        
        # 处理断开连接
        self.protocol_handler.handle_client_disconnect('conn_1', token)
        
        # 验证会话已被清理
        validate_message = {
            'type': 'session_validate',
            'session_token': token
        }
        
        response = self.protocol_handler.process_message('conn_2', validate_message, '127.0.0.1')
        self.assertFalse(response['success'])


class TestNetworkClient(unittest.TestCase):
    """网络客户端测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = NetworkClient()
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'client'):
            self.client.disconnect()
    
    def test_initial_state(self):
        """测试初始状态"""
        self.assertFalse(self.client.is_connected())
        
        connection_info = self.client.get_connection_info()
        self.assertIn('connected', connection_info)
        self.assertFalse(connection_info['connected'])
    
    def test_connect_to_invalid_server(self):
        """测试连接到无效服务器"""
        result = self.client.connect('127.0.0.1', 99999)  # 不存在的端口
        
        self.assertFalse(result['success'])
        self.assertIn('message', result)
        self.assertFalse(self.client.is_connected())
    
    def test_send_message_without_connection(self):
        """测试在未连接时发送消息"""
        message = {'type': 'test', 'data': 'test data'}
        result = self.client.send_message(message)
        
        self.assertFalse(result['success'])
        self.assertIn('未连接', result['message'])
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        stats = self.client.get_statistics()
        
        self.assertIn('total_messages_sent', stats)
        self.assertIn('total_messages_received', stats)
        self.assertIn('connection_attempts', stats)
        self.assertIn('successful_connections', stats)
        self.assertIn('failed_connections', stats)
    
    def test_set_timeouts(self):
        """测试设置超时时间"""
        self.client.set_timeouts(connection_timeout=15, receive_timeout=25)
        
        # 验证超时设置（通过内部属性检查）
        self.assertEqual(self.client.connection_timeout, 15)
        self.assertEqual(self.client.receive_timeout, 25)
    
    def test_set_retry_config(self):
        """测试设置重试配置"""
        self.client.set_retry_config(max_attempts=5)
        
        # 验证重试配置
        self.assertEqual(self.client.max_retry_attempts, 5)


class TestNetworkIntegration(unittest.TestCase):
    """网络通信集成测试"""
    
    def setUp(self):
        """测试前准备"""
        # 设置数据库
        self.test_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.test_dir, 'test_auth.db')
        self.db_manager = DatabaseManager(self.db_path)
        
        # 设置服务器组件
        self.user_manager = UserManager(self.db_manager)
        self.session_manager = SessionManager(self.db_manager)
        self.protocol_handler = ProtocolHandler(self.user_manager, self.session_manager)
        
        # 创建测试用户
        self.user_manager.create_user('testuser', 'testpass123')
        
        # 设置Socket监听器
        self.socket_listener = SocketListener('127.0.0.1', 0, max_connections=5)
        self.socket_listener.set_client_handler(self._handle_client)
        
        # 启动服务器
        start_result = self.socket_listener.start_listening()
        self.assertTrue(start_result['success'])
        self.server_port = start_result['port']
        
        # 设置客户端
        self.client = NetworkClient()
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'client'):
            self.client.disconnect()
        if hasattr(self, 'socket_listener'):
            self.socket_listener.stop_listening()
        if hasattr(self, 'db_manager'):
            del self.db_manager
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def _handle_client(self, conn_id, client_socket, address):
        """客户端处理器"""
        try:
            while True:
                # 接收消息
                message = self.socket_listener.receive_message(client_socket, timeout=5.0)
                if not message:
                    break
                
                # 处理消息
                response = self.protocol_handler.process_message(conn_id, message, address[0])
                
                # 发送响应
                self.socket_listener.send_message(conn_id, response)
                
        except Exception as e:
            print(f"处理客户端时发生错误: {e}")
    
    def test_client_server_communication(self):
        """测试客户端-服务器通信"""
        # 连接到服务器
        connect_result = self.client.connect('127.0.0.1', self.server_port)
        self.assertTrue(connect_result['success'])
        self.assertTrue(self.client.is_connected())
        
        # 发送认证请求
        auth_message = {
            'type': 'auth_request',
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        send_result = self.client.send_message(auth_message)
        self.assertTrue(send_result['success'])
        
        # 接收响应
        response = self.client.receive_message(timeout=5.0)
        self.assertIsNotNone(response)
        self.assertEqual(response['type'], 'auth_response')
        self.assertTrue(response['success'])
        self.assertIn('session_token', response)


if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestSocketListener,
        TestThreadPoolManager,
        TestProtocolHandler,
        TestNetworkClient,
        # TestNetworkIntegration  # 集成测试可能需要更多时间，可以单独运行
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print(f"\n网络通信模块测试完成:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    # 返回退出代码
    sys.exit(0 if result.wasSuccessful() else 1)
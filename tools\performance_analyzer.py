#!/usr/bin/env python3
"""
性能分析和优化工具
分析网络验证工具的性能瓶颈并提供优化建议
"""

import sys
import os
import time
import threading
import psutil
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.database_manager import DatabaseManager
from server.socket_listener import SocketListener
from server.authentication_handler import AuthenticationHandler
from client.network_client import NetworkClient


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        """初始化性能分析器"""
        self.start_time = time.time()
        self.metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'network_stats': [],
            'database_stats': [],
            'auth_stats': [],
            'connection_stats': []
        }
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self, interval: float = 1.0):
        """
        开始性能监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.monitoring:
            print("性能监控已在运行中")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        print(f"开始性能监控，间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        print("性能监控已停止")
    
    def _monitor_loop(self, interval: float):
        """监控循环"""
        while self.monitoring:
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 收集应用指标
                self._collect_application_metrics()
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"监控过程中发生错误: {e}")
                time.sleep(interval)
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 网络统计
            network = psutil.net_io_counters()
            
            # 磁盘IO
            disk_io = psutil.disk_io_counters()
            
            timestamp = time.time()
            
            self.metrics['cpu_usage'].append({
                'timestamp': timestamp,
                'cpu_percent': cpu_percent,
                'cpu_count': psutil.cpu_count()
            })
            
            self.metrics['memory_usage'].append({
                'timestamp': timestamp,
                'total': memory.total,
                'available': memory.available,
                'percent': memory.percent,
                'used': memory.used
            })
            
            self.metrics['network_stats'].append({
                'timestamp': timestamp,
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            })
            
        except Exception as e:
            print(f"收集系统指标时发生错误: {e}")
    
    def _collect_application_metrics(self):
        """收集应用程序指标"""
        try:
            # 这里可以添加应用程序特定的指标收集
            # 例如：数据库连接数、活跃会话数等
            pass
        except Exception as e:
            print(f"收集应用指标时发生错误: {e}")
    
    def analyze_database_performance(self, db_path: str = "data/auth.db") -> Dict[str, Any]:
        """
        分析数据库性能
        
        Args:
            db_path: 数据库文件路径
            
        Returns:
            Dict[str, Any]: 数据库性能分析结果
        """
        if not os.path.exists(db_path):
            return {"error": "数据库文件不存在"}
        
        try:
            analysis = {
                'file_size': os.path.getsize(db_path),
                'table_stats': {},
                'index_stats': {},
                'query_performance': {}
            }
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 获取表统计信息
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                for (table_name,) in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = cursor.fetchone()[0]
                    
                    analysis['table_stats'][table_name] = {
                        'row_count': row_count
                    }
                
                # 获取索引信息
                cursor.execute("SELECT name, tbl_name FROM sqlite_master WHERE type='index'")
                indexes = cursor.fetchall()
                
                for index_name, table_name in indexes:
                    analysis['index_stats'][index_name] = {
                        'table': table_name
                    }
                
                # 测试查询性能
                test_queries = [
                    ("SELECT COUNT(*) FROM users", "用户计数查询"),
                    ("SELECT * FROM users LIMIT 10", "用户列表查询"),
                    ("SELECT COUNT(*) FROM sessions", "会话计数查询")
                ]
                
                for query, description in test_queries:
                    try:
                        start_time = time.time()
                        cursor.execute(query)
                        cursor.fetchall()
                        end_time = time.time()
                        
                        analysis['query_performance'][description] = {
                            'query': query,
                            'execution_time': end_time - start_time
                        }
                    except Exception as e:
                        analysis['query_performance'][description] = {
                            'query': query,
                            'error': str(e)
                        }
            
            return analysis
            
        except Exception as e:
            return {"error": f"分析数据库性能时发生错误: {e}"}
    
    def benchmark_authentication(self, iterations: int = 100) -> Dict[str, Any]:
        """
        认证性能基准测试
        
        Args:
            iterations: 测试迭代次数
            
        Returns:
            Dict[str, Any]: 基准测试结果
        """
        try:
            # 创建临时数据库进行测试
            import tempfile
            temp_dir = tempfile.mkdtemp()
            temp_db = os.path.join(temp_dir, 'benchmark.db')
            
            db_manager = DatabaseManager(temp_db)
            auth_handler = AuthenticationHandler(db_manager)
            
            # 创建测试用户
            test_users = []
            for i in range(min(10, iterations)):
                username = f"testuser{i}"
                password = f"testpass{i}"
                result = auth_handler.user_manager.create_user(username, password)
                if result['success']:
                    test_users.append((username, password))
            
            # 测试认证性能
            auth_times = []
            success_count = 0
            
            for i in range(iterations):
                if not test_users:
                    break
                
                username, password = test_users[i % len(test_users)]
                
                start_time = time.time()
                result = auth_handler.authenticate(username, password, '127.0.0.1')
                end_time = time.time()
                
                auth_times.append(end_time - start_time)
                if result['success']:
                    success_count += 1
            
            # 计算统计信息
            if auth_times:
                avg_time = sum(auth_times) / len(auth_times)
                min_time = min(auth_times)
                max_time = max(auth_times)
            else:
                avg_time = min_time = max_time = 0
            
            # 清理临时文件
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            return {
                'iterations': iterations,
                'success_count': success_count,
                'success_rate': success_count / iterations if iterations > 0 else 0,
                'avg_auth_time': avg_time,
                'min_auth_time': min_time,
                'max_auth_time': max_time,
                'total_time': sum(auth_times)
            }
            
        except Exception as e:
            return {"error": f"认证基准测试失败: {e}"}
    
    def benchmark_network_performance(self, host: str = '127.0.0.1', port: int = 8888, 
                                    connections: int = 10, requests_per_connection: int = 10) -> Dict[str, Any]:
        """
        网络性能基准测试
        
        Args:
            host: 服务器地址
            port: 服务器端口
            connections: 并发连接数
            requests_per_connection: 每个连接的请求数
            
        Returns:
            Dict[str, Any]: 网络性能测试结果
        """
        try:
            results = {
                'total_connections': connections,
                'requests_per_connection': requests_per_connection,
                'successful_connections': 0,
                'successful_requests': 0,
                'connection_times': [],
                'request_times': [],
                'errors': []
            }
            
            def test_connection():
                """测试单个连接"""
                try:
                    client = NetworkClient()
                    
                    # 测试连接时间
                    start_time = time.time()
                    connect_result = client.connect(host, port)
                    connect_time = time.time() - start_time
                    
                    if connect_result['success']:
                        results['successful_connections'] += 1
                        results['connection_times'].append(connect_time)
                        
                        # 测试请求性能
                        for _ in range(requests_per_connection):
                            try:
                                start_time = time.time()
                                
                                # 发送测试消息
                                test_message = {
                                    'type': 'ping',
                                    'timestamp': time.time()
                                }
                                
                                send_result = client.send_message(test_message)
                                if send_result['success']:
                                    # 接收响应
                                    response = client.receive_message()
                                    request_time = time.time() - start_time
                                    
                                    if response:
                                        results['successful_requests'] += 1
                                        results['request_times'].append(request_time)
                                
                            except Exception as e:
                                results['errors'].append(f"请求错误: {e}")
                    
                    client.disconnect()
                    
                except Exception as e:
                    results['errors'].append(f"连接错误: {e}")
            
            # 创建并启动测试线程
            threads = []
            for _ in range(connections):
                thread = threading.Thread(target=test_connection)
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=30)  # 30秒超时
            
            # 计算统计信息
            if results['connection_times']:
                results['avg_connection_time'] = sum(results['connection_times']) / len(results['connection_times'])
                results['min_connection_time'] = min(results['connection_times'])
                results['max_connection_time'] = max(results['connection_times'])
            
            if results['request_times']:
                results['avg_request_time'] = sum(results['request_times']) / len(results['request_times'])
                results['min_request_time'] = min(results['request_times'])
                results['max_request_time'] = max(results['request_times'])
            
            results['connection_success_rate'] = results['successful_connections'] / connections
            total_expected_requests = connections * requests_per_connection
            results['request_success_rate'] = results['successful_requests'] / total_expected_requests if total_expected_requests > 0 else 0
            
            return results
            
        except Exception as e:
            return {"error": f"网络性能测试失败: {e}"}
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """
        生成性能报告
        
        Returns:
            Dict[str, Any]: 性能报告
        """
        report = {
            'timestamp': datetime.now().isoformat(),
            'monitoring_duration': time.time() - self.start_time,
            'system_performance': {},
            'database_performance': {},
            'auth_performance': {},
            'network_performance': {},
            'recommendations': []
        }
        
        # 分析系统性能
        if self.metrics['cpu_usage']:
            cpu_data = self.metrics['cpu_usage']
            avg_cpu = sum(item['cpu_percent'] for item in cpu_data) / len(cpu_data)
            max_cpu = max(item['cpu_percent'] for item in cpu_data)
            
            report['system_performance']['cpu'] = {
                'average': avg_cpu,
                'maximum': max_cpu,
                'samples': len(cpu_data)
            }
            
            if avg_cpu > 80:
                report['recommendations'].append("CPU使用率过高，建议优化算法或增加硬件资源")
            elif avg_cpu > 60:
                report['recommendations'].append("CPU使用率较高，建议监控并考虑优化")
        
        if self.metrics['memory_usage']:
            memory_data = self.metrics['memory_usage']
            avg_memory = sum(item['percent'] for item in memory_data) / len(memory_data)
            max_memory = max(item['percent'] for item in memory_data)
            
            report['system_performance']['memory'] = {
                'average_percent': avg_memory,
                'maximum_percent': max_memory,
                'samples': len(memory_data)
            }
            
            if avg_memory > 85:
                report['recommendations'].append("内存使用率过高，建议优化内存使用或增加内存")
            elif avg_memory > 70:
                report['recommendations'].append("内存使用率较高，建议监控内存使用情况")
        
        # 数据库性能分析
        db_analysis = self.analyze_database_performance()
        report['database_performance'] = db_analysis
        
        if 'query_performance' in db_analysis:
            slow_queries = []
            for desc, info in db_analysis['query_performance'].items():
                if 'execution_time' in info and info['execution_time'] > 0.1:  # 100ms
                    slow_queries.append(desc)
            
            if slow_queries:
                report['recommendations'].append(f"发现慢查询: {', '.join(slow_queries)}，建议优化")
        
        # 认证性能测试
        auth_benchmark = self.benchmark_authentication(50)
        report['auth_performance'] = auth_benchmark
        
        if 'avg_auth_time' in auth_benchmark and auth_benchmark['avg_auth_time'] > 0.5:
            report['recommendations'].append("认证响应时间较慢，建议优化认证算法")
        
        # 添加通用建议
        if not report['recommendations']:
            report['recommendations'].append("系统性能良好，建议继续监控")
        
        return report
    
    def save_report(self, report: Dict[str, Any], filename: str = None):
        """
        保存性能报告到文件
        
        Args:
            report: 性能报告
            filename: 文件名，默认使用时间戳
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.json"
        
        try:
            # 确保reports目录存在
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)
            
            filepath = reports_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"性能报告已保存到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            print(f"保存性能报告失败: {e}")
            return None


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='网络验证工具性能分析器')
    parser.add_argument('--monitor', '-m', action='store_true', help='启动性能监控')
    parser.add_argument('--duration', '-d', type=int, default=60, help='监控持续时间（秒）')
    parser.add_argument('--interval', '-i', type=float, default=1.0, help='监控间隔（秒）')
    parser.add_argument('--benchmark', '-b', action='store_true', help='运行基准测试')
    parser.add_argument('--report', '-r', action='store_true', help='生成性能报告')
    parser.add_argument('--db-analysis', action='store_true', help='仅分析数据库性能')
    
    args = parser.parse_args()
    
    analyzer = PerformanceAnalyzer()
    
    try:
        if args.db_analysis:
            print("分析数据库性能...")
            db_analysis = analyzer.analyze_database_performance()
            print(json.dumps(db_analysis, indent=2, ensure_ascii=False))
            
        elif args.benchmark:
            print("运行基准测试...")
            
            # 认证基准测试
            print("认证性能测试...")
            auth_result = analyzer.benchmark_authentication(100)
            print(f"认证测试结果: {json.dumps(auth_result, indent=2, ensure_ascii=False)}")
            
        elif args.monitor:
            print(f"开始性能监控，持续时间: {args.duration}秒")
            analyzer.start_monitoring(args.interval)
            
            try:
                time.sleep(args.duration)
            except KeyboardInterrupt:
                print("\n监控被用户中断")
            
            analyzer.stop_monitoring()
            
            if args.report:
                print("生成性能报告...")
                report = analyzer.generate_performance_report()
                analyzer.save_report(report)
                
        elif args.report:
            print("生成性能报告...")
            report = analyzer.generate_performance_report()
            analyzer.save_report(report)
            print(json.dumps(report, indent=2, ensure_ascii=False))
            
        else:
            print("请指定操作参数，使用 --help 查看帮助")
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
# 网络验证工具依赖包

# 核心依赖（可选但推荐）
psutil>=5.8.0                    # 系统监控和性能统计
pywebview>=4.0.0                 # Web GUI界面支持

# Windows服务集成（Windows平台可选）
# pywin32>=306                   # Windows服务支持

# 加密增强（可选）
# cryptography>=3.4.8           # 高级加密功能

# 注意：以下包已包含在Python标准库中，无需额外安装
# - tkinter (GUI界面)
# - sqlite3 (数据库)
# - socket (网络通信)
# - threading (多线程)
# - hashlib (密码哈希)
# - logging (日志记录)
# - json (JSON处理)
# - configparser (配置文件)
# - datetime (日期时间)
# - pathlib (路径处理)
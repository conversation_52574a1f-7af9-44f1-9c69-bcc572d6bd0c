#!/usr/bin/env python3
"""
网络验证工具集成测试
测试完整的认证流程和系统集成
"""

import sys
import os
import unittest
import tempfile
import shutil
import threading
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.server_application import ServerApplication
from client.network_client import NetworkClient
from client.auth_result_handler import AuthResultHandler
from server.database_manager import DatabaseManager
from server.user_manager import UserManager


class TestCompleteAuthFlow(unittest.TestCase):
    """完整认证流程测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        
        # 创建临时配置文件
        self.config_file = os.path.join(self.test_dir, 'test_config.ini')
        self._create_test_config()
        
        # 启动服务器应用
        self.server_app = ServerApplication(self.config_file)
        start_result = self.server_app.start()
        
        self.assertTrue(start_result['success'], f"服务器启动失败: {start_result.get('message')}")
        
        # 获取服务器信息
        server_info = self.server_app.get_server_info()
        self.server_host = server_info['host']
        self.server_port = server_info['port']
        
        # 等待服务器完全启动
        time.sleep(1)
        
        # 创建测试用户
        self._create_test_users()
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'server_app'):
            self.server_app.shutdown()
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def _create_test_config(self):
        """创建测试配置文件"""
        config_content = f"""[server]
host = 127.0.0.1
port = 0
max_connections = 10

[database]
path = {os.path.join(self.test_dir, 'test_auth.db')}

[logging]
level = ERROR
file_path = {os.path.join(self.test_dir, 'test_server.log')}
max_file_size = 1048576
backup_count = 1

[security]
session_timeout = 300
max_failed_attempts = 3
lockout_duration = 60
password_min_length = 6
"""
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
    
    def _create_test_users(self):
        """创建测试用户"""
        # 创建额外的测试用户
        test_users = [
            ('user1', 'password1'),
            ('user2', 'password2'),
            ('testuser', 'testpass123')
        ]
        
        for username, password in test_users:
            result = self.server_app.user_manager.create_user(username, password)
            self.assertTrue(result['success'], f"创建用户 {username} 失败")
    
    def test_single_user_auth_flow(self):
        """测试单用户认证流程"""
        client = NetworkClient()
        
        try:
            # 1. 连接服务器
            connect_result = client.connect(self.server_host, self.server_port)
            self.assertTrue(connect_result['success'], f"连接失败: {connect_result.get('message')}")
            
            # 2. 发送认证请求
            auth_message = {
                'type': 'auth_request',
                'username': 'testuser',
                'password': 'testpass123'
            }
            
            send_result = client.send_message(auth_message)
            self.assertTrue(send_result['success'], "发送认证请求失败")
            
            # 3. 接收认证响应
            response = client.receive_message(timeout=10.0)
            self.assertIsNotNone(response, "未收到认证响应")
            self.assertEqual(response['type'], 'auth_response')
            self.assertTrue(response['success'], f"认证失败: {response.get('message')}")
            
            session_token = response['session_token']
            user_info = response['user_info']
            
            self.assertIsNotNone(session_token)
            self.assertEqual(user_info['username'], 'testuser')
            
            # 4. 验证会话
            validate_message = {
                'type': 'session_validate',
                'session_token': session_token
            }
            
            client.send_message(validate_message)
            validate_response = client.receive_message(timeout=5.0)
            
            self.assertIsNotNone(validate_response)
            self.assertEqual(validate_response['type'], 'session_response')
            self.assertTrue(validate_response['success'])
            
            # 5. 注销
            logout_message = {
                'type': 'logout',
                'session_token': session_token
            }
            
            client.send_message(logout_message)
            logout_response = client.receive_message(timeout=5.0)
            
            self.assertIsNotNone(logout_response)
            self.assertEqual(logout_response['type'], 'logout_response')
            self.assertTrue(logout_response['success'])
            
            # 6. 验证会话已失效
            client.send_message(validate_message)
            invalid_response = client.receive_message(timeout=5.0)
            
            self.assertIsNotNone(invalid_response)
            self.assertFalse(invalid_response['success'])
            
        finally:
            client.disconnect()
    
    def test_failed_authentication(self):
        """测试认证失败"""
        client = NetworkClient()
        
        try:
            # 连接服务器
            connect_result = client.connect(self.server_host, self.server_port)
            self.assertTrue(connect_result['success'])
            
            # 发送错误的认证请求
            auth_message = {
                'type': 'auth_request',
                'username': 'testuser',
                'password': 'wrongpassword'
            }
            
            client.send_message(auth_message)
            response = client.receive_message(timeout=5.0)
            
            self.assertIsNotNone(response)
            self.assertEqual(response['type'], 'auth_response')
            self.assertFalse(response['success'])
            self.assertIn('message', response)
            
        finally:
            client.disconnect()
    
    def test_nonexistent_user_auth(self):
        """测试不存在用户的认证"""
        client = NetworkClient()
        
        try:
            connect_result = client.connect(self.server_host, self.server_port)
            self.assertTrue(connect_result['success'])
            
            auth_message = {
                'type': 'auth_request',
                'username': 'nonexistent_user',
                'password': 'anypassword'
            }
            
            client.send_message(auth_message)
            response = client.receive_message(timeout=5.0)
            
            self.assertIsNotNone(response)
            self.assertFalse(response['success'])
            
        finally:
            client.disconnect()
    
    def test_concurrent_authentications(self):
        """测试并发认证"""
        def authenticate_user(username, password):
            """单个用户认证函数"""
            client = NetworkClient()
            try:
                # 连接
                connect_result = client.connect(self.server_host, self.server_port)
                if not connect_result['success']:
                    return {'success': False, 'error': 'connection_failed'}
                
                # 认证
                auth_message = {
                    'type': 'auth_request',
                    'username': username,
                    'password': password
                }
                
                send_result = client.send_message(auth_message)
                if not send_result['success']:
                    return {'success': False, 'error': 'send_failed'}
                
                response = client.receive_message(timeout=10.0)
                if not response:
                    return {'success': False, 'error': 'no_response'}
                
                return {
                    'success': response['success'],
                    'username': username,
                    'session_token': response.get('session_token'),
                    'message': response.get('message')
                }
                
            except Exception as e:
                return {'success': False, 'error': str(e)}
            finally:
                client.disconnect()
        
        # 并发认证测试
        users = [
            ('user1', 'password1'),
            ('user2', 'password2'),
            ('testuser', 'testpass123'),
            ('admin', 'admin123')  # 默认管理员
        ]
        
        results = []
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            # 提交所有认证任务
            future_to_user = {
                executor.submit(authenticate_user, username, password): (username, password)
                for username, password in users
            }
            
            # 收集结果
            for future in as_completed(future_to_user):
                username, password = future_to_user[future]
                try:
                    result = future.result(timeout=15)
                    results.append(result)
                except Exception as e:
                    results.append({
                        'success': False,
                        'username': username,
                        'error': str(e)
                    })
        
        # 验证结果
        successful_auths = [r for r in results if r['success']]
        failed_auths = [r for r in results if not r['success']]
        
        print(f"成功认证: {len(successful_auths)}")
        print(f"失败认证: {len(failed_auths)}")
        
        # 至少应该有一些成功的认证
        self.assertGreater(len(successful_auths), 0)
        
        # 检查成功认证的用户都有会话令牌
        for result in successful_auths:
            self.assertIn('session_token', result)
            self.assertIsNotNone(result['session_token'])
    
    def test_session_timeout(self):
        """测试会话超时（简化版）"""
        client = NetworkClient()
        
        try:
            # 连接并认证
            connect_result = client.connect(self.server_host, self.server_port)
            self.assertTrue(connect_result['success'])
            
            auth_message = {
                'type': 'auth_request',
                'username': 'testuser',
                'password': 'testpass123'
            }
            
            client.send_message(auth_message)
            response = client.receive_message(timeout=5.0)
            
            self.assertTrue(response['success'])
            session_token = response['session_token']
            
            # 手动使会话过期（通过数据库操作）
            with self.server_app.db_manager.get_connection() as conn:
                from datetime import datetime, timedelta
                expired_time = (datetime.now() - timedelta(hours=1)).isoformat()
                conn.execute(
                    "UPDATE sessions SET expires_at = ? WHERE session_token = ?",
                    (expired_time, session_token)
                )
                conn.commit()
            
            # 尝试验证过期会话
            validate_message = {
                'type': 'session_validate',
                'session_token': session_token
            }
            
            client.send_message(validate_message)
            validate_response = client.receive_message(timeout=5.0)
            
            self.assertIsNotNone(validate_response)
            self.assertFalse(validate_response['success'])
            
        finally:
            client.disconnect()
    
    def test_account_lockout(self):
        """测试账户锁定机制"""
        client = NetworkClient()
        
        try:
            connect_result = client.connect(self.server_host, self.server_port)
            self.assertTrue(connect_result['success'])
            
            # 多次失败尝试
            for i in range(4):  # 超过配置的3次限制
                auth_message = {
                    'type': 'auth_request',
                    'username': 'user1',
                    'password': 'wrongpassword'
                }
                
                client.send_message(auth_message)
                response = client.receive_message(timeout=5.0)
                
                self.assertFalse(response['success'])
            
            # 即使使用正确密码也应该被锁定
            correct_auth_message = {
                'type': 'auth_request',
                'username': 'user1',
                'password': 'password1'
            }
            
            client.send_message(correct_auth_message)
            response = client.receive_message(timeout=5.0)
            
            self.assertFalse(response['success'])
            self.assertIn('锁定', response['message'])
            
        finally:
            client.disconnect()


class TestGUIIntegration(unittest.TestCase):
    """GUI集成测试"""
    
def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        
        # 创建临时配置文件
        self.config_file = os.path.join(self.test_dir, 'test_config.ini')
        self._create_test_config()
        
        # 启动服务器应用
        self.server_app = ServerApplication(self.config_file)
        start_result = self.server_app.start()
        self.assertTrue(start_result['success'])
        
        # 获取服务器信息
        server_info = self.server_app.get_server_info()
        self.server_host = server_info['host']
        self.server_port = server_info['port']
        
        time.sleep(1)
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'server_app'):
            self.server_app.shutdown()
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def _create_test_config(self):
        """创建测试配置文件"""
        config_content = f"""[server]
host = 127.0.0.1
port = 0
max_connections = 10

[database]
path = {os.path.join(self.test_dir, 'test_auth.db')}

[logging]
level = ERROR
file_path = {os.path.join(self.test_dir, 'test_server.log')}
"""
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
    
    def test_auth_result_handler(self):
        """测试认证结果处理器"""
        events_received = []
        
        def test_callback(event_type, data):
            events_received.append((event_type, data))
        
        # 创建认证结果处理器
        auth_handler = AuthResultHandler(test_callback)
        
        # 模拟认证成功
        success_result = {
            'success': True,
            'session_token': 'test_token_123',
            'user_info': {'username': 'testuser', 'id': 1}
        }
        
        auth_handler.handle_auth_result(success_result)
        
        # 验证回调被调用
        self.assertGreater(len(events_received), 0)
        
        # 检查事件类型
        event_types = [event[0] for event in events_received]
        self.assertIn('auth_success', event_types)
        
        # 模拟认证失败
        events_received.clear()
        
        failure_result = {
            'success': False,
            'message': '用户名或密码错误'
        }
        
        auth_handler.handle_auth_result(failure_result)
        
        # 验证失败事件
        self.assertGreater(len(events_received), 0)
        event_types = [event[0] for event in events_received]
        self.assertIn('auth_failure', event_types)


class TestSystemStatistics(unittest.TestCase):
    """系统统计测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        
        # 创建临时配置文件
        self.config_file = os.path.join(self.test_dir, 'test_config.ini')
        self._create_test_config()
        
        # 启动服务器应用
        self.server_app = ServerApplication(self.config_file)
        start_result = self.server_app.start()
        self.assertTrue(start_result['success'])
        
        time.sleep(1)
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'server_app'):
            self.server_app.shutdown()
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def _create_test_config(self):
        """创建测试配置文件"""
        config_content = f"""[server]
host = 127.0.0.1
port = 0
max_connections = 10

[database]
path = {os.path.join(self.test_dir, 'test_auth.db')}

[logging]
level = ERROR
file_path = {os.path.join(self.test_dir, 'test_server.log')}
"""
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
    
    def test_server_statistics(self):
        """测试服务器统计信息"""
        stats = self.server_app.get_server_statistics()
        
        # 验证基本统计字段
        expected_fields = [
            'is_running',
            'server_version',
            'uptime_seconds',
            'total_connections',
            'successful_auths',
            'failed_auths'
        ]
        
        for field in expected_fields:
            self.assertIn(field, stats, f"统计信息缺少字段: {field}")
        
        # 验证服务器正在运行
        self.assertTrue(stats['is_running'])
        
        # 验证统计数据类型
        self.assertIsInstance(stats['uptime_seconds'], (int, float))
        self.assertIsInstance(stats['total_connections'], int)
        self.assertIsInstance(stats['successful_auths'], int)
        self.assertIsInstance(stats['failed_auths'], int)
    
    def test_database_statistics(self):
        """测试数据库统计信息"""
        db_info = self.server_app.db_manager.get_database_info()
        
        expected_fields = [
            'database_path',
            'database_size',
            'tables',
            'version'
        ]
        
        for field in expected_fields:
            self.assertIn(field, db_info, f"数据库信息缺少字段: {field}")
        
        # 验证表信息
        self.assertIsInstance(db_info['tables'], dict)
        
        # 验证必要的表存在
        required_tables = ['users', 'sessions', 'logs']
        for table in required_tables:
            self.assertIn(table, db_info['tables'])


def run_integration_tests():
    """运行集成测试"""
    print("网络验证工具集成测试")
    print("=" * 50)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestCompleteAuthFlow,
        TestGUIIntegration,
        TestSystemStatistics
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print(f"\n集成测试完成:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}")
            print(f"  {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}")
            print(f"  {traceback}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_integration_tests()
    sys.exit(0 if success else 1)
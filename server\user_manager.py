"""
用户管理模块
实现用户CRUD操作、密码加密验证和数据约束检查
"""

import hashlib
import secrets
import re
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from server.database_manager import DatabaseManager
from common.exceptions import DatabaseError, AuthenticationError
from common.constants import DEFAULT_CONFIG


class UserManager:
    """用户管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化用户管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.min_password_length = DEFAULT_CONFIG['security']['password_min_length']
        self.max_failed_attempts = DEFAULT_CONFIG['security']['max_failed_attempts']
        self.lockout_duration = DEFAULT_CONFIG['security']['lockout_duration']
    
    def validate_username(self, username: str) -> bool:
        """
        验证用户名格式
        
        Args:
            username: 用户名
            
        Returns:
            bool: 验证通过返回True
        """
        if not username or len(username) < 3 or len(username) > 50:
            return False
        
        # 用户名只能包含字母、数字、下划线和连字符
        pattern = r'^[a-zA-Z0-9_-]+$'
        return bool(re.match(pattern, username))
    
    def validate_password(self, password: str) -> Dict[str, Any]:
        """
        验证密码强度
        
        Args:
            password: 密码
            
        Returns:
            Dict[str, Any]: 验证结果，包含is_valid和message
        """
        result = {'is_valid': True, 'message': '密码验证通过'}
        
        if not password:
            return {'is_valid': False, 'message': '密码不能为空'}
        
        if len(password) < self.min_password_length:
            return {
                'is_valid': False, 
                'message': f'密码长度至少{self.min_password_length}位'
            }
        
        # 检查是否包含字母和数字
        has_letter = bool(re.search(r'[a-zA-Z]', password))
        has_digit = bool(re.search(r'\d', password))
        
        if not (has_letter and has_digit):
            return {
                'is_valid': False,
                'message': '密码必须包含字母和数字'
            }
        
        return result
    
    def generate_salt(self) -> str:
        """
        生成随机盐值
        
        Returns:
            str: 32字节的十六进制盐值
        """
        return secrets.token_hex(16)
    
    def hash_password(self, password: str, salt: str) -> str:
        """
        使用SHA-256和盐值对密码进行哈希
        
        Args:
            password: 原始密码
            salt: 盐值
            
        Returns:
            str: 哈希后的密码
        """
        # 将密码和盐值组合后进行SHA-256哈希
        combined = f"{password}{salt}".encode('utf-8')
        return hashlib.sha256(combined).hexdigest()
    
    def verify_password(self, password: str, stored_hash: str, salt: str) -> bool:
        """
        验证密码是否正确
        
        Args:
            password: 输入的密码
            stored_hash: 存储的密码哈希
            salt: 盐值
            
        Returns:
            bool: 密码正确返回True
        """
        computed_hash = self.hash_password(password, salt)
        return computed_hash == stored_hash
    
    def create_user(self, username: str, password: str) -> Dict[str, Any]:
        """
        创建新用户
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        # 验证用户名
        if not self.validate_username(username):
            return {
                'success': False,
                'message': '用户名格式无效（3-50位字母数字下划线连字符）'
            }
        
        # 验证密码
        password_validation = self.validate_password(password)
        if not password_validation['is_valid']:
            return {
                'success': False,
                'message': password_validation['message']
            }
        
        # 检查用户是否已存在
        if self.db_manager.get_user(username):
            return {
                'success': False,
                'message': '用户名已存在'
            }
        
        try:
            # 生成盐值和密码哈希
            salt = self.generate_salt()
            password_hash = self.hash_password(password, salt)
            
            # 添加用户到数据库
            if self.db_manager.add_user(username, password_hash, salt):
                return {
                    'success': True,
                    'message': '用户创建成功',
                    'username': username
                }
            else:
                return {
                    'success': False,
                    'message': '用户创建失败'
                }
                
        except DatabaseError as e:
            return {
                'success': False,
                'message': f'数据库错误: {str(e)}'
            }
    
    def authenticate_user(self, username: str, password: str, client_ip: str = None) -> Dict[str, Any]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            client_ip: 客户端IP地址
            
        Returns:
            Dict[str, Any]: 认证结果
        """
        try:
            # 获取用户信息
            user = self.db_manager.get_user(username)
            if not user:
                return {
                    'success': False,
                    'message': '用户名或密码错误',
                    'locked': False
                }
            
            # 检查账户是否被锁定
            if user['locked_until']:
                locked_until = datetime.fromisoformat(user['locked_until'])
                if datetime.now() < locked_until:
                    remaining_time = int((locked_until - datetime.now()).total_seconds())
                    return {
                        'success': False,
                        'message': f'账户已被锁定，剩余时间: {remaining_time}秒',
                        'locked': True,
                        'remaining_time': remaining_time
                    }
                else:
                    # 锁定时间已过，重置失败次数和锁定状态
                    self.db_manager.update_user(
                        username,
                        failed_attempts=0,
                        locked_until=None
                    )
                    user['failed_attempts'] = 0
                    user['locked_until'] = None
            
            # 检查账户是否激活
            if not user['is_active']:
                return {
                    'success': False,
                    'message': '账户已被禁用',
                    'locked': False
                }
            
            # 验证密码
            if self.verify_password(password, user['password_hash'], user['salt']):
                # 认证成功，重置失败次数并更新最后登录时间
                self.db_manager.update_user(
                    username,
                    failed_attempts=0,
                    locked_until=None,
                    last_login=datetime.now().isoformat()
                )
                
                return {
                    'success': True,
                    'message': '认证成功',
                    'user_id': user['id'],
                    'username': username,
                    'last_login': user['last_login']
                }
            else:
                # 认证失败，增加失败次数
                failed_attempts = user['failed_attempts'] + 1
                update_data = {'failed_attempts': failed_attempts}
                
                # 检查是否需要锁定账户
                if failed_attempts >= self.max_failed_attempts:
                    locked_until = datetime.now() + timedelta(seconds=self.lockout_duration)
                    update_data['locked_until'] = locked_until.isoformat()
                    
                    self.db_manager.update_user(username, **update_data)
                    
                    return {
                        'success': False,
                        'message': f'认证失败次数过多，账户已被锁定{self.lockout_duration}秒',
                        'locked': True,
                        'remaining_time': self.lockout_duration
                    }
                else:
                    self.db_manager.update_user(username, **update_data)
                    remaining_attempts = self.max_failed_attempts - failed_attempts
                    
                    return {
                        'success': False,
                        'message': f'用户名或密码错误，剩余尝试次数: {remaining_attempts}',
                        'locked': False,
                        'remaining_attempts': remaining_attempts
                    }
                    
        except DatabaseError as e:
            return {
                'success': False,
                'message': f'数据库错误: {str(e)}',
                'locked': False
            }
    
    def update_user_password(self, username: str, new_password: str) -> Dict[str, Any]:
        """
        更新用户密码
        
        Args:
            username: 用户名
            new_password: 新密码
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        # 验证新密码
        password_validation = self.validate_password(new_password)
        if not password_validation['is_valid']:
            return {
                'success': False,
                'message': password_validation['message']
            }
        
        try:
            # 检查用户是否存在
            if not self.db_manager.get_user(username):
                return {
                    'success': False,
                    'message': '用户不存在'
                }
            
            # 生成新的盐值和密码哈希
            salt = self.generate_salt()
            password_hash = self.hash_password(new_password, salt)
            
            # 更新密码并重置失败次数
            if self.db_manager.update_user(
                username,
                password_hash=password_hash,
                salt=salt,
                failed_attempts=0,
                locked_until=None
            ):
                return {
                    'success': True,
                    'message': '密码更新成功'
                }
            else:
                return {
                    'success': False,
                    'message': '密码更新失败'
                }
                
        except DatabaseError as e:
            return {
                'success': False,
                'message': f'数据库错误: {str(e)}'
            }
    
    def delete_user(self, username: str) -> Dict[str, Any]:
        """
        删除用户
        
        Args:
            username: 用户名
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            if self.db_manager.delete_user(username):
                return {
                    'success': True,
                    'message': '用户删除成功'
                }
            else:
                return {
                    'success': False,
                    'message': '用户不存在或删除失败'
                }
                
        except DatabaseError as e:
            return {
                'success': False,
                'message': f'数据库错误: {str(e)}'
            }
    
    def get_user_info(self, username: str) -> Optional[Dict[str, Any]]:
        """
        获取用户信息（不包含敏感数据）
        
        Args:
            username: 用户名
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息
        """
        try:
            user = self.db_manager.get_user(username)
            if user:
                # 移除敏感信息
                safe_user = {
                    'id': user['id'],
                    'username': user['username'],
                    'created_at': user['created_at'],
                    'last_login': user['last_login'],
                    'is_active': user['is_active'],
                    'failed_attempts': user['failed_attempts'],
                    'is_locked': bool(user['locked_until'] and 
                                    datetime.fromisoformat(user['locked_until']) > datetime.now())
                }
                return safe_user
            return None
            
        except DatabaseError:
            return None
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """
        获取所有用户列表（不包含敏感数据）
        
        Returns:
            List[Dict[str, Any]]: 用户列表
        """
        try:
            users = self.db_manager.get_all_users()
            safe_users = []
            
            for user in users:
                safe_user = {
                    'id': user['id'],
                    'username': user['username'],
                    'created_at': user['created_at'],
                    'last_login': user['last_login'],
                    'is_active': user['is_active'],
                    'failed_attempts': user['failed_attempts'],
                    'is_locked': bool(user['locked_until'] and 
                                    datetime.fromisoformat(user['locked_until']) > datetime.now())
                }
                safe_users.append(safe_user)
            
            return safe_users
            
        except DatabaseError:
            return []
    
    def toggle_user_status(self, username: str) -> Dict[str, Any]:
        """
        切换用户激活状态
        
        Args:
            username: 用户名
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            user = self.db_manager.get_user(username)
            if not user:
                return {
                    'success': False,
                    'message': '用户不存在'
                }
            
            new_status = not user['is_active']
            if self.db_manager.update_user(username, is_active=new_status):
                status_text = '激活' if new_status else '禁用'
                return {
                    'success': True,
                    'message': f'用户已{status_text}',
                    'is_active': new_status
                }
            else:
                return {
                    'success': False,
                    'message': '状态更新失败'
                }
                
        except DatabaseError as e:
            return {
                'success': False,
                'message': f'数据库错误: {str(e)}'
            }
    
    def list_users(self) -> List[Dict[str, Any]]:
        """
        获取用户列表（Web GUI API兼容方法）
        
        Returns:
            List[Dict[str, Any]]: 用户列表
        """
        return self.get_all_users()
    
    def change_password(self, username: str, new_password: str) -> Dict[str, Any]:
        """
        修改用户密码（Web GUI API兼容方法）
        
        Args:
            username: 用户名
            new_password: 新密码
            
        Returns:
            Dict[str, Any]: 修改结果
        """
        return self.update_user_password(username, new_password)
    
    def register_user(self, username: str, password: str, email: str = None, client_ip: str = None) -> Dict[str, Any]:
        """
        用户注册（使用新的注册管理器）
        
        Args:
            username: 用户名
            password: 密码
            email: 邮箱地址（可选）
            client_ip: 客户端IP地址
            
        Returns:
            Dict[str, Any]: 注册结果
        """
        try:
            # 导入用户注册管理器
            from server.user_registration_manager import UserRegistrationManager
            
            # 创建注册管理器实例
            registration_manager = UserRegistrationManager(self.db_manager)
            
            # 执行注册
            result = registration_manager.register_user(
                username=username,
                password=password,
                confirm_password=password,  # 对于服务器端注册，确认密码与密码相同
                email=email,
                registration_ip=client_ip
            )
            
            return result
            
        except ImportError:
            # 如果无法导入新的注册管理器，使用旧的create_user方法
            return self.create_user(username, password)
        except Exception as e:
            return {
                'success': False,
                'message': f'注册过程中发生错误: {str(e)}'
            }
"""
客户端网络通信模块
实现与服务器的连接、认证请求发送和响应接收
"""

import socket
import json
import threading
import time
from typing import Dict, Any, Optional
from datetime import datetime

from server.protocol_handler import ClientProtocolHandler
from common.constants import MESSAGE_TYPES, DEFAULT_CONFIG
from common.exceptions import NetworkError


class NetworkClient:
    """网络客户端"""
    
    def __init__(self):
        """初始化网络客户端"""
        self.socket = None
        self.is_connected_flag = False
        self.host = None
        self.port = None
        
        # 协议处理器
        self.protocol_handler = ClientProtocolHandler()
        
        # 连接配置
        self.connection_timeout = 10.0
        self.receive_timeout = 30.0
        self.max_retry_attempts = 3
        self.retry_delay = 1.0
        
        # 线程安全锁
        self._lock = threading.Lock()
        
        # 连接统计
        self.stats = {
            'connect_time': None,
            'total_messages_sent': 0,
            'total_messages_received': 0,
            'last_activity': None,
            'reconnect_attempts': 0
        }
    
    def connect(self, host: str, port: int, timeout: float = None) -> Dict[str, Any]:
        """
        连接到服务器
        
        Args:
            host: 服务器地址
            port: 服务器端口
            timeout: 连接超时时间
            
        Returns:
            Dict[str, Any]: 连接结果
        """
        with self._lock:
            if self.is_connected_flag:
                return {
                    'success': False,
                    'message': '已经连接到服务器'
                }
            
            try:
                # 创建socket
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                
                # 设置超时
                connect_timeout = timeout or self.connection_timeout
                self.socket.settimeout(connect_timeout)
                
                # 连接到服务器
                self.socket.connect((host, port))
                
                # 连接成功
                self.is_connected_flag = True
                self.host = host
                self.port = port
                self.stats['connect_time'] = datetime.now().isoformat()
                self.stats['last_activity'] = datetime.now().isoformat()
                
                # 接收欢迎消息
                welcome_msg = self._receive_message()
                
                return {
                    'success': True,
                    'message': f'成功连接到 {host}:{port}',
                    'welcome_message': welcome_msg
                }
                
            except socket.timeout:
                self._cleanup_connection()
                return {
                    'success': False,
                    'message': f'连接超时 ({connect_timeout}秒)'
                }
            except socket.error as e:
                self._cleanup_connection()
                return {
                    'success': False,
                    'message': f'连接失败: {str(e)}'
                }
            except Exception as e:
                self._cleanup_connection()
                return {
                    'success': False,
                    'message': f'连接时发生未知错误: {str(e)}'
                }
    
    def disconnect(self) -> Dict[str, Any]:
        """
        断开连接
        
        Returns:
            Dict[str, Any]: 断开结果
        """
        with self._lock:
            try:
                if self.socket:
                    self.socket.close()
                
                self._cleanup_connection()
                
                return {
                    'success': True,
                    'message': '已断开连接'
                }
                
            except Exception as e:
                self._cleanup_connection()
                return {
                    'success': False,
                    'message': f'断开连接时发生错误: {str(e)}'
                }
    
    def _cleanup_connection(self):
        """清理连接状态"""
        self.is_connected_flag = False
        self.socket = None
        self.host = None
        self.port = None
        self.protocol_handler.clear_session()
    
    def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Dict[str, Any]: 认证结果
        """
        if not self.is_connected_flag:
            return {
                'success': False,
                'message': '未连接到服务器'
            }
        
        try:
            # 创建认证请求
            client_info = {
                'version': '1.0.0',
                'platform': 'Windows'
            }
            
            auth_request = self.protocol_handler.create_auth_request(
                username, password, client_info
            )
            
            # 发送认证请求
            send_result = self._send_message(auth_request)
            if not send_result['success']:
                return send_result
            
            # 接收认证响应
            response = self._receive_message()
            if not response:
                return {
                    'success': False,
                    'message': '未收到服务器响应'
                }
            
            # 处理认证响应
            auth_result = self.protocol_handler.process_auth_response(response)
            
            return auth_result
            
        except Exception as e:
            return {
                'success': False,
                'message': f'认证过程中发生错误: {str(e)}'
            }
    
    def register_user(self, username: str, password: str, email: str = None) -> Dict[str, Any]:
        """
        用户注册
        
        Args:
            username: 用户名
            password: 密码
            email: 邮箱地址（可选）
            
        Returns:
            Dict[str, Any]: 注册结果
        """
        if not self.is_connected_flag:
            return {
                'success': False,
                'message': '未连接到服务器'
            }
        
        try:
            # 创建注册请求
            register_request = {
                'type': 'REGISTER',
                'username': username,
                'password': password,
                'email': email,
                'client_info': {
                    'version': '1.0.0',
                    'platform': 'Windows'
                }
            }
            
            # 发送注册请求
            send_result = self._send_message(register_request)
            if not send_result['success']:
                return send_result
            
            # 接收注册响应
            response = self._receive_message()
            if not response:
                return {
                    'success': False,
                    'message': '未收到服务器响应'
                }
            
            # 处理注册响应
            if response.get('type') == 'REGISTER_RESPONSE':
                if response.get('success', False):
                    return {
                        'success': True,
                        'message': response.get('message', '注册成功'),
                        'username': response.get('username')
                    }
                else:
                    return {
                        'success': False,
                        'message': response.get('message', '注册失败'),
                        'errors': response.get('errors', [])
                    }
            else:
                return {
                    'success': False,
                    'message': f'收到意外响应: {response.get("type", "unknown")}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'注册过程中发生错误: {str(e)}'
            }
    
    def logout(self) -> Dict[str, Any]:
        """
        用户登出
        
        Returns:
            Dict[str, Any]: 登出结果
        """
        try:
            # 清除本地会话信息
            self.protocol_handler.clear_session()
            
            return {
                'success': True,
                'message': '已登出'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'登出时发生错误: {str(e)}'
            }
    
    def send_heartbeat(self) -> Dict[str, Any]:
        """
        发送心跳消息
        
        Returns:
            Dict[str, Any]: 心跳结果
        """
        if not self.is_connected_flag:
            return {
                'success': False,
                'message': '未连接到服务器'
            }
        
        try:
            # 创建心跳消息
            heartbeat_msg = self.protocol_handler.create_heartbeat()
            
            # 发送心跳
            send_result = self._send_message(heartbeat_msg)
            if not send_result['success']:
                return send_result
            
            # 接收心跳响应
            response = self._receive_message()
            if not response:
                return {
                    'success': False,
                    'message': '未收到心跳响应'
                }
            
            if response.get('type') == MESSAGE_TYPES['HEARTBEAT']:
                return {
                    'success': True,
                    'message': '心跳正常',
                    'server_time': response.get('server_time'),
                    'session_refreshed': response.get('session_refreshed', False)
                }
            else:
                return {
                    'success': False,
                    'message': f'收到意外响应: {response.get("type", "unknown")}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'心跳测试失败: {str(e)}'
            }
    
    def _send_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送消息到服务器
        
        Args:
            message: 要发送的消息
            
        Returns:
            Dict[str, Any]: 发送结果
        """
        try:
            # 序列化消息
            message_json = json.dumps(message, ensure_ascii=False)
            message_bytes = message_json.encode('utf-8')
            
            # 发送消息长度（4字节）
            length = len(message_bytes)
            self.socket.send(length.to_bytes(4, byteorder='big'))
            
            # 发送消息内容
            self.socket.send(message_bytes)
            
            # 更新统计
            self.stats['total_messages_sent'] += 1
            self.stats['last_activity'] = datetime.now().isoformat()
            
            return {
                'success': True,
                'message': '消息发送成功'
            }
            
        except socket.error as e:
            # 网络错误，可能连接已断开
            self._cleanup_connection()
            return {
                'success': False,
                'message': f'发送消息失败: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'发送消息时发生错误: {str(e)}'
            }
    
    def _receive_message(self, timeout: float = None) -> Optional[Dict[str, Any]]:
        """
        从服务器接收消息
        
        Args:
            timeout: 接收超时时间
            
        Returns:
            Optional[Dict[str, Any]]: 接收到的消息，失败返回None
        """
        try:
            # 设置接收超时
            receive_timeout = timeout or self.receive_timeout
            self.socket.settimeout(receive_timeout)
            
            # 接收消息长度（4字节）
            length_bytes = self._receive_exact(4)
            if not length_bytes:
                return None
            
            message_length = int.from_bytes(length_bytes, byteorder='big')
            
            # 检查消息长度是否合理（最大1MB）
            if message_length > 1024 * 1024:
                raise NetworkError("消息长度超出限制")
            
            # 接收消息内容
            message_bytes = self._receive_exact(message_length)
            if not message_bytes:
                return None
            
            # 解析JSON消息
            message_json = message_bytes.decode('utf-8')
            message = json.loads(message_json)
            
            # 更新统计
            self.stats['total_messages_received'] += 1
            self.stats['last_activity'] = datetime.now().isoformat()
            
            return message
            
        except socket.timeout:
            return None
        except socket.error:
            # 网络错误，可能连接已断开
            self._cleanup_connection()
            return None
        except json.JSONDecodeError:
            return None
        except Exception:
            return None
    
    def _receive_exact(self, length: int) -> Optional[bytes]:
        """
        接收指定长度的数据
        
        Args:
            length: 要接收的数据长度
            
        Returns:
            Optional[bytes]: 接收到的数据，失败返回None
        """
        data = b''
        while len(data) < length:
            try:
                chunk = self.socket.recv(length - len(data))
                if not chunk:
                    return None
                data += chunk
            except socket.error:
                return None
        return data
    
    def reconnect(self) -> Dict[str, Any]:
        """
        重新连接到服务器
        
        Returns:
            Dict[str, Any]: 重连结果
        """
        if not self.host or not self.port:
            return {
                'success': False,
                'message': '没有可用的连接信息'
            }
        
        # 断开当前连接
        self.disconnect()
        
        # 尝试重新连接
        for attempt in range(self.max_retry_attempts):
            self.stats['reconnect_attempts'] += 1
            
            result = self.connect(self.host, self.port)
            
            if result['success']:
                return {
                    'success': True,
                    'message': f'重连成功 (尝试 {attempt + 1}/{self.max_retry_attempts})',
                    'attempts': attempt + 1
                }
            
            # 等待后重试
            if attempt < self.max_retry_attempts - 1:
                time.sleep(self.retry_delay * (attempt + 1))
        
        return {
            'success': False,
            'message': f'重连失败，已尝试 {self.max_retry_attempts} 次'
        }
    
    def is_connected(self) -> bool:
        """
        检查是否已连接
        
        Returns:
            bool: 已连接返回True
        """
        return self.is_connected_flag and self.socket is not None
    
    def is_authenticated(self) -> bool:
        """
        检查是否已认证
        
        Returns:
            bool: 已认证返回True
        """
        return self.protocol_handler.is_authenticated()
    
    def get_connection_info(self) -> Dict[str, Any]:
        """
        获取连接信息
        
        Returns:
            Dict[str, Any]: 连接信息
        """
        return {
            'is_connected': self.is_connected_flag,
            'host': self.host,
            'port': self.port,
            'is_authenticated': self.is_authenticated(),
            'user_info': self.protocol_handler.user_info,
            'session_token': self.protocol_handler.session_token[:8] + '...' if self.protocol_handler.session_token else None
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取连接统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self.stats.copy()
        stats.update({
            'is_connected': self.is_connected_flag,
            'is_authenticated': self.is_authenticated(),
            'host': self.host,
            'port': self.port
        })
        
        # 计算连接时长
        if stats['connect_time']:
            connect_time = datetime.fromisoformat(stats['connect_time'])
            uptime_seconds = int((datetime.now() - connect_time).total_seconds())
            stats['uptime_seconds'] = uptime_seconds
        else:
            stats['uptime_seconds'] = 0
        
        return stats
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试连接状态
        
        Returns:
            Dict[str, Any]: 测试结果
        """
        if not self.is_connected_flag:
            return {
                'success': False,
                'message': '未连接到服务器'
            }
        
        try:
            # 发送心跳测试连接
            return self.send_heartbeat()
            
        except Exception as e:
            return {
                'success': False,
                'message': f'连接测试失败: {str(e)}'
            }
    
    def set_timeouts(self, connection_timeout: float = None, receive_timeout: float = None):
        """
        设置超时时间
        
        Args:
            connection_timeout: 连接超时时间
            receive_timeout: 接收超时时间
        """
        if connection_timeout is not None:
            self.connection_timeout = connection_timeout
        
        if receive_timeout is not None:
            self.receive_timeout = receive_timeout
    
    def set_retry_config(self, max_attempts: int = None, retry_delay: float = None):
        """
        设置重试配置
        
        Args:
            max_attempts: 最大重试次数
            retry_delay: 重试延迟时间
        """
        if max_attempts is not None:
            self.max_retry_attempts = max_attempts
        
        if retry_delay is not None:
            self.retry_delay = retry_delay
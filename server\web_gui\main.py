"""
Web GUI 主启动文件
使用 PyWebView 创建桌面应用程序
"""

import sys
import os
import webview
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from server.web_gui.api import WebGUIAPI


def create_window():
    """创建 PyWebView 窗口"""
    
    # 使用单文件版本的HTML
    web_gui_dir = Path(__file__).parent
    html_file = web_gui_dir / 'index.html'
    
    # 如果单文件版本不存在，回退到模板版本
    if not html_file.exists():
        template_dir = web_gui_dir / 'templates'
        html_file = template_dir / 'index.html'
    
    # 创建 API 实例
    api = WebGUIAPI()
    
    # 创建窗口
    window = webview.create_window(
        title='网络验证工具 - 服务器管理',
        url=str(html_file),
        js_api=api,
        width=1200,
        height=800,
        min_size=(800, 600),
        resizable=True,
        shadow=True,
        on_top=False,
        text_select=False
    )
    
    return window, api


def main():
    """主函数"""
    try:
        print("启动网络验证工具 Web GUI...")
        
        # 检查是否安装了 pywebview
        try:
            import webview
            print(f"✓ pywebview 已安装 (版本: {getattr(webview, '__version__', '未知')})")
        except ImportError:
            print("✗ 错误: 未安装 pywebview")
            print("请运行: pip install pywebview")
            return 1
        
        # 检查HTML文件
        web_gui_dir = Path(__file__).parent
        html_file = web_gui_dir / 'index.html'
        
        if html_file.exists():
            print(f"✓ 使用单文件版本: {html_file}")
        else:
            # 回退到模板版本
            template_dir = web_gui_dir / 'templates'
            html_file = template_dir / 'index.html'
            
            if not html_file.exists():
                print(f"✗ 错误: HTML文件不存在: {html_file}")
                return 1
            
            print(f"✓ 使用模板版本: {html_file}")
        
        # 测试API初始化
        try:
            api = WebGUIAPI()
            print("✓ WebGUIAPI 初始化成功")
            
            # 测试基本API调用
            about_info = api.get_about_info()
            print(f"✓ API测试成功: {about_info.get('name', 'N/A')}")
            
        except Exception as e:
            print(f"✗ API初始化失败: {e}")
            print("尝试继续启动...")
            api = WebGUIAPI()  # 重新尝试
        
        # 创建窗口
        print("创建WebView窗口...")
        window = webview.create_window(
            title='网络验证工具 - 服务器管理',
            url=str(html_file),
            js_api=api,
            width=1200,
            height=800,
            min_size=(800, 600),
            resizable=True,
            shadow=True,
            on_top=False,
            text_select=False
        )
        
        print("✓ WebView窗口创建成功")
        
        # 设置调试模式
        debug = '--debug' in sys.argv or '-d' in sys.argv
        if debug:
            print("🔧 调试模式已启用")
        
        # 启动应用
        print("启动WebView应用...")
        webview.start(
            debug=debug,
            http_server=True,  # 启用内置HTTP服务器
            http_port=0,       # 自动选择端口
        )
        
        print("Web GUI 已关闭")
        return 0
        
    except KeyboardInterrupt:
        print("\n应用被用户中断")
        return 1
    except Exception as e:
        print(f"✗ 启动 Web GUI 时发生错误: {e}")
        import traceback
        traceback.print_exc()
        
        # 提供故障排除建议
        print("\n故障排除建议:")
        print("1. 确保已安装 pywebview: pip install pywebview")
        print("2. 检查系统是否支持WebView (Windows需要Edge WebView2)")
        print("3. 尝试运行测试: python test_webgui_startup.py")
        print("4. 使用调试模式: python server/web_gui/main.py --debug")
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
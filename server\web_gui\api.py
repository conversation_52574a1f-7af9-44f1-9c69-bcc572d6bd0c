"""
Web GUI API 后端
提供服务器管理的 Web API 接口
"""

import sys
import os
import threading
import time
from typing import Dict, Any, List
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from server.server_application import ServerApplication
from server.database_manager import DatabaseManager
from server.user_manager import UserManager
from server.session_manager import SessionManager
from common.config_manager import ConfigManager
from common.log_manager import LogManager


class WebGUIAPI:
    """Web GUI API 类"""
    
    def __init__(self):
        """初始化 API"""
        self.server_app = None
        self.config_manager = None
        self.log_manager = None
        self.db_manager = None
        self.user_manager = None
        self.session_manager = None
        
        # 初始化组件
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化组件"""
        try:
            # 初始化配置管理器
            self.config_manager = ConfigManager("config.ini")
            
            # 初始化日志管理器
            log_config = self.config_manager.get_section('logging')
            self.log_manager = LogManager(log_config)
            
            # 初始化数据库管理器
            db_path = self.config_manager.get_value('database', 'path', 'data/auth.db')
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            self.db_manager = DatabaseManager(db_path)
            
            # 初始化用户管理器
            self.user_manager = UserManager(self.db_manager)
            
            # 初始化会话管理器
            self.session_manager = SessionManager(self.db_manager)
            
            print("Web GUI API 组件初始化完成")
            
        except Exception as e:
            print(f"Web GUI API 初始化失败: {e}")
            raise
    
    # 服务器控制 API
    def get_server_stats(self) -> Dict[str, Any]:
        """获取服务器统计信息"""
        try:
            if self.server_app and self.server_app.is_server_running():
                stats = self.server_app.get_server_statistics()
                server_info = self.server_app.get_server_info()
                
                # 合并统计信息和服务器信息
                result = {**stats, **server_info}
                return result
            else:
                # 服务器未运行时返回基本信息
                return {
                    'is_running': False,
                    'server_version': '1.0.0',
                    'total_connections': 0,
                    'successful_auths': 0,
                    'failed_auths': 0,
                    'active_sessions': 0,
                    'uptime_seconds': 0,
                    'host': self.config_manager.get_value('server', 'host', '0.0.0.0'),
                    'port': self.config_manager.get_value('server', 'port', 8888),
                    'start_time': None
                }
        except Exception as e:
            print(f"获取服务器统计失败: {e}")
            return {
                'error': str(e),
                'is_running': False
            }
    
    def start_server(self) -> Dict[str, Any]:
        """启动服务器"""
        try:
            if self.server_app and self.server_app.is_server_running():
                return {
                    'success': False,
                    'message': '服务器已在运行中'
                }
            
            # 创建新的服务器应用实例
            self.server_app = ServerApplication("config.ini")
            
            # 启动服务器
            result = self.server_app.start()
            
            if result['success']:
                self.log_manager.log_info("通过Web GUI启动服务器")
            
            return result
            
        except Exception as e:
            error_msg = f"启动服务器失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def stop_server(self) -> Dict[str, Any]:
        """停止服务器"""
        try:
            if not self.server_app or not self.server_app.is_server_running():
                return {
                    'success': False,
                    'message': '服务器未在运行'
                }
            
            # 停止服务器
            result = self.server_app.shutdown()
            
            if result['success']:
                self.log_manager.log_info("通过Web GUI停止服务器")
                self.server_app = None
            
            return result
            
        except Exception as e:
            error_msg = f"停止服务器失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def restart_server(self) -> Dict[str, Any]:
        """重启服务器"""
        try:
            # 先停止服务器
            if self.server_app and self.server_app.is_server_running():
                stop_result = self.stop_server()
                if not stop_result['success']:
                    return stop_result
                
                # 等待一秒钟
                time.sleep(1)
            
            # 再启动服务器
            start_result = self.start_server()
            
            if start_result['success']:
                self.log_manager.log_info("通过Web GUI重启服务器")
                return {
                    'success': True,
                    'message': '服务器重启成功'
                }
            else:
                return start_result
                
        except Exception as e:
            error_msg = f"重启服务器失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    # 用户管理 API
    def get_users(self) -> List[Dict[str, Any]]:
        """获取用户列表"""
        try:
            users = self.user_manager.list_users()
            return users
        except Exception as e:
            print(f"获取用户列表失败: {e}")
            return []
    
    def create_user(self, username: str, password: str) -> Dict[str, Any]:
        """创建用户"""
        try:
            result = self.user_manager.create_user(username, password)
            
            if result['success']:
                self.log_manager.log_info(f"通过Web GUI创建用户: {username}")
            
            return result
            
        except Exception as e:
            error_msg = f"创建用户失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def register_user(self, username: str, password: str, email: str = None) -> Dict[str, Any]:
        """用户注册（使用新的注册管理器）"""
        try:
            result = self.user_manager.register_user(username, password, email, 'web_gui')
            
            if result['success']:
                self.log_manager.log_info(f"通过Web GUI注册用户: {username}")
            
            return result
            
        except Exception as e:
            error_msg = f"用户注册失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def get_registration_statistics(self) -> Dict[str, Any]:
        """获取注册统计信息"""
        try:
            from server.user_registration_manager import UserRegistrationManager
            
            registration_manager = UserRegistrationManager(self.db_manager)
            stats = registration_manager.get_registration_statistics()
            
            return {
                'success': True,
                'data': stats
            }
            
        except Exception as e:
            error_msg = f"获取注册统计失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': {
                    'total_users': 0,
                    'active_users': 0,
                    'inactive_users': 0,
                    'registration_by_date': {}
                }
            }
    
    def delete_user(self, username: str) -> Dict[str, Any]:
        """删除用户"""
        try:
            result = self.user_manager.delete_user(username)
            
            if result['success']:
                self.log_manager.log_info(f"通过Web GUI删除用户: {username}")
            
            return result
            
        except Exception as e:
            error_msg = f"删除用户失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def change_user_password(self, username: str, new_password: str) -> Dict[str, Any]:
        """修改用户密码"""
        try:
            result = self.user_manager.change_password(username, new_password)
            
            if result['success']:
                self.log_manager.log_info(f"通过Web GUI修改用户密码: {username}")
            
            return result
            
        except Exception as e:
            error_msg = f"修改用户密码失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    # 会话管理 API
    def get_sessions(self) -> List[Dict[str, Any]]:
        """获取会话列表"""
        try:
            # 获取活跃会话
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT s.session_token, s.username, s.client_ip, s.created_at, s.expires_at,
                           CASE WHEN s.expires_at > datetime('now') THEN 1 ELSE 0 END as is_active
                    FROM sessions s
                    ORDER BY s.created_at DESC
                    LIMIT 100
                """)
                
                sessions = []
                for row in cursor.fetchall():
                    sessions.append({
                        'session_token': row['session_token'],
                        'username': row['username'],
                        'client_ip': row['client_ip'],
                        'created_at': row['created_at'],
                        'expires_at': row['expires_at'],
                        'is_active': bool(row['is_active'])
                    })
                
                return sessions
                
        except Exception as e:
            print(f"获取会话列表失败: {e}")
            return []
    
    def terminate_session(self, session_token: str) -> Dict[str, Any]:
        """终止会话"""
        try:
            result = self.session_manager.destroy_session(session_token)
            
            if result['success']:
                self.log_manager.log_info(f"通过Web GUI终止会话: {session_token[:16]}...")
            
            return result
            
        except Exception as e:
            error_msg = f"终止会话失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    # 日志管理 API
    def get_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取系统日志"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT timestamp, level, message, source
                    FROM logs
                    ORDER BY timestamp DESC
                    LIMIT ?
                """, (limit,))
                
                logs = []
                for row in cursor.fetchall():
                    logs.append({
                        'timestamp': row['timestamp'],
                        'level': row['level'],
                        'message': row['message'],
                        'source': row['source']
                    })
                
                return logs
                
        except Exception as e:
            print(f"获取日志失败: {e}")
            return []
    
    def clear_logs(self) -> Dict[str, Any]:
        """清空日志"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute("DELETE FROM logs")
                deleted_count = cursor.rowcount
                conn.commit()
            
            self.log_manager.log_info("通过Web GUI清空系统日志")
            
            return {
                'success': True,
                'message': f'已清空 {deleted_count} 条日志记录'
            }
            
        except Exception as e:
            error_msg = f"清空日志失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    # 配置管理 API
    def get_config(self) -> Dict[str, Any]:
        """获取配置信息"""
        try:
            config = {}
            
            # 服务器配置
            server_config = self.config_manager.get_section('server')
            config.update(server_config)
            
            # 安全配置
            security_config = self.config_manager.get_section('security')
            config.update(security_config)
            
            return config
            
        except Exception as e:
            print(f"获取配置失败: {e}")
            return {}
    
    def save_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """保存配置"""
        try:
            # 更新配置
            for key, value in config_data.items():
                if key in ['host', 'port', 'max_connections']:
                    self.config_manager.set_value('server', key, value)
                elif key in ['session_timeout', 'max_failed_attempts', 'lockout_duration']:
                    self.config_manager.set_value('security', key, value)
            
            # 保存配置文件
            self.config_manager.save_config()
            
            self.log_manager.log_info("通过Web GUI保存配置")
            
            return {
                'success': True,
                'message': '配置保存成功'
            }
            
        except Exception as e:
            error_msg = f"保存配置失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    # 系统信息 API
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            import platform
            import psutil
            
            # 系统信息
            system_info = {
                'platform': platform.system(),
                'platform_version': platform.version(),
                'architecture': platform.architecture()[0],
                'processor': platform.processor(),
                'python_version': platform.python_version(),
                'hostname': platform.node()
            }
            
            # 资源使用情况
            try:
                system_info.update({
                    'cpu_percent': psutil.cpu_percent(interval=1),
                    'memory_percent': psutil.virtual_memory().percent,
                    'disk_percent': psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent
                })
            except:
                # 如果 psutil 不可用，跳过资源信息
                pass
            
            return system_info
            
        except Exception as e:
            print(f"获取系统信息失败: {e}")
            return {}
    
    # 余额管理 API
    def get_user_balance(self, username: str) -> Dict[str, Any]:
        """获取用户余额"""
        try:
            from server.account_balance_manager import AccountBalanceManager
            
            balance_manager = AccountBalanceManager(self.db_manager)
            balance = balance_manager.get_user_balance(username)
            
            if balance is not None:
                return {
                    'success': True,
                    'balance': float(balance),
                    'username': username
                }
            else:
                return {
                    'success': False,
                    'message': f'用户 "{username}" 不存在'
                }
                
        except Exception as e:
            error_msg = f"获取用户余额失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def recharge_user_balance(self, username: str, amount: float, description: str = None) -> Dict[str, Any]:
        """为用户充值"""
        try:
            from server.account_balance_manager import AccountBalanceManager
            from decimal import Decimal
            
            balance_manager = AccountBalanceManager(self.db_manager)
            
            result = balance_manager.recharge_balance(
                username=username,
                amount=Decimal(str(amount)),
                operator='web_admin',
                description=description or '管理员充值',
                recharge_type='admin'
            )
            
            if result['success']:
                self.log_manager.log_info(f"通过Web GUI为用户 {username} 充值 {amount}")
            
            return result
            
        except Exception as e:
            error_msg = f"用户充值失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def get_user_balance_summary(self, username: str) -> Dict[str, Any]:
        """获取用户余额汇总"""
        try:
            from server.account_balance_manager import AccountBalanceManager
            
            balance_manager = AccountBalanceManager(self.db_manager)
            summary = balance_manager.get_balance_summary(username)
            
            return summary
            
        except Exception as e:
            error_msg = f"获取余额汇总失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def get_recharge_history(self, username: str, limit: int = 50) -> Dict[str, Any]:
        """获取充值历史"""
        try:
            from server.account_balance_manager import AccountBalanceManager
            
            balance_manager = AccountBalanceManager(self.db_manager)
            history = balance_manager.get_recharge_history(username, limit)
            
            return {
                'success': True,
                'data': history
            }
            
        except Exception as e:
            error_msg = f"获取充值历史失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': []
            }
    
    def get_consumption_history(self, username: str, limit: int = 50) -> Dict[str, Any]:
        """获取消费历史"""
        try:
            from server.account_balance_manager import AccountBalanceManager
            
            balance_manager = AccountBalanceManager(self.db_manager)
            history = balance_manager.get_consumption_history(username, limit)
            
            return {
                'success': True,
                'data': history
            }
            
        except Exception as e:
            error_msg = f"获取消费历史失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': []
            }
    
    def get_balance_statistics(self) -> Dict[str, Any]:
        """获取余额统计信息"""
        try:
            from server.account_balance_manager import AccountBalanceManager
            
            balance_manager = AccountBalanceManager(self.db_manager)
            stats = balance_manager.get_user_statistics()
            
            return {
                'success': True,
                'data': stats
            }
            
        except Exception as e:
            error_msg = f"获取余额统计失败: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': {}
            }
    
    def get_about_info(self) -> Dict[str, Any]:
        """获取关于信息"""
        return {
            'name': '网络验证工具',
            'version': '1.0.0',
            'description': '基于Python的网络认证系统',
            'author': 'Network Auth Tool Team',
            'license': 'MIT License',
            'website': 'https://github.com/network-auth-tool',
            'build_date': '2024-01-01'
        }
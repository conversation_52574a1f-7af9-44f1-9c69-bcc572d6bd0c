#!/usr/bin/env python3
"""
用户账户管理功能演示
展示注册、充值、余额管理等完整功能
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_manager import DatabaseManager
from server.account_balance_manager import AccountBalanceManager
from server.user_registration_manager import UserRegistrationManager
from client.recharge_dialog import RechargeDialog


class AccountManagementDemo:
    """用户账户管理演示应用"""
    
    def __init__(self):
        """初始化演示应用"""
        self.root = tk.Tk()
        self.root.title("用户账户管理系统演示")
        self.root.geometry("800x600")
        
        # 初始化数据库组件
        self.init_database()
        
        # 创建界面
        self.create_widgets()
        
        # 当前选中的用户
        self.current_user = None
    
    def init_database(self):
        """初始化数据库组件"""
        try:
            # 使用演示数据库
            demo_db_path = "data/demo_account.db"
            
            # 如果数据库存在，询问是否重新创建
            if os.path.exists(demo_db_path):
                if messagebox.askyesno("数据库存在", "发现现有演示数据库，是否重新创建？"):
                    os.remove(demo_db_path)
            
            self.db_manager = DatabaseManager(demo_db_path)
            self.balance_manager = AccountBalanceManager(self.db_manager)
            self.registration_manager = UserRegistrationManager(self.db_manager)
            
            print("✓ 数据库组件初始化完成")
            
        except Exception as e:
            messagebox.showerror("初始化失败", f"数据库初始化失败：{str(e)}")
            sys.exit(1)
    
    def create_widgets(self):
        """创建界面控件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="用户账户管理系统演示", 
                               font=('Arial', 18, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 用户注册标签页
        self.create_registration_tab(notebook)
        
        # 用户管理标签页
        self.create_user_management_tab(notebook)
        
        # 余额管理标签页
        self.create_balance_management_tab(notebook)
        
        # 统计信息标签页
        self.create_statistics_tab(notebook)
    
    def create_registration_tab(self, parent):
        """创建用户注册标签页"""
        reg_frame = ttk.Frame(parent, padding="20")
        parent.add(reg_frame, text="用户注册")
        
        # 注册表单
        ttk.Label(reg_frame, text="新用户注册", font=('Arial', 14, 'bold')).pack(pady=(0, 20))
        
        # 用户名
        ttk.Label(reg_frame, text="用户名:").pack(anchor=tk.W)
        self.reg_username = tk.StringVar()
        ttk.Entry(reg_frame, textvariable=self.reg_username, width=30).pack(pady=(0, 10), anchor=tk.W)
        
        # 密码
        ttk.Label(reg_frame, text="密码:").pack(anchor=tk.W)
        self.reg_password = tk.StringVar()
        ttk.Entry(reg_frame, textvariable=self.reg_password, show="*", width=30).pack(pady=(0, 10), anchor=tk.W)
        
        # 邮箱
        ttk.Label(reg_frame, text="邮箱 (可选):").pack(anchor=tk.W)
        self.reg_email = tk.StringVar()
        ttk.Entry(reg_frame, textvariable=self.reg_email, width=30).pack(pady=(0, 10), anchor=tk.W)
        
        # 初始余额
        ttk.Label(reg_frame, text="初始余额:").pack(anchor=tk.W)
        self.reg_balance = tk.StringVar(value="10.00")
        ttk.Entry(reg_frame, textvariable=self.reg_balance, width=30).pack(pady=(0, 20), anchor=tk.W)
        
        # 注册按钮
        ttk.Button(reg_frame, text="注册用户", command=self.register_user).pack(pady=10)
        
        # 注册结果显示
        self.reg_result = tk.Text(reg_frame, height=10, width=70)
        self.reg_result.pack(pady=(20, 0), fill=tk.BOTH, expand=True)
        
        # 滚动条
        reg_scrollbar = ttk.Scrollbar(reg_frame, orient=tk.VERTICAL, command=self.reg_result.yview)
        self.reg_result.configure(yscrollcommand=reg_scrollbar.set)
    
    def create_user_management_tab(self, parent):
        """创建用户管理标签页"""
        user_frame = ttk.Frame(parent, padding="20")
        parent.add(user_frame, text="用户管理")
        
        # 用户列表
        ttk.Label(user_frame, text="用户列表", font=('Arial', 14, 'bold')).pack(pady=(0, 10))
        
        # 用户列表框架
        list_frame = ttk.Frame(user_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 创建Treeview
        columns = ('用户名', '余额', '邮箱', '状态', '注册时间')
        self.user_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        for col in columns:
            self.user_tree.heading(col, text=col)
            self.user_tree.column(col, width=120)
        
        # 滚动条
        user_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.user_tree.yview)
        self.user_tree.configure(yscrollcommand=user_scrollbar.set)
        
        # 布局
        self.user_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        user_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.user_tree.bind('<<TreeviewSelect>>', self.on_user_select)
        
        # 操作按钮
        button_frame = ttk.Frame(user_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="刷新列表", command=self.refresh_user_list).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="查看详情", command=self.show_user_details).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="充值", command=self.recharge_user).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="删除用户", command=self.delete_user).pack(side=tk.LEFT)
        
        # 初始加载用户列表
        self.refresh_user_list()
    
    def create_balance_management_tab(self, parent):
        """创建余额管理标签页"""
        balance_frame = ttk.Frame(parent, padding="20")
        parent.add(balance_frame, text="余额管理")
        
        # 用户选择
        ttk.Label(balance_frame, text="余额管理", font=('Arial', 14, 'bold')).pack(pady=(0, 20))
        
        # 用户选择框架
        select_frame = ttk.Frame(balance_frame)
        select_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(select_frame, text="选择用户:").pack(side=tk.LEFT)
        self.balance_user = tk.StringVar()
        self.balance_user_combo = ttk.Combobox(select_frame, textvariable=self.balance_user, width=20)
        self.balance_user_combo.pack(side=tk.LEFT, padx=(10, 0))
        self.balance_user_combo.bind('<<ComboboxSelected>>', self.on_balance_user_select)
        
        ttk.Button(select_frame, text="刷新", command=self.refresh_balance_users).pack(side=tk.LEFT, padx=(10, 0))
        
        # 余额信息显示
        info_frame = ttk.LabelFrame(balance_frame, text="余额信息", padding="15")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.balance_info = tk.Text(info_frame, height=8, width=70)
        self.balance_info.pack(fill=tk.BOTH, expand=True)
        
        # 操作按钮
        action_frame = ttk.Frame(balance_frame)
        action_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(action_frame, text="充值", command=self.open_recharge_dialog).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="查看充值历史", command=self.show_recharge_history).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="查看消费历史", command=self.show_consumption_history).pack(side=tk.LEFT)
        
        # 初始化用户列表
        self.refresh_balance_users()
    
    def create_statistics_tab(self, parent):
        """创建统计信息标签页"""
        stats_frame = ttk.Frame(parent, padding="20")
        parent.add(stats_frame, text="统计信息")
        
        ttk.Label(stats_frame, text="系统统计", font=('Arial', 14, 'bold')).pack(pady=(0, 20))
        
        # 统计信息显示
        self.stats_text = tk.Text(stats_frame, height=20, width=80)
        self.stats_text.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 刷新按钮
        ttk.Button(stats_frame, text="刷新统计", command=self.refresh_statistics).pack()
        
        # 初始加载统计信息
        self.refresh_statistics()
    
    def register_user(self):
        """注册用户"""
        username = self.reg_username.get().strip()
        password = self.reg_password.get()
        email = self.reg_email.get().strip()
        balance_str = self.reg_balance.get().strip()
        
        if not username or not password:
            messagebox.showerror("错误", "用户名和密码不能为空")
            return
        
        try:
            initial_balance = float(balance_str) if balance_str else 0.0
        except ValueError:
            messagebox.showerror("错误", "初始余额格式无效")
            return
        
        try:
            # 注册用户
            result = self.registration_manager.register_user(
                username=username,
                password=password,
                email=email if email else None
            )
            
            if result['success']:
                # 如果设置了初始余额，进行充值
                if initial_balance > 0:
                    recharge_result = self.balance_manager.recharge_balance(
                        username=username,
                        amount=Decimal(str(initial_balance)),
                        description='注册初始余额',
                        recharge_type='system'
                    )
                    
                    if recharge_result['success']:
                        result['message'] += f"，初始余额 ¥{initial_balance:.2f} 已充值"
                
                # 显示结果
                self.reg_result.insert(tk.END, f"✓ {result['message']}\n")
                self.reg_result.see(tk.END)
                
                # 清空表单
                self.reg_username.set("")
                self.reg_password.set("")
                self.reg_email.set("")
                self.reg_balance.set("10.00")
                
                # 刷新用户列表
                self.refresh_user_list()
                self.refresh_balance_users()
                
            else:
                self.reg_result.insert(tk.END, f"✗ 注册失败: {result['message']}\n")
                self.reg_result.see(tk.END)
                
        except Exception as e:
            error_msg = f"注册过程中发生错误: {str(e)}"
            self.reg_result.insert(tk.END, f"✗ {error_msg}\n")
            self.reg_result.see(tk.END)
    
    def refresh_user_list(self):
        """刷新用户列表"""
        try:
            # 清空现有数据
            for item in self.user_tree.get_children():
                self.user_tree.delete(item)
            
            # 获取用户列表
            users = self.db_manager.get_all_users()
            
            for user in users:
                # 获取用户余额
                balance = self.balance_manager.get_user_balance(user['username'])
                balance_str = f"¥{float(balance):.2f}" if balance is not None else "N/A"
                
                self.user_tree.insert('', tk.END, values=(
                    user['username'],
                    balance_str,
                    user.get('email', ''),
                    user.get('status', 'active'),
                    user.get('registration_date', user.get('created_at', ''))
                ))
                
        except Exception as e:
            messagebox.showerror("错误", f"刷新用户列表失败: {str(e)}")
    
    def on_user_select(self, event):
        """用户选择事件"""
        selection = self.user_tree.selection()
        if selection:
            item = self.user_tree.item(selection[0])
            self.current_user = item['values'][0]  # 用户名
    
    def show_user_details(self):
        """显示用户详情"""
        if not self.current_user:
            messagebox.showwarning("提示", "请先选择一个用户")
            return
        
        try:
            # 获取用户信息
            user_info = self.db_manager.get_user_info(self.current_user)
            balance_summary = self.balance_manager.get_balance_summary(self.current_user)
            
            if user_info and balance_summary['success']:
                details = f"用户详细信息\\n\\n"
                details += f"用户名: {user_info['username']}\\n"
                details += f"邮箱: {user_info.get('email', '未设置')}\\n"
                details += f"状态: {user_info.get('status', 'active')}\\n"
                details += f"注册时间: {user_info.get('registration_date', '未知')}\\n"
                details += f"最后登录: {user_info.get('last_login', '从未登录')}\\n\\n"
                details += f"余额信息\\n"
                details += f"当前余额: ¥{balance_summary['current_balance']:.2f}\\n"
                details += f"总充值: ¥{balance_summary['total_recharged']:.2f}\\n"
                details += f"总消费: ¥{balance_summary['total_consumed']:.2f}\\n"
                details += f"最后充值: {balance_summary.get('last_recharge_time', '从未充值')}\\n"
                details += f"最后消费: {balance_summary.get('last_consumption_time', '从未消费')}"
                
                messagebox.showinfo(f"用户详情 - {self.current_user}", details)
            else:
                messagebox.showerror("错误", "获取用户详情失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"显示用户详情失败: {str(e)}")
    
    def recharge_user(self):
        """为选中用户充值"""
        if not self.current_user:
            messagebox.showwarning("提示", "请先选择一个用户")
            return
        
        self.open_recharge_dialog_for_user(self.current_user)
    
    def delete_user(self):
        """删除用户"""
        if not self.current_user:
            messagebox.showwarning("提示", "请先选择一个用户")
            return
        
        if messagebox.askyesno("确认删除", f"确定要删除用户 '{self.current_user}' 吗？\\n\\n此操作不可恢复！"):
            try:
                result = self.db_manager.delete_user(self.current_user)
                if result:
                    messagebox.showinfo("成功", f"用户 '{self.current_user}' 已删除")
                    self.current_user = None
                    self.refresh_user_list()
                    self.refresh_balance_users()
                else:
                    messagebox.showerror("失败", "删除用户失败")
            except Exception as e:
                messagebox.showerror("错误", f"删除用户失败: {str(e)}")
    
    def refresh_balance_users(self):
        """刷新余额管理用户列表"""
        try:
            users = self.db_manager.get_all_users()
            usernames = [user['username'] for user in users]
            self.balance_user_combo['values'] = usernames
            
            if usernames and not self.balance_user.get():
                self.balance_user.set(usernames[0])
                self.on_balance_user_select(None)
                
        except Exception as e:
            print(f"刷新用户列表失败: {e}")
    
    def on_balance_user_select(self, event):
        """余额用户选择事件"""
        username = self.balance_user.get()
        if username:
            self.update_balance_info(username)
    
    def update_balance_info(self, username):
        """更新余额信息显示"""
        try:
            summary = self.balance_manager.get_balance_summary(username)
            
            if summary['success']:
                info = f"用户: {username}\\n\\n"
                info += f"当前余额: ¥{summary['current_balance']:.2f}\\n"
                info += f"总充值金额: ¥{summary['total_recharged']:.2f}\\n"
                info += f"总消费金额: ¥{summary['total_consumed']:.2f}\\n"
                info += f"最后充值时间: {summary.get('last_recharge_time', '从未充值')}\\n"
                info += f"最后消费时间: {summary.get('last_consumption_time', '从未消费')}\\n"
                
                self.balance_info.delete(1.0, tk.END)
                self.balance_info.insert(1.0, info)
            else:
                self.balance_info.delete(1.0, tk.END)
                self.balance_info.insert(1.0, f"获取用户 {username} 余额信息失败")
                
        except Exception as e:
            self.balance_info.delete(1.0, tk.END)
            self.balance_info.insert(1.0, f"错误: {str(e)}")
    
    def open_recharge_dialog(self):
        """打开充值对话框"""
        username = self.balance_user.get()
        if not username:
            messagebox.showwarning("提示", "请先选择一个用户")
            return
        
        self.open_recharge_dialog_for_user(username)
    
    def open_recharge_dialog_for_user(self, username):
        """为指定用户打开充值对话框"""
        try:
            # 获取当前余额
            current_balance = self.balance_manager.get_user_balance(username)
            if current_balance is None:
                messagebox.showerror("错误", f"用户 '{username}' 不存在")
                return
            
            # 充值回调函数
            def recharge_callback(user, amount):
                return self.balance_manager.recharge_balance(
                    username=user,
                    amount=amount,
                    description='管理员充值',
                    recharge_type='admin'
                )
            
            # 显示充值对话框
            dialog = RechargeDialog(
                parent=self.root,
                username=username,
                current_balance=float(current_balance),
                recharge_callback=recharge_callback
            )
            
            result = dialog.show()
            
            if result:
                messagebox.showinfo("充值成功", f"用户 '{username}' 充值成功！\\n\\n"
                                  f"充值金额: ¥{result.get('recharge_amount', 0):.2f}\\n"
                                  f"新余额: ¥{result.get('new_balance', 0):.2f}")
                
                # 刷新显示
                self.refresh_user_list()
                self.update_balance_info(username)
                self.refresh_statistics()
                
        except Exception as e:
            messagebox.showerror("错误", f"打开充值对话框失败: {str(e)}")
    
    def show_recharge_history(self):
        """显示充值历史"""
        username = self.balance_user.get()
        if not username:
            messagebox.showwarning("提示", "请先选择一个用户")
            return
        
        try:
            history = self.balance_manager.get_recharge_history(username, 20)
            
            if history:
                history_text = f"用户 '{username}' 充值历史\\n\\n"
                for record in history:
                    history_text += f"{record['recharge_time']} - ¥{record['amount']:.2f} "
                    history_text += f"[{record['recharge_type']}] ({record['description']})\\n"
            else:
                history_text = f"用户 '{username}' 暂无充值记录"
            
            messagebox.showinfo("充值历史", history_text)
            
        except Exception as e:
            messagebox.showerror("错误", f"获取充值历史失败: {str(e)}")
    
    def show_consumption_history(self):
        """显示消费历史"""
        username = self.balance_user.get()
        if not username:
            messagebox.showwarning("提示", "请先选择一个用户")
            return
        
        try:
            history = self.balance_manager.get_consumption_history(username, 20)
            
            if history:
                history_text = f"用户 '{username}' 消费历史\\n\\n"
                for record in history:
                    history_text += f"{record['consumption_time']} - ¥{record['amount']:.2f} "
                    history_text += f"[{record['service_type']}] ({record['description']})\\n"
            else:
                history_text = f"用户 '{username}' 暂无消费记录"
            
            messagebox.showinfo("消费历史", history_text)
            
        except Exception as e:
            messagebox.showerror("错误", f"获取消费历史失败: {str(e)}")
    
    def refresh_statistics(self):
        """刷新统计信息"""
        try:
            # 获取用户统计
            user_stats = self.balance_manager.get_user_statistics()
            
            # 获取注册统计
            reg_stats = self.registration_manager.get_registration_statistics()
            
            stats_text = "=== 系统统计信息 ===\\n\\n"
            
            # 用户统计
            stats_text += "用户统计:\\n"
            stats_text += f"  总用户数: {user_stats.get('total_users', 0)}\\n"
            stats_text += f"  有余额用户: {user_stats.get('users_with_balance', 0)}\\n"
            stats_text += f"  平均余额: ¥{user_stats.get('average_balance', 0):.2f}\\n\\n"
            
            # 余额统计
            stats_text += "余额统计:\\n"
            stats_text += f"  系统总余额: ¥{user_stats.get('total_balance', 0):.2f}\\n"
            stats_text += f"  今日充值: ¥{user_stats.get('today_recharge', 0):.2f}\\n"
            stats_text += f"  今日消费: ¥{user_stats.get('today_consumption', 0):.2f}\\n\\n"
            
            # 注册统计
            stats_text += "注册统计:\\n"
            stats_text += f"  总注册用户: {reg_stats.get('total_users', 0)}\\n"
            stats_text += f"  活跃用户: {reg_stats.get('active_users', 0)}\\n"
            stats_text += f"  非活跃用户: {reg_stats.get('inactive_users', 0)}\\n\\n"
            
            # 按日期统计
            if reg_stats.get('registration_by_date'):
                stats_text += "每日注册统计:\\n"
                for date, count in reg_stats['registration_by_date'].items():
                    stats_text += f"  {date}: {count} 人\\n"
            
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
            
        except Exception as e:
            error_text = f"获取统计信息失败: {str(e)}"
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, error_text)
    
    def run(self):
        """运行演示应用"""
        print("启动用户账户管理系统演示...")
        self.root.mainloop()


def main():
    """主函数"""
    print("=" * 60)
    print("用户账户管理系统演示")
    print("=" * 60)
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    try:
        # 创建并运行演示应用
        demo = AccountManagementDemo()
        demo.run()
        
        print("演示应用已关闭")
        return 0
        
    except Exception as e:
        print(f"演示应用启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
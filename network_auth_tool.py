#!/usr/bin/env python3
"""
网络验证工具 - 主启动程序
整合所有模块，提供统一的启动入口
"""

import sys
import os
import argparse
import signal
import time
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.server_application import ServerApplication
from client.client_application import ClientApplication
from common.config_manager import ConfigManager
from common.log_manager import LogManager


class NetworkAuthTool:
    """网络验证工具主应用程序"""
    
    def __init__(self):
        """初始化主应用程序"""
        self.server_app = None
        self.client_app = None
        self.config_manager = None
        self.log_manager = None
        self.running = False
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在关闭...")
        self.shutdown()
        sys.exit(0)
    
    def initialize(self, config_file: str = "config.ini"):
        """
        初始化系统
        
        Args:
            config_file: 配置文件路径
        """
        try:
            print("正在初始化网络验证工具...")
            
            # 初始化配置管理器
            self.config_manager = ConfigManager(config_file)
            print("√ 配置管理器初始化完成")
            
            # 初始化日志管理器
            log_config = self.config_manager.get_section('logging')
            self.log_manager = LogManager(log_config)
            print("√ 日志管理器初始化完成")
            
            # 记录启动日志
            self.log_manager.log_info("网络验证工具正在启动")
            
            print("√ 系统初始化完成")
            return True
            
        except Exception as e:
            print(f"× 系统初始化失败: {e}")
            return False
    
    def start_server(self, config_file: str = "config.ini"):
        """
        启动服务器
        
        Args:
            config_file: 配置文件路径
        """
        try:
            if not self.initialize(config_file):
                return False
            
            print("正在启动服务器...")
            
            # 创建服务器应用程序
            self.server_app = ServerApplication(config_file)
            
            # 启动服务器
            result = self.server_app.start()
            
            if result['success']:
                print(f"√ 服务器启动成功: {result['message']}")
                self.running = True
                
                # 显示服务器信息
                self._show_server_info()
                
                # 保持运行
                self._keep_running()
                
                return True
            else:
                print(f"× 服务器启动失败: {result['message']}")
                return False
                
        except Exception as e:
            print(f"× 启动服务器时发生错误: {e}")
            return False
    
    def start_client(self, config_file: str = "client_config.ini"):
        """
        启动客户端
        
        Args:
            config_file: 配置文件路径
        """
        try:
            if not self.initialize(config_file):
                return False
            
            print("正在启动客户端...")
            
            # 创建客户端应用程序
            self.client_app = ClientApplication()
            
            # 启动客户端GUI
            self.client_app.run_gui()
            print("√ 客户端启动成功")
            return True
                
        except Exception as e:
            print(f"× 启动客户端时发生错误: {e}")
            return False
    
    def start_web_gui(self):
        """启动Web管理界面"""
        try:
            print("正在启动Web管理界面...")
            
            # 首先尝试使用pywebview
            try:
                import webview
                print("✓ pywebview 已安装，使用桌面应用模式")
                
                from server.web_gui.main import main as web_gui_main
                return web_gui_main() == 0
                
            except ImportError:
                print("⚠ 未安装 pywebview，使用浏览器模式")
                return self._start_simple_web_gui()
            except Exception as e:
                print(f"⚠ pywebview 启动失败: {e}")
                print("尝试使用浏览器模式...")
                return self._start_simple_web_gui()
            
        except Exception as e:
            print(f"× 启动Web GUI失败: {e}")
            return False
    
    def _start_simple_web_gui(self):
        """启动简单的Web GUI（浏览器模式）"""
        try:
            from server.web_gui.simple_server import start_simple_web_gui
            return start_simple_web_gui()
        except Exception as e:
            print(f"× 启动简单Web GUI失败: {e}")
            print("\n故障排除建议:")
            print("1. 安装pywebview: pip install pywebview")
            print("2. 检查端口8080是否被占用")
            print("3. 运行测试: python test_webgui_startup.py")
            return False
    
    def run_tests(self):
        """运行系统测试"""
        try:
            print("正在运行系统测试...")
            
            # 运行完整认证流程测试
            from test_complete_auth_flow import AuthFlowTester
            tester = AuthFlowTester()
            success = tester.run_tests()
            
            return success
            
        except Exception as e:
            print(f"× 运行测试失败: {e}")
            return False
    
    def _show_server_info(self):
        """显示服务器信息"""
        try:
            if self.server_app:
                info = self.server_app.get_server_info()
                stats = self.server_app.get_server_statistics()
                
                print("\n" + "=" * 50)
                print("服务器信息")
                print("=" * 50)
                print(f"监听地址: {info.get('host', 'N/A')}:{info.get('port', 'N/A')}")
                print(f"最大连接数: {info.get('max_connections', 'N/A')}")
                print(f"服务器版本: {info.get('version', 'N/A')}")
                print(f"启动时间: {info.get('start_time', 'N/A')}")
                print(f"总连接数: {stats.get('total_connections', 0)}")
                print(f"活跃会话: {stats.get('active_sessions', 0)}")
                print("=" * 50)
                print("服务器正在运行，按 Ctrl+C 停止")
                
        except Exception as e:
            print(f"获取服务器信息失败: {e}")
    
    def _keep_running(self):
        """保持服务器运行"""
        try:
            while self.running:
                time.sleep(1)
                
                # 定期检查服务器状态
                if self.server_app and not self.server_app.is_server_running():
                    print("检测到服务器已停止")
                    break
                    
        except KeyboardInterrupt:
            print("\n收到中断信号，正在关闭服务器...")
        except Exception as e:
            print(f"服务器运行时发生错误: {e}")
    
    def shutdown(self):
        """关闭系统"""
        try:
            self.running = False
            
            if self.server_app:
                print("正在关闭服务器...")
                result = self.server_app.shutdown()
                if result['success']:
                    print("√ 服务器已关闭")
                else:
                    print(f"× 服务器关闭失败: {result['message']}")
            
            if self.client_app:
                print("正在关闭客户端...")
                self.client_app.shutdown()
                print("√ 客户端已关闭")
            
            if self.log_manager:
                self.log_manager.log_info("网络验证工具正在关闭")
                self.log_manager.shutdown()
            
            print("√ 系统已关闭")
            
        except Exception as e:
            print(f"关闭系统时发生错误: {e}")
    
    def show_status(self):
        """显示系统状态"""
        try:
            print("\n" + "=" * 60)
            print("网络验证工具系统状态")
            print("=" * 60)
            
            # 检查配置文件
            config_files = ["config.ini", "client_config.ini"]
            for config_file in config_files:
                if os.path.exists(config_file):
                    print(f"√ 配置文件: {config_file}")
                else:
                    print(f"× 配置文件缺失: {config_file}")
            
            # 检查数据目录
            data_dirs = ["data", "logs"]
            for data_dir in data_dirs:
                if os.path.exists(data_dir):
                    print(f"√ 数据目录: {data_dir}")
                else:
                    print(f"× 数据目录缺失: {data_dir}")
            
            # 检查依赖
            dependencies = [
                ("sqlite3", "SQLite数据库"),
                ("tkinter", "GUI界面"),
                ("threading", "多线程支持"),
                ("socket", "网络通信"),
                ("json", "JSON处理"),
                ("hashlib", "密码加密"),
                ("secrets", "安全随机数"),
                ("datetime", "时间处理")
            ]
            
            print("\n依赖检查:")
            for module, desc in dependencies:
                try:
                    __import__(module)
                    print(f"√ {desc}: {module}")
                except ImportError:
                    print(f"× {desc}: {module} (缺失)")
            
            # 可选依赖
            optional_deps = [
                ("webview", "Web管理界面"),
                ("psutil", "系统监控")
            ]
            
            print("\n可选依赖:")
            for module, desc in optional_deps:
                try:
                    __import__(module)
                    print(f"√ {desc}: {module}")
                except ImportError:
                    print(f"- {desc}: {module} (未安装)")
            
            print("=" * 60)
            
        except Exception as e:
            print(f"获取系统状态失败: {e}")


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="网络验证工具 - 基于Python的网络认证系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s server                    # 启动服务器
  %(prog)s client                    # 启动客户端
  %(prog)s webgui                    # 启动Web管理界面
  %(prog)s test                      # 运行系统测试
  %(prog)s status                    # 显示系统状态
  %(prog)s server --config custom.ini  # 使用自定义配置启动服务器
        """
    )
    
    parser.add_argument(
        'mode',
        choices=['server', 'client', 'webgui', 'test', 'status'],
        help='运行模式'
    )
    
    parser.add_argument(
        '--config',
        default='config.ini',
        help='配置文件路径 (默认: config.ini)'
    )
    
    parser.add_argument(
        '--client-config',
        default='client_config.ini',
        help='客户端配置文件路径 (默认: client_config.ini)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='网络验证工具 v1.0.0'
    )
    
    return parser


def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = create_parser()
        args = parser.parse_args()
        
        # 创建主应用程序
        app = NetworkAuthTool()
        
        # 根据模式执行相应操作
        if args.mode == 'server':
            success = app.start_server(args.config)
        elif args.mode == 'client':
            success = app.start_client(args.client_config)
        elif args.mode == 'webgui':
            success = app.start_web_gui()
        elif args.mode == 'test':
            success = app.run_tests()
        elif args.mode == 'status':
            app.show_status()
            success = True
        else:
            parser.print_help()
            success = False
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 0
    except Exception as e:
        print(f"程序运行失败: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
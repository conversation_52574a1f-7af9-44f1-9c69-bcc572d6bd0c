#!/usr/bin/env python3
"""
调试数据库问题
"""

import os
import sys
import sqlite3

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_migrations import DatabaseMigration


def debug_database():
    """调试数据库问题"""
    db_path = "data/debug.db"
    
    # 删除测试数据库
    if os.path.exists(db_path):
        os.remove(db_path)
    
    # 运行迁移
    migration = DatabaseMigration(db_path)
    success = migration.run_migrations()
    
    if not success:
        print("迁移失败")
        return
    
    # 直接测试数据库操作
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 查看表结构
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='users'")
            result = cursor.fetchone()
            if result:
                print("用户表结构:")
                print(result[0])
            else:
                print("用户表不存在")
                return
            
            # 尝试插入用户
            print("\n尝试插入用户...")
            cursor.execute("""
                INSERT INTO users (username, password_hash, email, balance, 
                                 registration_date, status, created_at, updated_at) 
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, 'active', 
                       CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """, ("testuser", "hash123", "<EMAIL>", 0.0))
            
            conn.commit()
            print("✓ 用户插入成功")
            
            # 查询用户
            cursor.execute("SELECT * FROM users WHERE username = ?", ("testuser",))
            user = cursor.fetchone()
            if user:
                print(f"✓ 用户查询成功: {user}")
            else:
                print("✗ 用户查询失败")
            
    except Exception as e:
        print(f"数据库操作失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_database()
#!/usr/bin/env python3
"""
充值卡密生成器启动脚本
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.card_generator_gui import CardGeneratorGUI


def main():
    """主函数"""
    print("启动充值卡密生成器...")
    
    try:
        # 确保数据目录存在
        os.makedirs("data", exist_ok=True)
        
        # 创建并运行GUI
        app = CardGeneratorGUI()
        app.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
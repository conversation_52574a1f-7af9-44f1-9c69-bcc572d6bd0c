# 设计文档

## 概述

网络验证工具采用客户端-服务器架构，使用Python开发。服务器端提供用户管理界面和认证服务，客户端提供简洁的认证测试界面。系统使用SQLite数据库存储用户信息，支持多线程并发处理，确保多用户同时访问的稳定性。

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "客户端"
        C1[客户端GUI] --> C2[网络通信模块]
        C2 --> C3[认证处理器]
    end
    
    subgraph "服务器端"
        S1[服务器GUI] --> S2[服务器管理器]
        S2 --> S3[Socket监听器]
        S3 --> S4[线程池管理器]
        S4 --> S5[认证处理器]
        S5 --> S6[数据库管理器]
        S6 --> S7[SQLite数据库]
        S2 --> S8[配置管理器]
        S2 --> S9[日志管理器]
    end
    
    C2 -.->|TCP连接| S3
    S5 -.->|查询用户| S6
```

### 技术栈选择

- **GUI框架**: Tkinter (Python内置，无需额外依赖)
- **数据库**: SQLite3 (轻量级，适合单机部署)
- **网络通信**: Socket (TCP协议)
- **多线程**: threading模块
- **密码加密**: hashlib (SHA-256 + 盐值)
- **配置管理**: configparser
- **日志系统**: logging模块

## 组件和接口

### 服务器端组件

#### 1. 服务器GUI管理器 (ServerGUI)
```python
class ServerGUI:
    def __init__(self)
    def start_server(self)
    def stop_server(self)
    def add_user(self, username, password)
    def delete_user(self, username)
    def update_connection_status(self, count)
    def show_logs(self)
```

#### 2. Socket监听器 (SocketListener)
```python
class SocketListener:
    def __init__(self, host, port)
    def start_listening(self)
    def stop_listening(self)
    def accept_connections(self)
    def handle_client(self, client_socket, address)
```

#### 3. 线程池管理器 (ThreadPoolManager)
```python
class ThreadPoolManager:
    def __init__(self, max_threads=50)
    def submit_task(self, func, *args)
    def shutdown(self)
    def get_active_count(self)
```

#### 4. 认证处理器 (AuthenticationHandler)
```python
class AuthenticationHandler:
    def __init__(self, db_manager)
    def authenticate_user(self, username, password)
    def hash_password(self, password, salt)
    def generate_session_token(self)
    def validate_session(self, token)
```

#### 5. 数据库管理器 (DatabaseManager)
```python
class DatabaseManager:
    def __init__(self, db_path)
    def create_tables(self)
    def add_user(self, username, password_hash, salt)
    def get_user(self, username)
    def delete_user(self, username)
    def update_user(self, username, **kwargs)
```

### 客户端组件

#### 1. 客户端GUI (ClientGUI)
```python
class ClientGUI:
    def __init__(self)
    def show_login_form(self)
    def connect_to_server(self, host, port)
    def send_credentials(self, username, password)
    def show_result(self, success, message)
```

#### 2. 网络客户端 (NetworkClient)
```python
class NetworkClient:
    def __init__(self)
    def connect(self, host, port)
    def send_auth_request(self, username, password)
    def receive_response(self)
    def disconnect(self)
```

## 数据模型

### 数据库表结构

#### users表
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(64) NOT NULL,
    salt VARCHAR(32) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    failed_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP NULL
);
```

#### sessions表
```sql
CREATE TABLE sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    session_token VARCHAR(64) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    client_ip VARCHAR(45),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

#### logs表
```sql
CREATE TABLE logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    level VARCHAR(10) NOT NULL,
    message TEXT NOT NULL,
    client_ip VARCHAR(45),
    username VARCHAR(50)
);
```

### 通信协议

#### 认证请求格式 (JSON)
```json
{
    "type": "auth_request",
    "username": "user123",
    "password": "password123",
    "client_info": {
        "version": "1.0",
        "platform": "Windows"
    }
}
```

#### 认证响应格式 (JSON)
```json
{
    "type": "auth_response",
    "success": true,
    "message": "Authentication successful",
    "session_token": "abc123...",
    "user_info": {
        "username": "user123",
        "last_login": "2024-01-01 12:00:00"
    }
}
```

## 错误处理

### 错误类型定义

```python
class NetworkAuthError(Exception):
    pass

class AuthenticationError(NetworkAuthError):
    pass

class DatabaseError(NetworkAuthError):
    pass

class ConfigurationError(NetworkAuthError):
    pass

class NetworkError(NetworkAuthError):
    pass
```

### 错误处理策略

1. **网络错误**: 自动重试机制，最多重试3次
2. **认证错误**: 记录失败尝试，超过5次锁定账户30分钟
3. **数据库错误**: 事务回滚，记录详细错误日志
4. **配置错误**: 使用默认配置，提示用户检查配置文件
5. **系统错误**: 优雅关闭，保存当前状态

### 日志级别

- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息（用户登录、连接建立等）
- **WARNING**: 警告信息（认证失败、配置问题等）
- **ERROR**: 错误信息（数据库错误、网络异常等）
- **CRITICAL**: 严重错误（系统崩溃、数据损坏等）

## 测试策略

### 单元测试

1. **数据库操作测试**
   - 用户CRUD操作
   - 密码加密验证
   - 会话管理

2. **认证逻辑测试**
   - 正确凭据认证
   - 错误凭据处理
   - 账户锁定机制

3. **网络通信测试**
   - Socket连接建立
   - 数据传输完整性
   - 异常断开处理

### 集成测试

1. **客户端-服务器通信测试**
   - 完整认证流程
   - 并发连接测试
   - 网络异常恢复

2. **GUI功能测试**
   - 界面响应性
   - 用户操作流程
   - 错误信息显示

### 性能测试

1. **并发性能测试**
   - 最大并发连接数
   - 响应时间测试
   - 内存使用监控

2. **数据库性能测试**
   - 查询响应时间
   - 大量用户数据处理
   - 数据库锁定测试

## 安全考虑

### 密码安全

1. **密码加密**: 使用SHA-256 + 随机盐值
2. **盐值生成**: 每个用户使用唯一的32字节随机盐值
3. **密码策略**: 最少8位，包含字母数字

### 网络安全

1. **数据传输**: JSON格式，考虑后续添加TLS加密
2. **会话管理**: 基于令牌的会话，设置过期时间
3. **IP限制**: 记录客户端IP，支持IP白名单功能

### 系统安全

1. **输入验证**: 所有用户输入进行严格验证
2. **SQL注入防护**: 使用参数化查询
3. **日志安全**: 敏感信息不记录到日志文件

## 配置管理

### 配置文件结构 (config.ini)

```ini
[server]
host = 0.0.0.0
port = 8888
max_connections = 50
session_timeout = 3600

[database]
path = data/auth.db
backup_interval = 24

[security]
max_failed_attempts = 5
lockout_duration = 1800
password_min_length = 8

[logging]
level = INFO
file_path = logs/server.log
max_file_size = 10485760
backup_count = 5
```

### 默认配置

系统提供合理的默认配置，确保在配置文件缺失或损坏时能正常运行。所有配置项都有对应的默认值，并在启动时进行验证。
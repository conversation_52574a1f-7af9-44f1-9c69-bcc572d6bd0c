"""
客户端GUI界面模块
使用Tkinter创建客户端登录窗口和服务器连接配置界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from typing import Dict, Any, Optional, Callable
from datetime import datetime

from client.network_client import NetworkClient
from client.card_recharge_dialog import CardRechargeDialog
from common.constants import DEFAULT_CONFIG


class UserRegistrationDialog:
    """用户注册对话框"""
    
    def __init__(self, parent, network_client):
        self.parent = parent
        self.network_client = network_client
        self.dialog = None
        
    def show(self):
        """显示注册对话框"""
        # 创建顶级窗口
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("用户注册")
        self.dialog.geometry("400x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        self.create_widgets()
        self.setup_layout()
        
        # 绑定事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_cancel)
        
    def create_widgets(self):
        """创建对话框组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.dialog, padding="20")
        
        # 标题
        self.title_label = ttk.Label(
            self.main_frame, 
            text="创建新账户", 
            font=('Arial', 14, 'bold')
        )
        
        # 用户名
        ttk.Label(self.main_frame, text="用户名:").grid(row=1, column=0, sticky='w', pady=(10, 2))
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(self.main_frame, textvariable=self.username_var, width=30)
        
        # 用户名提示
        self.username_hint = ttk.Label(
            self.main_frame, 
            text="3-20个字符，只能包含字母、数字、下划线和连字符", 
            font=('Arial', 8),
            foreground='gray'
        )
        
        # 密码
        ttk.Label(self.main_frame, text="密码:").grid(row=3, column=0, sticky='w', pady=(10, 2))
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(self.main_frame, textvariable=self.password_var, show="*", width=30)
        
        # 密码提示
        self.password_hint = ttk.Label(
            self.main_frame, 
            text="至少6个字符，包含字母和数字", 
            font=('Arial', 8),
            foreground='gray'
        )
        
        # 确认密码
        ttk.Label(self.main_frame, text="确认密码:").grid(row=5, column=0, sticky='w', pady=(10, 2))
        self.confirm_password_var = tk.StringVar()
        self.confirm_password_entry = ttk.Entry(self.main_frame, textvariable=self.confirm_password_var, show="*", width=30)
        
        # 邮箱
        ttk.Label(self.main_frame, text="邮箱 (可选):").grid(row=7, column=0, sticky='w', pady=(10, 2))
        self.email_var = tk.StringVar()
        self.email_entry = ttk.Entry(self.main_frame, textvariable=self.email_var, width=30)
        
        # 邮箱提示
        self.email_hint = ttk.Label(
            self.main_frame, 
            text="用于密码重置和通知（可选）", 
            font=('Arial', 8),
            foreground='gray'
        )
        
        # 密码强度指示器
        self.strength_frame = ttk.Frame(self.main_frame)
        self.strength_label = ttk.Label(self.strength_frame, text="密码强度:")
        self.strength_bar = ttk.Progressbar(
            self.strength_frame, 
            length=200, 
            mode='determinate'
        )
        self.strength_text = ttk.Label(self.strength_frame, text="", font=('Arial', 8))
        
        # 按钮框架
        self.button_frame = ttk.Frame(self.main_frame)
        
        # 注册按钮
        self.register_button = ttk.Button(
            self.button_frame, 
            text="注册", 
            command=self.register_user
        )
        
        # 取消按钮
        self.cancel_button = ttk.Button(
            self.button_frame, 
            text="取消", 
            command=self.on_cancel
        )
        
        # 绑定事件
        self.password_var.trace('w', self.on_password_change)
        self.confirm_password_entry.bind('<Return>', lambda e: self.register_user())
        
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill='both', expand=True)
        
        # 标题
        self.title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 用户名
        self.username_entry.grid(row=1, column=1, sticky='ew', pady=2)
        self.username_hint.grid(row=2, column=1, sticky='w', pady=(0, 5))
        
        # 密码
        self.password_entry.grid(row=3, column=1, sticky='ew', pady=2)
        self.password_hint.grid(row=4, column=1, sticky='w', pady=(0, 5))
        
        # 确认密码
        self.confirm_password_entry.grid(row=5, column=1, sticky='ew', pady=2)
        
        # 邮箱
        self.email_entry.grid(row=7, column=1, sticky='ew', pady=2)
        self.email_hint.grid(row=8, column=1, sticky='w', pady=(0, 10))
        
        # 密码强度指示器
        self.strength_frame.grid(row=9, column=0, columnspan=2, sticky='ew', pady=(10, 20))
        self.strength_label.pack(side='left')
        self.strength_bar.pack(side='left', padx=(10, 10))
        self.strength_text.pack(side='left')
        
        # 按钮
        self.button_frame.grid(row=10, column=0, columnspan=2, pady=(10, 0))
        self.register_button.pack(side='right', padx=(10, 0))
        self.cancel_button.pack(side='right')
        
        # 配置列权重
        self.main_frame.columnconfigure(1, weight=1)
        
    def on_password_change(self, *args):
        """密码变化时更新强度指示器"""
        password = self.password_var.get()
        strength = self.calculate_password_strength(password)
        
        # 更新进度条
        self.strength_bar['value'] = strength['score']
        
        # 更新颜色和文本
        if strength['score'] < 30:
            color = 'red'
            text = '弱'
        elif strength['score'] < 60:
            color = 'orange'
            text = '中等'
        elif strength['score'] < 80:
            color = 'yellow'
            text = '强'
        else:
            color = 'green'
            text = '很强'
        
        self.strength_text.config(text=text, foreground=color)
        
    def calculate_password_strength(self, password):
        """计算密码强度"""
        score = 0
        feedback = []
        
        if len(password) >= 6:
            score += 20
        else:
            feedback.append("至少6个字符")
            
        if len(password) >= 8:
            score += 10
            
        if any(c.islower() for c in password):
            score += 15
        else:
            feedback.append("包含小写字母")
            
        if any(c.isupper() for c in password):
            score += 15
        else:
            feedback.append("包含大写字母")
            
        if any(c.isdigit() for c in password):
            score += 15
        else:
            feedback.append("包含数字")
            
        if any(c in "!@#$%^&*(),.?\":{}|<>" for c in password):
            score += 15
            
        if len(password) >= 12:
            score += 10
            
        return {
            'score': min(score, 100),
            'feedback': feedback
        }
        
    def validate_input(self):
        """验证输入数据"""
        errors = []
        
        username = self.username_var.get().strip()
        password = self.password_var.get()
        confirm_password = self.confirm_password_var.get()
        email = self.email_var.get().strip()
        
        # 验证用户名
        if not username:
            errors.append("请输入用户名")
        elif len(username) < 3:
            errors.append("用户名至少3个字符")
        elif len(username) > 20:
            errors.append("用户名不能超过20个字符")
        elif not username.replace('_', '').replace('-', '').isalnum():
            errors.append("用户名只能包含字母、数字、下划线和连字符")
        elif username[0].isdigit():
            errors.append("用户名不能以数字开头")
            
        # 验证密码
        if not password:
            errors.append("请输入密码")
        elif len(password) < 6:
            errors.append("密码至少6个字符")
        elif not any(c.isalpha() for c in password):
            errors.append("密码必须包含字母")
        elif not any(c.isdigit() for c in password):
            errors.append("密码必须包含数字")
            
        # 验证确认密码
        if password != confirm_password:
            errors.append("两次输入的密码不一致")
            
        # 验证邮箱（如果提供）
        if email:
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                errors.append("邮箱格式不正确")
                
        return errors
        
    def register_user(self):
        """注册用户"""
        # 验证输入
        errors = self.validate_input()
        if errors:
            messagebox.showerror("输入错误", "\n".join(errors))
            return
            
        username = self.username_var.get().strip()
        password = self.password_var.get()
        email = self.email_var.get().strip() or None
        
        # 禁用按钮
        self.register_button.config(state='disabled', text="注册中...")
        
        # 在新线程中注册
        def register_thread():
            try:
                result = self.network_client.register_user(username, password, email)
                
                # 在主线程中处理结果
                self.dialog.after(0, lambda: self.on_register_result(result))
                
            except Exception as e:
                self.dialog.after(0, lambda: self.on_register_result({
                    'success': False,
                    'message': f'注册时发生错误: {str(e)}'
                }))
        
        threading.Thread(target=register_thread, daemon=True).start()
        
    def on_register_result(self, result):
        """处理注册结果"""
        # 恢复按钮
        self.register_button.config(state='normal', text="注册")
        
        if result.get('success', False):
            messagebox.showinfo(
                "注册成功", 
                f"用户 '{result.get('username', '')}' 注册成功！\n请使用新账户登录。"
            )
            self.dialog.destroy()
        else:
            error_msg = result.get('message', '注册失败')
            
            # 显示详细错误信息
            if 'errors' in result:
                error_msg += "\n\n详细错误:\n" + "\n".join(result['errors'])
                
            messagebox.showerror("注册失败", error_msg)
            
    def on_cancel(self):
        """取消注册"""
        self.dialog.destroy()


class ClientGUI:
    """客户端图形用户界面"""
    
    def __init__(self, client_app=None):
        """初始化客户端GUI"""
        self.root = tk.Tk()
        self.root.title("网络验证工具 - 客户端")
        self.root.geometry("500x600")
        self.root.resizable(True, True)
        
        # 使用传入的客户端应用程序或创建新的网络客户端
        if client_app:
            self.client_app = client_app
            self.network_client = client_app.network_client
        else:
            self.network_client = NetworkClient()
            self.client_app = self.network_client
        
        # 连接状态
        self.is_connected = False
        self.is_authenticated = False
        self.connection_info = {}
        
        # GUI组件
        self.setup_styles()
        self.create_widgets()
        self.setup_layout()
        
        # 绑定事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 状态更新定时器
        self.update_status_timer()
    
    def setup_styles(self):
        """设置GUI样式"""
        style = ttk.Style()
        
        # 配置样式
        style.configure('Title.TLabel', font=('Arial', 14, 'bold'))
        style.configure('Status.TLabel', font=('Arial', 10))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # 标题
        self.title_label = ttk.Label(
            self.main_frame, 
            text="网络验证客户端", 
            style='Title.TLabel'
        )
        
        # 服务器连接配置框架
        self.connection_frame = ttk.LabelFrame(
            self.main_frame, 
            text="服务器连接配置", 
            padding="10"
        )
        
        # 服务器地址
        ttk.Label(self.connection_frame, text="服务器地址:").grid(row=0, column=0, sticky='w', pady=2)
        self.host_var = tk.StringVar(value="127.0.0.1")
        self.host_entry = ttk.Entry(self.connection_frame, textvariable=self.host_var, width=20)
        
        # 服务器端口
        ttk.Label(self.connection_frame, text="端口:").grid(row=1, column=0, sticky='w', pady=2)
        self.port_var = tk.StringVar(value=str(DEFAULT_CONFIG['server']['port']))
        self.port_entry = ttk.Entry(self.connection_frame, textvariable=self.port_var, width=20)
        
        # 连接按钮
        self.connect_button = ttk.Button(
            self.connection_frame, 
            text="连接", 
            command=self.toggle_connection
        )
        
        # 连接状态
        self.connection_status_var = tk.StringVar(value="未连接")
        self.connection_status_label = ttk.Label(
            self.connection_frame, 
            textvariable=self.connection_status_var,
            style='Status.TLabel'
        )
        
        # 认证框架
        self.auth_frame = ttk.LabelFrame(
            self.main_frame, 
            text="用户认证", 
            padding="10"
        )
        
        # 用户名
        ttk.Label(self.auth_frame, text="用户名:").grid(row=0, column=0, sticky='w', pady=2)
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(self.auth_frame, textvariable=self.username_var, width=25)
        
        # 密码
        ttk.Label(self.auth_frame, text="密码:").grid(row=1, column=0, sticky='w', pady=2)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(
            self.auth_frame, 
            textvariable=self.password_var, 
            show="*", 
            width=25
        )
        
        # 认证按钮
        self.auth_button = ttk.Button(
            self.auth_frame, 
            text="登录", 
            command=self.authenticate,
            state='disabled'
        )
        
        # 注册按钮
        self.register_button = ttk.Button(
            self.auth_frame, 
            text="注册", 
            command=self.show_register_dialog,
            state='disabled'
        )
        
        # 登出按钮
        self.logout_button = ttk.Button(
            self.auth_frame, 
            text="登出", 
            command=self.logout,
            state='disabled'
        )
        
        # 认证状态
        self.auth_status_var = tk.StringVar(value="未认证")
        self.auth_status_label = ttk.Label(
            self.auth_frame, 
            textvariable=self.auth_status_var,
            style='Status.TLabel'
        )
        
        # 用户信息框架
        self.user_info_frame = ttk.LabelFrame(
            self.main_frame, 
            text="用户信息", 
            padding="10"
        )
        
        # 用户信息显示区域
        info_display_frame = ttk.Frame(self.user_info_frame)
        
        # 用户名显示
        ttk.Label(info_display_frame, text="用户名:", font=('Arial', 9, 'bold')).grid(row=0, column=0, sticky='w', pady=2)
        self.display_username_var = tk.StringVar(value="未登录")
        ttk.Label(info_display_frame, textvariable=self.display_username_var, font=('Arial', 9)).grid(row=0, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 剩余时间显示
        ttk.Label(info_display_frame, text="剩余时间:", font=('Arial', 9, 'bold')).grid(row=1, column=0, sticky='w', pady=2)
        self.remaining_time_var = tk.StringVar(value="未知")
        self.remaining_time_label = ttk.Label(info_display_frame, textvariable=self.remaining_time_var, font=('Arial', 9, 'bold'))
        self.remaining_time_label.grid(row=1, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 账户状态显示
        ttk.Label(info_display_frame, text="账户状态:", font=('Arial', 9, 'bold')).grid(row=2, column=0, sticky='w', pady=2)
        self.account_status_var = tk.StringVar(value="未知")
        self.account_status_label = ttk.Label(info_display_frame, textvariable=self.account_status_var, font=('Arial', 9))
        self.account_status_label.grid(row=2, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 充值按钮
        self.recharge_button = ttk.Button(
            info_display_frame, 
            text="卡密充值", 
            command=self.show_recharge_dialog,
            state='disabled'
        )
        self.recharge_button.grid(row=3, column=0, columnspan=2, pady=(10, 0), sticky='ew')
        
        # 刷新时间按钮
        self.refresh_time_button = ttk.Button(
            info_display_frame, 
            text="刷新时间", 
            command=self.refresh_user_time,
            state='disabled'
        )
        self.refresh_time_button.grid(row=4, column=0, columnspan=2, pady=(5, 0), sticky='ew')
        
        info_display_frame.pack(fill='x')
        info_display_frame.columnconfigure(1, weight=1)
        
        # 操作按钮框架
        self.action_frame = ttk.Frame(self.main_frame)
        
        # 心跳测试按钮
        self.heartbeat_button = ttk.Button(
            self.action_frame, 
            text="心跳测试", 
            command=self.send_heartbeat,
            state='disabled'
        )
        
        # 清除日志按钮
        self.clear_log_button = ttk.Button(
            self.action_frame, 
            text="清除日志", 
            command=self.clear_log
        )
        
        # 日志框架
        self.log_frame = ttk.LabelFrame(
            self.main_frame, 
            text="操作日志", 
            padding="10"
        )
        
        # 日志显示
        self.log_text = scrolledtext.ScrolledText(
            self.log_frame, 
            height=8, 
            width=60,
            state='disabled'
        )
    
    def setup_layout(self):
        """设置布局"""
        # 主框架
        self.main_frame.pack(fill='both', expand=True)
        
        # 标题
        self.title_label.pack(pady=(0, 10))
        
        # 连接配置框架
        self.connection_frame.pack(fill='x', pady=(0, 10))
        
        # 连接配置布局
        self.host_entry.grid(row=0, column=1, padx=(10, 0), pady=2, sticky='ew')
        self.port_entry.grid(row=1, column=1, padx=(10, 0), pady=2, sticky='ew')
        self.connect_button.grid(row=0, column=2, rowspan=2, padx=(10, 0), pady=2)
        self.connection_status_label.grid(row=2, column=0, columnspan=3, pady=(10, 0))
        
        # 配置列权重
        self.connection_frame.columnconfigure(1, weight=1)
        
        # 认证框架
        self.auth_frame.pack(fill='x', pady=(0, 10))
        
        # 认证布局
        self.username_entry.grid(row=0, column=1, padx=(10, 0), pady=2, sticky='ew')
        self.password_entry.grid(row=1, column=1, padx=(10, 0), pady=2, sticky='ew')
        self.auth_button.grid(row=0, column=2, padx=(10, 0), pady=2)
        self.register_button.grid(row=0, column=3, padx=(5, 0), pady=2)
        self.logout_button.grid(row=1, column=2, columnspan=2, padx=(10, 0), pady=2, sticky='ew')
        self.auth_status_label.grid(row=3, column=0, columnspan=4, pady=(10, 0))
        
        # 配置列权重
        self.auth_frame.columnconfigure(1, weight=1)
        
        # 用户信息框架
        self.user_info_frame.pack(fill='x', pady=(0, 10))
        
        # 操作按钮框架
        self.action_frame.pack(fill='x', pady=(0, 10))
        self.heartbeat_button.pack(side='left', padx=(0, 10))
        self.clear_log_button.pack(side='left')
        
        # 日志框架
        self.log_frame.pack(fill='both', expand=True)
        self.log_text.pack(fill='both', expand=True)
        
        # 绑定回车键
        self.password_entry.bind('<Return>', lambda e: self.authenticate())
    
    def toggle_connection(self):
        """切换连接状态"""
        if self.is_connected:
            self.disconnect()
        else:
            self.connect()
    
    def connect(self):
        """连接到服务器"""
        host = self.host_var.get().strip()
        port_str = self.port_var.get().strip()
        
        # 验证输入
        if not host:
            messagebox.showerror("错误", "请输入服务器地址")
            return
        
        try:
            port = int(port_str)
            if port < 1 or port > 65535:
                raise ValueError("端口范围错误")
        except ValueError:
            messagebox.showerror("错误", "请输入有效的端口号 (1-65535)")
            return
        
        # 在新线程中连接
        def connect_thread():
            try:
                self.log_message("正在连接服务器...")
                self.connection_status_var.set("连接中...")
                
                result = self.network_client.connect(host, port)
                
                if result['success']:
                    self.is_connected = True
                    self.connection_info = {
                        'host': host,
                        'port': port,
                        'connected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    # 更新UI
                    self.root.after(0, self._on_connect_success)
                    self.log_message(f"连接成功: {host}:{port}")
                else:
                    # 更新UI
                    self.root.after(0, lambda: self._on_connect_failed(result['message']))
                    self.log_message(f"连接失败: {result['message']}")
                    
            except Exception as e:
                self.root.after(0, lambda: self._on_connect_failed(str(e)))
                self.log_message(f"连接异常: {str(e)}")
        
        threading.Thread(target=connect_thread, daemon=True).start()
    
    def _on_connect_success(self):
        """连接成功回调"""
        self.connection_status_var.set("已连接")
        self.connect_button.config(text="断开")
        self.auth_button.config(state='normal')
        self.register_button.config(state='normal')
        self.heartbeat_button.config(state='normal')
        
        # 禁用连接配置
        self.host_entry.config(state='disabled')
        self.port_entry.config(state='disabled')
    
    def _on_connect_failed(self, error_message: str):
        """连接失败回调"""
        self.connection_status_var.set("连接失败")
        messagebox.showerror("连接错误", f"无法连接到服务器:\n{error_message}")
    
    def disconnect(self):
        """断开连接"""
        try:
            if self.is_authenticated:
                self.logout()
            
            self.network_client.disconnect()
            self.is_connected = False
            self.connection_info = {}
            
            # 更新UI
            self.connection_status_var.set("未连接")
            self.connect_button.config(text="连接")
            self.auth_button.config(state='disabled')
            self.register_button.config(state='disabled')
            self.heartbeat_button.config(state='disabled')
            
            # 启用连接配置
            self.host_entry.config(state='normal')
            self.port_entry.config(state='normal')
            
            self.log_message("已断开连接")
            
        except Exception as e:
            self.log_message(f"断开连接时发生错误: {str(e)}")
    
    def authenticate(self):
        """用户认证"""
        if not self.is_connected:
            messagebox.showerror("错误", "请先连接到服务器")
            return
        
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        if not username:
            messagebox.showerror("错误", "请输入用户名")
            return
        
        if not password:
            messagebox.showerror("错误", "请输入密码")
            return
        
        # 在新线程中认证
        def auth_thread():
            try:
                self.log_message(f"正在认证用户: {username}")
                self.auth_status_var.set("认证中...")
                
                result = self.network_client.authenticate(username, password)
                
                if result['success']:
                    self.is_authenticated = True
                    
                    # 更新UI
                    self.root.after(0, lambda: self._on_auth_success(result))
                    self.log_message(f"认证成功: {username}")
                else:
                    # 更新UI
                    self.root.after(0, lambda: self._on_auth_failed(result))
                    self.log_message(f"认证失败: {result['message']}")
                    
            except Exception as e:
                self.root.after(0, lambda: self._on_auth_failed({'message': str(e)}))
                self.log_message(f"认证异常: {str(e)}")
        
        threading.Thread(target=auth_thread, daemon=True).start()
    
    def _on_auth_success(self, result: Dict[str, Any]):
        """认证成功回调"""
        self.auth_status_var.set("已认证")
        self.auth_button.config(state='disabled')
        self.logout_button.config(state='normal')
        
        # 清除密码
        self.password_var.set("")
        
        # 显示用户信息
        self.display_user_info(result)
        
        messagebox.showinfo("认证成功", f"欢迎, {result.get('username', '用户')}!")
    
    def _on_auth_failed(self, result: Dict[str, Any]):
        """认证失败回调"""
        self.auth_status_var.set("认证失败")
        
        # 清除密码
        self.password_var.set("")
        
        error_msg = result.get('message', '认证失败')
        
        # 显示额外信息
        if 'remaining_attempts' in result:
            error_msg += f"\n剩余尝试次数: {result['remaining_attempts']}"
        
        if 'remaining_time' in result:
            error_msg += f"\n账户锁定剩余时间: {result['remaining_time']}秒"
        
        messagebox.showerror("认证失败", error_msg)
    
    def logout(self):
        """用户登出"""
        if not self.is_authenticated:
            return
        
        try:
            result = self.network_client.logout()
            
            self.is_authenticated = False
            self.auth_status_var.set("未认证")
            self.auth_button.config(state='normal')
            self.logout_button.config(state='disabled')
            
            # 清除用户信息
            self.clear_user_info()
            
            if result.get('success', True):
                self.log_message("登出成功")
            else:
                self.log_message(f"登出时发生错误: {result.get('message', '未知错误')}")
                
        except Exception as e:
            self.log_message(f"登出异常: {str(e)}")
    
    def send_heartbeat(self):
        """发送心跳"""
        if not self.is_connected:
            messagebox.showerror("错误", "请先连接到服务器")
            return
        
        def heartbeat_thread():
            try:
                self.log_message("发送心跳...")
                result = self.network_client.send_heartbeat()
                
                if result['success']:
                    self.log_message("心跳响应正常")
                    if 'server_time' in result:
                        self.log_message(f"服务器时间: {result['server_time']}")
                else:
                    self.log_message(f"心跳失败: {result.get('message', '未知错误')}")
                    
            except Exception as e:
                self.log_message(f"心跳异常: {str(e)}")
        
        threading.Thread(target=heartbeat_thread, daemon=True).start()
    
    def display_user_info(self, auth_result: Dict[str, Any]):
        """显示用户信息"""
        self.user_info_text.config(state='normal')
        self.user_info_text.delete(1.0, tk.END)
        
        info_lines = [
            f"用户名: {auth_result.get('username', 'N/A')}",
            f"用户ID: {auth_result.get('user_id', 'N/A')}",
            f"认证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"会话过期: {auth_result.get('expires_at', 'N/A')}",
            f"上次登录: {auth_result.get('last_login', 'N/A')}",
            "",
            "连接信息:",
            f"服务器: {self.connection_info.get('host', 'N/A')}:{self.connection_info.get('port', 'N/A')}",
            f"连接时间: {self.connection_info.get('connected_at', 'N/A')}"
        ]
        
        self.user_info_text.insert(tk.END, '\n'.join(info_lines))
        self.user_info_text.config(state='disabled')
    
    def clear_user_info(self):
        """清除用户信息"""
        self.user_info_text.config(state='normal')
        self.user_info_text.delete(1.0, tk.END)
        self.user_info_text.config(state='disabled')
    
    def log_message(self, message: str):
        """记录日志消息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.config(state='normal')
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')
    
    def clear_log(self):
        """清除日志"""
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')
    
    def update_status_timer(self):
        """状态更新定时器"""
        # 检查连接状态
        if self.is_connected and not self.network_client.is_connected():
            self.is_connected = False
            self.is_authenticated = False
            self.connection_status_var.set("连接丢失")
            self.auth_status_var.set("未认证")
            self.connect_button.config(text="连接")
            self.auth_button.config(state='disabled')
            self.register_button.config(state='disabled')
            self.logout_button.config(state='disabled')
            self.heartbeat_button.config(state='disabled')
            self.host_entry.config(state='normal')
            self.port_entry.config(state='normal')
            self.clear_user_info()
            self.log_message("连接已丢失")
        
        # 每秒检查一次
        self.root.after(1000, self.update_status_timer)
    
    def show_register_dialog(self):
        """显示用户注册对话框"""
        if not self.is_connected:
            messagebox.showerror("错误", "请先连接到服务器")
            return
        
        # 创建注册对话框
        register_dialog = UserRegistrationDialog(self.root, self.network_client)
        register_dialog.show()
    
    def show_recharge_dialog(self):
        """显示充值对话框"""
        if not self.is_authenticated:
            messagebox.showerror("错误", "请先登录")
            return
        
        try:
            # 创建充值对话框
            recharge_dialog = CardRechargeDialog(self.root, self.network_client)
            recharge_dialog.show()
        except Exception as e:
            self.log_message(f"打开充值对话框失败: {str(e)}")
            messagebox.showerror("错误", f"无法打开充值对话框: {str(e)}")
    
    def refresh_user_time(self):
        """刷新用户剩余时间"""
        if not self.is_authenticated:
            messagebox.showerror("错误", "请先登录")
            return
        
        def refresh_thread():
            try:
                self.log_message("正在刷新用户时间...")
                result = self.network_client.get_remaining_time()
                
                if result.get('success', False):
                    remaining_time = result.get('remaining_time', 0)
                    self.root.after(0, lambda: self._update_remaining_time(remaining_time))
                    self.log_message(f"剩余时间: {remaining_time}秒")
                else:
                    self.log_message(f"获取剩余时间失败: {result.get('message', '未知错误')}")
                    
            except Exception as e:
                self.log_message(f"刷新时间异常: {str(e)}")
        
        threading.Thread(target=refresh_thread, daemon=True).start()
    
    def _update_remaining_time(self, remaining_time):
        """更新剩余时间显示"""
        if remaining_time > 0:
            hours = remaining_time // 3600
            minutes = (remaining_time % 3600) // 60
            seconds = remaining_time % 60
            time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            self.remaining_time_var.set(time_str)
            self.remaining_time_label.config(foreground='green')
        else:
            self.remaining_time_var.set("已过期")
            self.remaining_time_label.config(foreground='red')
    
    def on_closing(self):
        """窗口关闭事件"""
        try:
            if self.is_connected:
                self.disconnect()
        except:
            pass
        
        self.root.destroy()
    
    def run(self):
        """运行GUI"""
        self.log_message("客户端已启动")
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = ClientGUI()
        app.run()
    except Exception as e:
        print(f"启动客户端GUI失败: {e}")


if __name__ == "__main__":
    main()
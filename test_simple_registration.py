#!/usr/bin/env python3
"""
简单的客户端注册测试
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_manager import DatabaseManager
from server.user_registration_manager import UserRegistrationManager
from server.protocol_handler import ProtocolHandler
from server.authentication_handler import AuthenticationHandler
from server.session_manager import SessionManager


def test_simple_registration():
    """简单的注册测试"""
    print("=" * 50)
    print("简单客户端注册测试")
    print("=" * 50)
    
    # 使用测试数据库
    test_db_path = "data/test_simple_reg.db"
    
    # 删除测试数据库（如果存在）
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
        print("已删除旧的测试数据库")
    
    try:
        # 初始化组件
        print("\n1. 初始化组件...")
        db_manager = DatabaseManager(test_db_path)
        from server.user_manager import UserManager
        user_manager = UserManager(db_manager)
        session_manager = SessionManager(db_manager)
        protocol_handler = ProtocolHandler(user_manager, session_manager)
        
        print("✓ 组件初始化完成")
        
        # 测试注册请求处理
        print("\n2. 测试注册请求处理...")
        
        # 模拟注册请求
        register_requests = [
            {
                'type': 'REGISTER',
                'username': 'testuser1',
                'password': 'testpass123',
                'email': '<EMAIL>'
            },
            {
                'type': 'REGISTER',
                'username': 'testuser2',
                'password': 'testpass456',
                'email': None  # 测试None邮箱
            },
            {
                'type': 'REGISTER',
                'username': 'testuser3',
                'password': 'testpass789'
                # 没有email字段
            }
        ]
        
        for i, request in enumerate(register_requests, 1):
            print(f"\n2.{i} 处理注册请求: {request['username']}")
            
            response = protocol_handler.process_message(
                conn_id='test_conn_1',
                message=request,
                client_ip='127.0.0.1'
            )
            
            if response.get('success'):
                print(f"✓ 注册成功: {response['message']}")
            else:
                print(f"✗ 注册失败: {response['message']}")
                if 'errors' in response:
                    for error in response['errors']:
                        print(f"    - {error}")
        
        # 测试重复注册
        print("\n3. 测试重复注册...")
        
        duplicate_request = {
            'type': 'REGISTER',
            'username': 'testuser1',  # 重复用户名
            'password': 'anotherpass123',
            'email': '<EMAIL>'
        }
        
        response = protocol_handler.process_message(
            conn_id='test_conn_1',
            message=duplicate_request,
            client_ip='127.0.0.1'
        )
        
        if not response.get('success'):
            print(f"✓ 重复注册正确被拒绝: {response['message']}")
        else:
            print(f"✗ 重复注册处理异常: {response}")
        
        # 测试无效数据
        print("\n4. 测试无效数据...")
        
        invalid_requests = [
            {
                'type': 'REGISTER',
                'username': 'ab',  # 用户名过短
                'password': 'testpass123'
            },
            {
                'type': 'REGISTER',
                'username': 'validuser',
                'password': '123'  # 密码过短
            }
        ]
        
        for request in invalid_requests:
            response = protocol_handler.process_message(
                conn_id='test_conn_1',
                message=request,
                client_ip='127.0.0.1'
            )
            
            if not response.get('success'):
                print(f"✓ 无效数据正确被拒绝: {response['message']}")
            else:
                print(f"✗ 无效数据处理异常: {response}")
        
        # 验证用户是否创建成功
        print("\n5. 验证用户创建...")
        
        for username in ['testuser1', 'testuser2', 'testuser3']:
            user_info = db_manager.get_user_info(username)
            if user_info:
                print(f"✓ 用户 {username} 创建成功")
            else:
                print(f"✗ 用户 {username} 未找到")
        
        print("\n" + "=" * 50)
        print("✅ 简单注册测试完成！")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试数据库
        try:
            if os.path.exists(test_db_path):
                import time
                time.sleep(0.1)
                os.remove(test_db_path)
                print("\n已清理测试数据库")
        except PermissionError:
            print(f"\n注意: 无法删除测试数据库 {test_db_path}，请手动删除")


def main():
    """主函数"""
    print("开始简单客户端注册测试...")
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 运行测试
    success = test_simple_registration()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
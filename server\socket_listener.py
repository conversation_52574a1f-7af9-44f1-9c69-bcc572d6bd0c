"""
Socket监听器模块
实现TCP服务器监听、客户端连接管理和网络异常处理
"""

import socket
import threading
import time
import json
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime

from common.exceptions import NetworkError
from common.constants import DEFAULT_CONFIG


class SocketListener:
    """Socket监听器"""
    
    def __init__(self, host: str = None, port: int = None, max_connections: int = None):
        """
        初始化Socket监听器
        
        Args:
            host: 监听地址
            port: 监听端口
            max_connections: 最大连接数
        """
        self.host = host or DEFAULT_CONFIG['server']['host']
        self.port = port or DEFAULT_CONFIG['server']['port']
        self.max_connections = max_connections or DEFAULT_CONFIG['server']['max_connections']
        
        self.server_socket = None
        self.is_running = False
        self.client_handler = None
        self.connection_count = 0
        self.connections = {}  # 存储活跃连接信息
        self._lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_connections': 0,
            'current_connections': 0,
            'failed_connections': 0,
            'start_time': None,
            'last_connection_time': None
        }
    
    def set_client_handler(self, handler: Callable):
        """
        设置客户端处理函数
        
        Args:
            handler: 处理客户端连接的函数，接收(client_socket, address)参数
        """
        self.client_handler = handler
    
    def start_listening(self) -> Dict[str, Any]:
        """
        启动服务器监听
        
        Returns:
            Dict[str, Any]: 启动结果
        """
        try:
            # 创建socket
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            
            # 设置socket选项
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 绑定地址和端口
            self.server_socket.bind((self.host, self.port))
            
            # 开始监听
            self.server_socket.listen(self.max_connections)
            
            self.is_running = True
            self.stats['start_time'] = datetime.now().isoformat()
            
            # 启动接受连接的线程
            accept_thread = threading.Thread(target=self._accept_connections, daemon=True)
            accept_thread.start()
            
            return {
                'success': True,
                'message': f'服务器已启动，监听 {self.host}:{self.port}',
                'host': self.host,
                'port': self.port,
                'max_connections': self.max_connections
            }
            
        except socket.error as e:
            return {
                'success': False,
                'message': f'启动服务器失败: {str(e)}',
                'error_code': e.errno if hasattr(e, 'errno') else None
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'启动服务器时发生未知错误: {str(e)}'
            }
    
    def stop_listening(self) -> Dict[str, Any]:
        """
        停止服务器监听
        
        Returns:
            Dict[str, Any]: 停止结果
        """
        try:
            self.is_running = False
            
            # 关闭所有客户端连接
            with self._lock:
                for conn_id, conn_info in list(self.connections.items()):
                    try:
                        conn_info['socket'].close()
                    except:
                        pass
                self.connections.clear()
                self.connection_count = 0
                self.stats['current_connections'] = 0
            
            # 关闭服务器socket
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
            
            return {
                'success': True,
                'message': '服务器已停止',
                'stats': self.get_statistics()
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'停止服务器时发生错误: {str(e)}'
            }
    
    def _accept_connections(self):
        """接受客户端连接的主循环"""
        while self.is_running and self.server_socket:
            try:
                # 设置超时，避免阻塞
                self.server_socket.settimeout(1.0)
                
                try:
                    client_socket, address = self.server_socket.accept()
                except socket.timeout:
                    continue
                except socket.error:
                    if self.is_running:
                        print(f"接受连接时发生错误")
                    break
                
                # 检查连接数限制
                with self._lock:
                    if self.connection_count >= self.max_connections:
                        try:
                            # 发送拒绝消息
                            error_msg = {
                                'type': 'error',
                                'message': '服务器连接数已满，请稍后重试'
                            }
                            client_socket.send(json.dumps(error_msg).encode('utf-8'))
                            client_socket.close()
                            self.stats['failed_connections'] += 1
                        except:
                            pass
                        continue
                
                # 记录连接信息
                conn_id = f"{address[0]}:{address[1]}_{int(time.time())}"
                conn_info = {
                    'socket': client_socket,
                    'address': address,
                    'connected_at': datetime.now().isoformat(),
                    'last_activity': datetime.now().isoformat()
                }
                
                with self._lock:
                    self.connections[conn_id] = conn_info
                    self.connection_count += 1
                    self.stats['total_connections'] += 1
                    self.stats['current_connections'] = self.connection_count
                    self.stats['last_connection_time'] = datetime.now().isoformat()
                
                print(f"新客户端连接: {address[0]}:{address[1]} (连接ID: {conn_id})")
                
                # 处理客户端连接
                if self.client_handler:
                    # 在新线程中处理客户端
                    client_thread = threading.Thread(
                        target=self._handle_client_wrapper,
                        args=(conn_id, client_socket, address),
                        daemon=True
                    )
                    client_thread.start()
                else:
                    # 没有处理器，关闭连接
                    self._remove_connection(conn_id)
                    client_socket.close()
                
            except Exception as e:
                if self.is_running:
                    print(f"接受连接时发生未知错误: {e}")
                    time.sleep(1)  # 避免快速循环
    
    def _handle_client_wrapper(self, conn_id: str, client_socket: socket.socket, address: tuple):
        """
        客户端处理包装器，负责连接清理
        
        Args:
            conn_id: 连接ID
            client_socket: 客户端socket
            address: 客户端地址
        """
        try:
            # 调用用户定义的处理函数
            self.client_handler(conn_id, client_socket, address)
        except Exception as e:
            print(f"处理客户端 {address[0]}:{address[1]} 时发生错误: {e}")
        finally:
            # 清理连接
            self._remove_connection(conn_id)
            try:
                client_socket.close()
            except:
                pass
    
    def _remove_connection(self, conn_id: str):
        """
        移除连接记录
        
        Args:
            conn_id: 连接ID
        """
        with self._lock:
            if conn_id in self.connections:
                address = self.connections[conn_id]['address']
                del self.connections[conn_id]
                self.connection_count -= 1
                self.stats['current_connections'] = self.connection_count
                print(f"客户端断开连接: {address[0]}:{address[1]} (连接ID: {conn_id})")
    
    def send_message(self, conn_id: str, message: Dict[str, Any]) -> bool:
        """
        向指定连接发送消息
        
        Args:
            conn_id: 连接ID
            message: 要发送的消息
            
        Returns:
            bool: 发送成功返回True
        """
        with self._lock:
            if conn_id not in self.connections:
                return False
            
            try:
                client_socket = self.connections[conn_id]['socket']
                message_json = json.dumps(message, ensure_ascii=False)
                message_bytes = message_json.encode('utf-8')
                
                # 发送消息长度（4字节）
                length = len(message_bytes)
                client_socket.send(length.to_bytes(4, byteorder='big'))
                
                # 发送消息内容
                client_socket.send(message_bytes)
                
                # 更新最后活动时间
                self.connections[conn_id]['last_activity'] = datetime.now().isoformat()
                
                return True
                
            except Exception as e:
                print(f"发送消息到连接 {conn_id} 失败: {e}")
                # 连接可能已断开，移除连接记录
                self._remove_connection(conn_id)
                return False
    
    def receive_message(self, client_socket: socket.socket, timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """
        从客户端接收消息
        
        Args:
            client_socket: 客户端socket
            timeout: 超时时间（秒）
            
        Returns:
            Optional[Dict[str, Any]]: 接收到的消息，失败返回None
        """
        try:
            # 设置接收超时
            client_socket.settimeout(timeout)
            
            # 接收消息长度（4字节）
            length_bytes = self._receive_exact(client_socket, 4)
            if not length_bytes:
                return None
            
            message_length = int.from_bytes(length_bytes, byteorder='big')
            
            # 检查消息长度是否合理（最大1MB）
            if message_length > 1024 * 1024:
                raise NetworkError("消息长度超出限制")
            
            # 接收消息内容
            message_bytes = self._receive_exact(client_socket, message_length)
            if not message_bytes:
                return None
            
            # 解析JSON消息
            message_json = message_bytes.decode('utf-8')
            message = json.loads(message_json)
            
            return message
            
        except socket.timeout:
            print("接收消息超时")
            return None
        except json.JSONDecodeError as e:
            print(f"解析JSON消息失败: {e}")
            return None
        except Exception as e:
            print(f"接收消息失败: {e}")
            return None
    
    def _receive_exact(self, client_socket: socket.socket, length: int) -> Optional[bytes]:
        """
        接收指定长度的数据
        
        Args:
            client_socket: 客户端socket
            length: 要接收的数据长度
            
        Returns:
            Optional[bytes]: 接收到的数据，失败返回None
        """
        data = b''
        while len(data) < length:
            try:
                chunk = client_socket.recv(length - len(data))
                if not chunk:
                    return None
                data += chunk
            except socket.error:
                return None
        return data
    
    def broadcast_message(self, message: Dict[str, Any], exclude_conn_id: str = None) -> int:
        """
        向所有连接广播消息
        
        Args:
            message: 要广播的消息
            exclude_conn_id: 要排除的连接ID
            
        Returns:
            int: 成功发送的连接数
        """
        success_count = 0
        
        with self._lock:
            conn_ids = list(self.connections.keys())
        
        for conn_id in conn_ids:
            if conn_id != exclude_conn_id:
                if self.send_message(conn_id, message):
                    success_count += 1
        
        return success_count
    
    def get_connections(self) -> List[Dict[str, Any]]:
        """
        获取当前连接列表
        
        Returns:
            List[Dict[str, Any]]: 连接信息列表
        """
        with self._lock:
            connections = []
            for conn_id, conn_info in self.connections.items():
                connections.append({
                    'conn_id': conn_id,
                    'address': f"{conn_info['address'][0]}:{conn_info['address'][1]}",
                    'connected_at': conn_info['connected_at'],
                    'last_activity': conn_info['last_activity']
                })
            return connections
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取服务器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            stats = self.stats.copy()
            stats['current_connections'] = self.connection_count
            stats['is_running'] = self.is_running
            stats['host'] = self.host
            stats['port'] = self.port
            stats['max_connections'] = self.max_connections
            
            # 计算运行时间
            if stats['start_time']:
                start_time = datetime.fromisoformat(stats['start_time'])
                uptime_seconds = (datetime.now() - start_time).total_seconds()
                stats['uptime_seconds'] = int(uptime_seconds)
            else:
                stats['uptime_seconds'] = 0
            
            return stats
    
    def is_listening(self) -> bool:
        """
        检查服务器是否正在监听
        
        Returns:
            bool: 正在监听返回True
        """
        return self.is_running and self.server_socket is not None
    
    def disconnect_client(self, conn_id: str) -> bool:
        """
        断开指定客户端连接
        
        Args:
            conn_id: 连接ID
            
        Returns:
            bool: 断开成功返回True，连接不存在返回False
        """
        with self._lock:
            if conn_id in self.connections:
                try:
                    # 获取客户端socket
                    client_socket = self.connections[conn_id]['socket']
                    address = self.connections[conn_id]['address']
                    
                    # 发送断开连接通知（可选）
                    try:
                        disconnect_msg = {
                            'type': 'disconnect',
                            'message': '服务器主动断开连接'
                        }
                        message_json = json.dumps(disconnect_msg, ensure_ascii=False)
                        message_bytes = message_json.encode('utf-8')
                        length = len(message_bytes)
                        client_socket.send(length.to_bytes(4, byteorder='big'))
                        client_socket.send(message_bytes)
                    except:
                        pass  # 忽略发送通知失败
                    
                    # 关闭socket连接
                    client_socket.close()
                    
                    # 移除连接记录
                    del self.connections[conn_id]
                    self.connection_count -= 1
                    self.stats['current_connections'] = self.connection_count
                    
                    print(f"主动断开客户端连接: {address[0]}:{address[1]} (连接ID: {conn_id})")
                    return True
                    
                except Exception as e:
                    print(f"断开连接 {conn_id} 时发生错误: {e}")
                    # 即使出错也要清理连接记录
                    if conn_id in self.connections:
                        del self.connections[conn_id]
                        self.connection_count -= 1
                        self.stats['current_connections'] = self.connection_count
                    return False
            else:
                return False  # 连接不存在

    def disconnect_all_clients(self) -> int:
        """
        断开所有客户端连接
        
        Returns:
            int: 断开的连接数量
        """
        disconnected_count = 0
        
        with self._lock:
            # 复制连接ID列表，避免在迭代时修改字典
            conn_ids = list(self.connections.keys())
        
        # 逐个断开连接
        for conn_id in conn_ids:
            if self.disconnect_client(conn_id):
                disconnected_count += 1
        
        print(f"已断开所有客户端连接，共 {disconnected_count} 个连接")
        return disconnected_count

    def get_connection_info(self, conn_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定连接的信息
        
        Args:
            conn_id: 连接ID
            
        Returns:
            Optional[Dict[str, Any]]: 连接信息，不存在返回None
        """
        with self._lock:
            if conn_id in self.connections:
                conn_info = self.connections[conn_id].copy()
                conn_info['address'] = f"{conn_info['address'][0]}:{conn_info['address'][1]}"
                del conn_info['socket']  # 不返回socket对象
                return conn_info
            return None
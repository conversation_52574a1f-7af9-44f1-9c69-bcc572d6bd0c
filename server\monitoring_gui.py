"""
监控和日志GUI模块
实现连接状态监控、系统状态监控和日志查看功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from server.authentication_handler import AuthenticationHandler
from server.socket_listener import SocketListener
from server.thread_pool_manager import ThreadPoolManager
from server.database_manager import DatabaseManager


class MonitoringGUI:
    """监控和日志GUI"""
    
    def __init__(self, parent_frame: ttk.Frame, auth_handler: AuthenticationHandler,
                 socket_listener: SocketListener = None, thread_pool: ThreadPoolManager = None,
                 db_manager: DatabaseManager = None):
        """
        初始化监控GUI
        
        Args:
            parent_frame: 父框架
            auth_handler: 认证处理器
            socket_listener: Socket监听器
            thread_pool: 线程池管理器
            db_manager: 数据库管理器
        """
        self.parent_frame = parent_frame
        self.auth_handler = auth_handler
        self.socket_listener = socket_listener
        self.thread_pool = thread_pool
        self.db_manager = db_manager
        
        # GUI组件
        self.session_tree = None
        self.log_tree = None
        self.chart_canvas = None
        
        # 监控数据
        self.monitoring_data = {
            'auth_history': [],
            'connection_history': [],
            'performance_history': []
        }
        
        # 更新控制
        self.auto_refresh = True
        self.refresh_interval = 5  # 秒
        
        self._create_widgets()
        self._start_monitoring()
    
    def _create_widgets(self):
        """创建GUI组件"""
        # 创建笔记本控件
        notebook = ttk.Notebook(self.parent_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 会话监控标签页
        self._create_session_monitor_tab(notebook)
        
        # 系统日志标签页
        self._create_system_log_tab(notebook)
        
        # 性能监控标签页
        self._create_performance_monitor_tab(notebook)
        
        # 实时统计标签页
        self._create_realtime_stats_tab(notebook)
    
    def _create_session_monitor_tab(self, parent):
        """创建会话监控标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="会话监控")
        
        # 工具栏
        toolbar_frame = ttk.Frame(frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar_frame, text="刷新会话", 
                  command=self._refresh_sessions).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="终止会话", 
                  command=self._terminate_session).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="导出会话", 
                  command=self._export_sessions).pack(side=tk.LEFT, padx=(0, 10))
        
        # 自动刷新选项
        self.auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(toolbar_frame, text="自动刷新", 
                       variable=self.auto_refresh_var,
                       command=self._toggle_auto_refresh).pack(side=tk.RIGHT)
        
        # 会话列表
        list_frame = ttk.LabelFrame(frame, text="活跃会话", padding=5)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ("session_id", "username", "client_ip", "created_at", "expires_at", "last_activity")
        self.session_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        headers = {
            "session_id": ("会话ID", 100),
            "username": ("用户名", 100),
            "client_ip": ("客户端IP", 120),
            "created_at": ("创建时间", 150),
            "expires_at": ("过期时间", 150),
            "last_activity": ("最后活动", 150)
        }
        
        for col, (header, width) in headers.items():
            self.session_tree.heading(col, text=header)
            self.session_tree.column(col, width=width)
        
        # 添加滚动条
        session_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.session_tree.yview)
        self.session_tree.configure(yscrollcommand=session_scrollbar.set)
        
        self.session_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        session_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 双击事件
        self.session_tree.bind("<Double-1>", self._show_session_details)
    
    def _create_system_log_tab(self, parent):
        """创建系统日志标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="系统日志")
        
        # 日志过滤工具栏
        filter_frame = ttk.Frame(frame)
        filter_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 日志级别过滤
        ttk.Label(filter_frame, text="级别:").pack(side=tk.LEFT, padx=(0, 5))
        self.log_level_var = tk.StringVar(value="全部")
        level_combo = ttk.Combobox(filter_frame, textvariable=self.log_level_var,
                                  values=["全部", "INFO", "WARNING", "ERROR", "SUCCESS"],
                                  state="readonly", width=10)
        level_combo.pack(side=tk.LEFT, padx=(0, 10))
        level_combo.bind('<<ComboboxSelected>>', self._filter_logs)
        
        # 时间范围过滤
        ttk.Label(filter_frame, text="时间范围:").pack(side=tk.LEFT, padx=(0, 5))
        self.time_range_var = tk.StringVar(value="全部")
        time_combo = ttk.Combobox(filter_frame, textvariable=self.time_range_var,
                                 values=["全部", "最近1小时", "最近24小时", "最近7天"],
                                 state="readonly", width=12)
        time_combo.pack(side=tk.LEFT, padx=(0, 10))
        time_combo.bind('<<ComboboxSelected>>', self._filter_logs)
        
        # 搜索框
        ttk.Label(filter_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.log_search_var = tk.StringVar()
        self.log_search_var.trace('w', self._filter_logs)
        search_entry = ttk.Entry(filter_frame, textvariable=self.log_search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        # 操作按钮
        ttk.Button(filter_frame, text="刷新日志", 
                  command=self._refresh_logs).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(filter_frame, text="清空日志", 
                  command=self._clear_logs).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(filter_frame, text="导出日志", 
                  command=self._export_logs).pack(side=tk.RIGHT, padx=(10, 0))
        
        # 日志列表
        log_frame = ttk.LabelFrame(frame, text="日志记录", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        log_columns = ("timestamp", "level", "message", "client_ip", "username")
        self.log_tree = ttk.Treeview(log_frame, columns=log_columns, show="headings", height=20)
        
        # 设置日志列标题和宽度
        log_headers = {
            "timestamp": ("时间", 150),
            "level": ("级别", 80),
            "message": ("消息", 400),
            "client_ip": ("客户端IP", 120),
            "username": ("用户名", 100)
        }
        
        for col, (header, width) in log_headers.items():
            self.log_tree.heading(col, text=header)
            self.log_tree.column(col, width=width)
        
        # 添加滚动条
        log_scrollbar_v = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_tree.yview)
        log_scrollbar_h = ttk.Scrollbar(log_frame, orient=tk.HORIZONTAL, command=self.log_tree.xview)
        self.log_tree.configure(yscrollcommand=log_scrollbar_v.set, xscrollcommand=log_scrollbar_h.set)
        
        self.log_tree.grid(row=0, column=0, sticky="nsew")
        log_scrollbar_v.grid(row=0, column=1, sticky="ns")
        log_scrollbar_h.grid(row=1, column=0, sticky="ew")
        
        log_frame.grid_rowconfigure(0, weight=1)
        log_frame.grid_columnconfigure(0, weight=1)
        
        # 配置日志行颜色
        self.log_tree.tag_configure("INFO", foreground="black")
        self.log_tree.tag_configure("WARNING", foreground="orange")
        self.log_tree.tag_configure("ERROR", foreground="red")
        self.log_tree.tag_configure("SUCCESS", foreground="green")
    
    def _create_performance_monitor_tab(self, parent):
        """创建性能监控标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="性能监控")
        
        # 性能指标框架
        metrics_frame = ttk.LabelFrame(frame, text="系统指标", padding=10)
        metrics_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建指标显示网格
        metrics_grid = ttk.Frame(metrics_frame)
        metrics_grid.pack()
        
        # 性能指标标签
        self.performance_labels = {}
        metrics = [
            ("CPU使用率:", "cpu_usage", "%"),
            ("内存使用:", "memory_usage", "MB"),
            ("活跃连接:", "active_connections", "个"),
            ("认证速率:", "auth_rate", "次/分钟"),
            ("数据库查询:", "db_queries", "次/秒"),
            ("响应时间:", "response_time", "ms")
        ]
        
        for i, (label, key, unit) in enumerate(metrics):
            row, col = i // 3, (i % 3) * 3
            ttk.Label(metrics_grid, text=label).grid(row=row, column=col, sticky=tk.W, padx=(0, 5))
            
            value_label = ttk.Label(metrics_grid, text="0", font=("Arial", 10, "bold"))
            value_label.grid(row=row, column=col+1, sticky=tk.W, padx=(0, 5))
            
            ttk.Label(metrics_grid, text=unit).grid(row=row, column=col+2, sticky=tk.W, padx=(0, 20))
            
            self.performance_labels[key] = value_label
        
        # 图表框
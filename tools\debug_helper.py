#!/usr/bin/env python3
"""
调试和诊断工具
帮助诊断网络验证工具的问题并提供解决方案
"""

import sys
import os
import json
import sqlite3
import socket
import threading
import time
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.database_manager import DatabaseManager
from server.socket_listener import SocketListener
from client.network_client import NetworkClient
from common.config_manager import ConfigManager


class DebugHelper:
    """调试助手"""
    
    def __init__(self):
        """初始化调试助手"""
        self.issues_found = []
        self.suggestions = []
        self.test_results = {}
    
    def diagnose_system(self) -> Dict[str, Any]:
        """
        系统诊断
        
        Returns:
            Dict[str, Any]: 诊断结果
        """
        print("开始系统诊断...")
        
        diagnosis = {
            'timestamp': datetime.now().isoformat(),
            'python_version': sys.version,
            'platform': sys.platform,
            'issues': [],
            'suggestions': [],
            'tests': {}
        }
        
        # 检查Python版本
        self._check_python_version(diagnosis)
        
        # 检查依赖模块
        self._check_dependencies(diagnosis)
        
        # 检查文件结构
        self._check_file_structure(diagnosis)
        
        # 检查配置文件
        self._check_configuration(diagnosis)
        
        # 检查数据库
        self._check_database(diagnosis)
        
        # 检查网络连接
        self._check_network_connectivity(diagnosis)
        
        # 检查端口可用性
        self._check_port_availability(diagnosis)
        
        return diagnosis
    
    def _check_python_version(self, diagnosis: Dict[str, Any]):
        """检查Python版本"""
        try:
            version_info = sys.version_info
            if version_info < (3, 7):
                diagnosis['issues'].append({
                    'type': 'python_version',
                    'severity': 'high',
                    'message': f'Python版本过低: {sys.version}，需要3.7或更高版本'
                })
                diagnosis['suggestions'].append('请升级Python到3.7或更高版本')
            else:
                diagnosis['tests']['python_version'] = {
                    'status': 'pass',
                    'message': f'Python版本正常: {sys.version}'
                }
        except Exception as e:
            diagnosis['issues'].append({
                'type': 'python_version',
                'severity': 'high',
                'message': f'检查Python版本时发生错误: {e}'
            })
    
    def _check_dependencies(self, diagnosis: Dict[str, Any]):
        """检查依赖模块"""
        required_modules = [
            'tkinter',
            'sqlite3',
            'socket',
            'threading',
            'json',
            'hashlib',
            'configparser',
            'logging'
        ]
        
        optional_modules = [
            'psutil',
            'win32serviceutil'  # Windows服务支持
        ]
        
        missing_required = []
        missing_optional = []
        
        # 检查必需模块
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_required.append(module)
        
        # 检查可选模块
        for module in optional_modules:
            try:
                __import__(module)
            except ImportError:
                missing_optional.append(module)
        
        if missing_required:
            diagnosis['issues'].append({
                'type': 'missing_dependencies',
                'severity': 'high',
                'message': f'缺少必需模块: {", ".join(missing_required)}'
            })
            diagnosis['suggestions'].append(f'请安装缺少的模块: pip install {" ".join(missing_required)}')
        
        if missing_optional:
            diagnosis['issues'].append({
                'type': 'missing_optional_dependencies',
                'severity': 'medium',
                'message': f'缺少可选模块: {", ".join(missing_optional)}'
            })
            diagnosis['suggestions'].append(f'建议安装可选模块以获得完整功能: pip install {" ".join(missing_optional)}')
        
        if not missing_required:
            diagnosis['tests']['dependencies'] = {
                'status': 'pass',
                'message': '所有必需模块都已安装'
            }
    
    def _check_file_structure(self, diagnosis: Dict[str, Any]):
        """检查文件结构"""
        required_files = [
            'network_auth_tool.py',
            'server/server_application.py',
            'client/client_application.py',
            'common/config_manager.py',
            'common/constants.py'
        ]
        
        required_dirs = [
            'server',
            'client',
            'common',
            'data',
            'logs'
        ]
        
        missing_files = []
        missing_dirs = []
        
        # 检查文件
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        # 检查目录
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                missing_dirs.append(dir_path)
        
        if missing_files:
            diagnosis['issues'].append({
                'type': 'missing_files',
                'severity': 'high',
                'message': f'缺少必需文件: {", ".join(missing_files)}'
            })
            diagnosis['suggestions'].append('请确保所有必需文件都存在')
        
        if missing_dirs:
            diagnosis['issues'].append({
                'type': 'missing_directories',
                'severity': 'medium',
                'message': f'缺少目录: {", ".join(missing_dirs)}'
            })
            diagnosis['suggestions'].append('请创建缺少的目录')
        
        if not missing_files and not missing_dirs:
            diagnosis['tests']['file_structure'] = {
                'status': 'pass',
                'message': '文件结构完整'
            }
    
    def _check_configuration(self, diagnosis: Dict[str, Any]):
        """检查配置文件"""
        try:
            config_manager = ConfigManager()
            
            # 检查服务器配置
            server_config = config_manager.get_server_config()
            if not server_config:
                diagnosis['issues'].append({
                    'type': 'server_config',
                    'severity': 'high',
                    'message': '无法加载服务器配置'
                })
                diagnosis['suggestions'].append('请检查config.ini文件是否存在且格式正确')
            
            # 检查客户端配置
            client_config = config_manager.get_client_config()
            if not client_config:
                diagnosis['issues'].append({
                    'type': 'client_config',
                    'severity': 'medium',
                    'message': '无法加载客户端配置'
                })
                diagnosis['suggestions'].append('请检查client_config.ini文件是否存在且格式正确')
            
            if server_config and client_config:
                diagnosis['tests']['configuration'] = {
                    'status': 'pass',
                    'message': '配置文件加载正常'
                }
                
        except Exception as e:
            diagnosis['issues'].append({
                'type': 'configuration_error',
                'severity': 'high',
                'message': f'检查配置时发生错误: {e}'
            })
    
    def _check_database(self, diagnosis: Dict[str, Any]):
        """检查数据库"""
        try:
            db_path = "data/auth.db"
            
            if not os.path.exists(db_path):
                diagnosis['issues'].append({
                    'type': 'database_missing',
                    'severity': 'high',
                    'message': '数据库文件不存在'
                })
                diagnosis['suggestions'].append('请运行install.py初始化数据库')
                return
            
            # 检查数据库连接
            db_manager = DatabaseManager(db_path)
            
            # 检查表结构
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查必需的表
                required_tables = ['users', 'sessions', 'logs']
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                existing_tables = [row[0] for row in cursor.fetchall()]
                
                missing_tables = [table for table in required_tables if table not in existing_tables]
                
                if missing_tables:
                    diagnosis['issues'].append({
                        'type': 'database_tables',
                        'severity': 'high',
                        'message': f'数据库缺少表: {", ".join(missing_tables)}'
                    })
                    diagnosis['suggestions'].append('请重新运行install.py初始化数据库')
                else:
                    diagnosis['tests']['database'] = {
                        'status': 'pass',
                        'message': '数据库结构正常'
                    }
                    
        except Exception as e:
            diagnosis['issues'].append({
                'type': 'database_error',
                'severity': 'high',
                'message': f'检查数据库时发生错误: {e}'
            })
    
    def _check_network_connectivity(self, diagnosis: Dict[str, Any]):
        """检查网络连接"""
        try:
            # 测试本地回环连接
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(5)
            
            try:
                test_socket.connect(('127.0.0.1', 80))  # 尝试连接到本地80端口
                test_socket.close()
            except:
                pass  # 连接失败是正常的，我们只是测试网络栈
            
            diagnosis['tests']['network_connectivity'] = {
                'status': 'pass',
                'message': '网络连接正常'
            }
            
        except Exception as e:
            diagnosis['issues'].append({
                'type': 'network_error',
                'severity': 'medium',
                'message': f'网络连接测试失败: {e}'
            })
    
    def _check_port_availability(self, diagnosis: Dict[str, Any]):
        """检查端口可用性"""
        try:
            # 检查默认端口8888是否可用
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            try:
                test_socket.bind(('127.0.0.1', 8888))
                test_socket.close()
                
                diagnosis['tests']['port_availability'] = {
                    'status': 'pass',
                    'message': '默认端口8888可用'
                }
                
            except OSError as e:
                if e.errno == 98:  # Address already in use
                    diagnosis['issues'].append({
                        'type': 'port_in_use',
                        'severity': 'medium',
                        'message': '端口8888已被占用'
                    })
                    diagnosis['suggestions'].append('请更改配置文件中的端口号或停止占用端口的程序')
                else:
                    diagnosis['issues'].append({
                        'type': 'port_error',
                        'severity': 'medium',
                        'message': f'端口测试失败: {e}'
                    })
                    
        except Exception as e:
            diagnosis['issues'].append({
                'type': 'port_check_error',
                'severity': 'low',
                'message': f'检查端口可用性时发生错误: {e}'
            })
    
    def test_server_startup(self) -> Dict[str, Any]:
        """测试服务器启动"""
        print("测试服务器启动...")
        
        result = {
            'success': False,
            'message': '',
            'details': {},
            'errors': []
        }
        
        try:
            from server.server_application import ServerApplication
            
            # 创建服务器应用实例
            server_app = ServerApplication()
            
            # 尝试启动服务器
            start_result = server_app.start()
            
            if start_result['success']:
                result['success'] = True
                result['message'] = '服务器启动成功'
                result['details'] = start_result
                
                # 等待一小段时间确保服务器完全启动
                time.sleep(2)
                
                # 停止服务器
                stop_result = server_app.shutdown()
                if stop_result['success']:
                    result['message'] += '，停止成功'
                else:
                    result['errors'].append(f'停止服务器失败: {stop_result["message"]}')
            else:
                result['message'] = f'服务器启动失败: {start_result["message"]}'
                result['errors'].append(start_result['message'])
                
        except Exception as e:
            result['message'] = f'测试服务器启动时发生异常: {e}'
            result['errors'].append(str(e))
            result['details']['exception'] = traceback.format_exc()
        
        return result
    
    def test_client_connection(self, host: str = '127.0.0.1', port: int = 8888) -> Dict[str, Any]:
        """测试客户端连接"""
        print(f"测试客户端连接到 {host}:{port}...")
        
        result = {
            'success': False,
            'message': '',
            'details': {},
            'errors': []
        }
        
        try:
            client = NetworkClient()
            
            # 尝试连接
            connect_result = client.connect(host, port)
            
            if connect_result['success']:
                result['success'] = True
                result['message'] = '客户端连接成功'
                result['details']['connection'] = connect_result
                
                # 测试发送消息
                test_message = {
                    'type': 'ping',
                    'timestamp': time.time()
                }
                
                send_result = client.send_message(test_message)
                if send_result['success']:
                    result['details']['send'] = send_result
                    
                    # 尝试接收响应
                    try:
                        response = client.receive_message(timeout=5)
                        if response:
                            result['details']['receive'] = response
                            result['message'] += '，消息收发正常'
                        else:
                            result['errors'].append('未收到服务器响应')
                    except Exception as e:
                        result['errors'].append(f'接收消息失败: {e}')
                else:
                    result['errors'].append(f'发送消息失败: {send_result["message"]}')
                
                # 断开连接
                client.disconnect()
                
            else:
                result['message'] = f'客户端连接失败: {connect_result["message"]}'
                result['errors'].append(connect_result['message'])
                
        except Exception as e:
            result['message'] = f'测试客户端连接时发生异常: {e}'
            result['errors'].append(str(e))
            result['details']['exception'] = traceback.format_exc()
        
        return result
    
    def test_authentication_flow(self) -> Dict[str, Any]:
        """测试完整认证流程"""
        print("测试完整认证流程...")
        
        result = {
            'success': False,
            'message': '',
            'steps': {},
            'errors': []
        }
        
        try:
            # 1. 创建测试用户
            from server.database_manager import DatabaseManager
            from server.authentication_handler import AuthenticationHandler
            
            db_manager = DatabaseManager()
            auth_handler = AuthenticationHandler(db_manager)
            
            test_username = f"test_user_{int(time.time())}"
            test_password = "test_password_123"
            
            # 创建用户
            create_result = auth_handler.user_manager.create_user(test_username, test_password)
            result['steps']['create_user'] = create_result
            
            if not create_result['success']:
                result['errors'].append(f'创建测试用户失败: {create_result["message"]}')
                return result
            
            # 2. 测试认证
            auth_result = auth_handler.authenticate(test_username, test_password, '127.0.0.1')
            result['steps']['authenticate'] = auth_result
            
            if not auth_result['success']:
                result['errors'].append(f'用户认证失败: {auth_result["message"]}')
                return result
            
            session_token = auth_result['session_token']
            
            # 3. 测试会话验证
            validate_result = auth_handler.validate_session(session_token)
            result['steps']['validate_session'] = validate_result
            
            if not validate_result['success']:
                result['errors'].append(f'会话验证失败: {validate_result["message"]}')
                return result
            
            # 4. 测试注销
            logout_result = auth_handler.logout(session_token)
            result['steps']['logout'] = logout_result
            
            if not logout_result['success']:
                result['errors'].append(f'注销失败: {logout_result["message"]}')
                return result
            
            # 5. 清理测试用户
            delete_result = auth_handler.user_manager.delete_user(test_username)
            result['steps']['delete_user'] = delete_result
            
            if delete_result['success']:
                result['success'] = True
                result['message'] = '认证流程测试完成，所有步骤成功'
            else:
                result['errors'].append(f'删除测试用户失败: {delete_result["message"]}')
                result['message'] = '认证流程测试基本成功，但清理测试用户失败'
                
        except Exception as e:
            result['message'] = f'测试认证流程时发生异常: {e}'
            result['errors'].append(str(e))
            result['steps']['exception'] = traceback.format_exc()
        
        return result
    
    def generate_debug_report(self) -> Dict[str, Any]:
        """生成调试报告"""
        print("生成调试报告...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'system_diagnosis': {},
            'server_test': {},
            'client_test': {},
            'auth_test': {},
            'summary': {
                'total_issues': 0,
                'critical_issues': 0,
                'recommendations': []
            }
        }
        
        # 系统诊断
        report['system_diagnosis'] = self.diagnose_system()
        
        # 服务器测试
        report['server_test'] = self.test_server_startup()
        
        # 认证流程测试
        report['auth_test'] = self.test_authentication_flow()
        
        # 统计问题
        issues = report['system_diagnosis'].get('issues', [])
        report['summary']['total_issues'] = len(issues)
        report['summary']['critical_issues'] = len([i for i in issues if i.get('severity') == 'high'])
        
        # 收集建议
        suggestions = report['system_diagnosis'].get('suggestions', [])
        report['summary']['recommendations'] = suggestions
        
        # 添加测试结果建议
        if not report['server_test']['success']:
            report['summary']['recommendations'].append('服务器启动测试失败，请检查配置和依赖')
        
        if not report['auth_test']['success']:
            report['summary']['recommendations'].append('认证流程测试失败，请检查数据库和认证配置')
        
        return report
    
    def save_debug_report(self, report: Dict[str, Any], filename: str = None):
        """保存调试报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"debug_report_{timestamp}.json"
        
        try:
            # 确保reports目录存在
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)
            
            filepath = reports_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"调试报告已保存到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            print(f"保存调试报告失败: {e}")
            return None


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='网络验证工具调试助手')
    parser.add_argument('--diagnose', '-d', action='store_true', help='运行系统诊断')
    parser.add_argument('--test-server', action='store_true', help='测试服务器启动')
    parser.add_argument('--test-client', action='store_true', help='测试客户端连接')
    parser.add_argument('--test-auth', action='store_true', help='测试认证流程')
    parser.add_argument('--full-report', '-f', action='store_true', help='生成完整调试报告')
    parser.add_argument('--host', default='127.0.0.1', help='服务器地址')
    parser.add_argument('--port', type=int, default=8888, help='服务器端口')
    
    args = parser.parse_args()
    
    debug_helper = DebugHelper()
    
    try:
        if args.diagnose:
            diagnosis = debug_helper.diagnose_system()
            print(json.dumps(diagnosis, indent=2, ensure_ascii=False))
            
        elif args.test_server:
            result = debug_helper.test_server_startup()
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
        elif args.test_client:
            result = debug_helper.test_client_connection(args.host, args.port)
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
        elif args.test_auth:
            result = debug_helper.test_authentication_flow()
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
        elif args.full_report:
            report = debug_helper.generate_debug_report()
            debug_helper.save_debug_report(report)
            print(json.dumps(report, indent=2, ensure_ascii=False))
            
        else:
            print("请指定操作参数，使用 --help 查看帮助")
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        traceback.print_exc()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
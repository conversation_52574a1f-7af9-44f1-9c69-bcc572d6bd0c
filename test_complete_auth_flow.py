#!/usr/bin/env python3
"""
完整认证流程测试
测试客户端-服务器完整通信流程
"""

import sys
import os
import time
import threading
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.server_application import ServerApplication
from client.client_application import ClientApplication
from common.config_manager import ConfigManager


class AuthFlowTester:
    """认证流程测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.server_app = None
        self.client_app = None
        self.test_results = []
        self.server_thread = None
        
    def run_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("网络验证工具 - 完整认证流程测试")
        print("=" * 60)
        
        try:
            # 1. 启动服务器
            if not self._start_server():
                return False
            
            # 等待服务器启动
            time.sleep(2)
            
            # 2. 测试基本认证流程
            self._test_basic_auth()
            
            # 3. 测试错误认证
            self._test_invalid_auth()
            
            # 4. 测试账户锁定
            self._test_account_lockout()
            
            # 5. 测试会话管理
            self._test_session_management()
            
            # 6. 测试并发认证
            self._test_concurrent_auth()
            
            # 7. 显示测试结果
            self._show_results()
            
            return True
            
        except Exception as e:
            print(f"测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # 清理资源
            self._cleanup()
    
    def _start_server(self):
        """启动测试服务器"""
        try:
            print("1. 启动测试服务器...")
            
            # 创建服务器应用
            self.server_app = ServerApplication("config.ini")
            
            # 启动服务器
            result = self.server_app.start()
            
            if result['success']:
                print(f"   √ 服务器启动成功: {result['message']}")
                return True
            else:
                print(f"   × 服务器启动失败: {result['message']}")
                return False
                
        except Exception as e:
            print(f"   × 启动服务器时发生错误: {e}")
            return False
    
    def _test_basic_auth(self):
        """测试基本认证流程"""
        print("\n2. 测试基本认证流程...")
        
        try:
            # 创建测试用户
            user_manager = self.server_app.user_manager
            create_result = user_manager.create_user("testuser", "testpass123")
            
            if create_result['success']:
                print("   √ 测试用户创建成功")
            else:
                print(f"   × 测试用户创建失败: {create_result['message']}")
                return
            
            # 创建客户端并测试认证
            client_app = ClientApplication("client_config.ini")
            
            # 连接服务器
            connect_result = client_app.connect_to_server()
            if connect_result['success']:
                print("   √ 客户端连接服务器成功")
            else:
                print(f"   × 客户端连接失败: {connect_result['message']}")
                return
            
            # 执行认证
            auth_result = client_app.authenticate("testuser", "testpass123")
            if auth_result['success']:
                print("   √ 用户认证成功")
                print(f"     会话令牌: {auth_result.get('session_token', 'N/A')[:16]}...")
                
                self.test_results.append({
                    'test': 'basic_auth',
                    'status': 'PASS',
                    'message': '基本认证流程正常'
                })
            else:
                print(f"   × 用户认证失败: {auth_result['message']}")
                self.test_results.append({
                    'test': 'basic_auth',
                    'status': 'FAIL',
                    'message': f'认证失败: {auth_result["message"]}'
                })
            
            # 断开连接
            client_app.disconnect()
            print("   √ 客户端断开连接")
            
        except Exception as e:
            print(f"   × 基本认证测试失败: {e}")
            self.test_results.append({
                'test': 'basic_auth',
                'status': 'ERROR',
                'message': f'测试异常: {str(e)}'
            })
    
    def _test_invalid_auth(self):
        """测试错误认证"""
        print("\n3. 测试错误认证...")
        
        try:
            client_app = ClientApplication("client_config.ini")
            
            # 连接服务器
            connect_result = client_app.connect_to_server()
            if not connect_result['success']:
                print(f"   × 客户端连接失败: {connect_result['message']}")
                return
            
            # 测试错误用户名
            auth_result = client_app.authenticate("wronguser", "testpass123")
            if not auth_result['success']:
                print("   √ 错误用户名认证正确被拒绝")
            else:
                print("   × 错误用户名认证应该被拒绝")
            
            # 测试错误密码
            auth_result = client_app.authenticate("testuser", "wrongpass")
            if not auth_result['success']:
                print("   √ 错误密码认证正确被拒绝")
                remaining = auth_result.get('remaining_attempts')
                if remaining is not None:
                    print(f"     剩余尝试次数: {remaining}")
            else:
                print("   × 错误密码认证应该被拒绝")
            
            client_app.disconnect()
            
            self.test_results.append({
                'test': 'invalid_auth',
                'status': 'PASS',
                'message': '错误认证处理正常'
            })
            
        except Exception as e:
            print(f"   × 错误认证测试失败: {e}")
            self.test_results.append({
                'test': 'invalid_auth',
                'status': 'ERROR',
                'message': f'测试异常: {str(e)}'
            })
    
    def _test_account_lockout(self):
        """测试账户锁定"""
        print("\n4. 测试账户锁定机制...")
        
        try:
            client_app = ClientApplication("client_config.ini")
            connect_result = client_app.connect_to_server()
            
            if not connect_result['success']:
                print(f"   × 客户端连接失败: {connect_result['message']}")
                return
            
            # 多次错误认证触发锁定
            max_attempts = 5
            for i in range(max_attempts + 1):
                auth_result = client_app.authenticate("testuser", "wrongpass")
                
                if auth_result.get('locked'):
                    print(f"   √ 账户在第{i+1}次尝试后被锁定")
                    remaining_time = auth_result.get('remaining_time', 0)
                    print(f"     锁定剩余时间: {remaining_time}秒")
                    break
                else:
                    remaining = auth_result.get('remaining_attempts')
                    if remaining is not None:
                        print(f"   - 第{i+1}次错误尝试，剩余: {remaining}")
            
            client_app.disconnect()
            
            self.test_results.append({
                'test': 'account_lockout',
                'status': 'PASS',
                'message': '账户锁定机制正常'
            })
            
        except Exception as e:
            print(f"   × 账户锁定测试失败: {e}")
            self.test_results.append({
                'test': 'account_lockout',
                'status': 'ERROR',
                'message': f'测试异常: {str(e)}'
            })
    
    def _test_session_management(self):
        """测试会话管理"""
        print("\n5. 测试会话管理...")
        
        try:
            # 解锁测试用户
            user_manager = self.server_app.user_manager
            user_manager.db_manager.update_user("testuser", failed_attempts=0, locked_until=None)
            
            client_app = ClientApplication("client_config.ini")
            connect_result = client_app.connect_to_server()
            
            if not connect_result['success']:
                print(f"   × 客户端连接失败: {connect_result['message']}")
                return
            
            # 认证获取会话
            auth_result = client_app.authenticate("testuser", "testpass123")
            if auth_result['success']:
                session_token = auth_result.get('session_token')
                print(f"   √ 获取会话令牌: {session_token[:16]}...")
                
                # 验证会话
                session_manager = self.server_app.session_manager
                validation = session_manager.validate_session(session_token)
                
                if validation['valid']:
                    print("   √ 会话验证成功")
                    print(f"     用户: {validation.get('username')}")
                    print(f"     过期时间: {validation.get('expires_at')}")
                else:
                    print(f"   × 会话验证失败: {validation['message']}")
                
                # 刷新会话
                refresh_result = session_manager.refresh_session(session_token)
                if refresh_result['success']:
                    print("   √ 会话刷新成功")
                else:
                    print(f"   × 会话刷新失败: {refresh_result['message']}")
                
                # 销毁会话
                destroy_result = session_manager.destroy_session(session_token)
                if destroy_result['success']:
                    print("   √ 会话销毁成功")
                else:
                    print(f"   × 会话销毁失败: {destroy_result['message']}")
            
            client_app.disconnect()
            
            self.test_results.append({
                'test': 'session_management',
                'status': 'PASS',
                'message': '会话管理功能正常'
            })
            
        except Exception as e:
            print(f"   × 会话管理测试失败: {e}")
            self.test_results.append({
                'test': 'session_management',
                'status': 'ERROR',
                'message': f'测试异常: {str(e)}'
            })
    
    def _test_concurrent_auth(self):
        """测试并发认证"""
        print("\n6. 测试并发认证...")
        
        try:
            # 创建多个测试用户
            user_manager = self.server_app.user_manager
            test_users = []
            
            for i in range(3):
                username = f"testuser{i+1}"
                result = user_manager.create_user(username, "testpass123")
                if result['success']:
                    test_users.append(username)
                    print(f"   √ 创建用户: {username}")
            
            # 并发认证测试
            results = []
            threads = []
            
            def auth_worker(username):
                try:
                    client = ClientApplication("client_config.ini")
                    connect_result = client.connect_to_server()
                    
                    if connect_result['success']:
                        auth_result = client.authenticate(username, "testpass123")
                        results.append({
                            'username': username,
                            'success': auth_result['success'],
                            'message': auth_result.get('message', ''),
                            'session_token': auth_result.get('session_token', '')[:8] if auth_result.get('session_token') else None
                        })
                    
                    client.disconnect()
                    
                except Exception as e:
                    results.append({
                        'username': username,
                        'success': False,
                        'message': f'异常: {str(e)}',
                        'session_token': None
                    })
            
            # 启动并发认证
            for username in test_users:
                thread = threading.Thread(target=auth_worker, args=(username,))
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            # 检查结果
            successful_auths = sum(1 for r in results if r['success'])
            print(f"   √ 并发认证完成: {successful_auths}/{len(test_users)} 成功")
            
            for result in results:
                status = "√" if result['success'] else "×"
                print(f"     {status} {result['username']}: {result['message']}")
            
            self.test_results.append({
                'test': 'concurrent_auth',
                'status': 'PASS' if successful_auths == len(test_users) else 'PARTIAL',
                'message': f'并发认证: {successful_auths}/{len(test_users)} 成功'
            })
            
        except Exception as e:
            print(f"   × 并发认证测试失败: {e}")
            self.test_results.append({
                'test': 'concurrent_auth',
                'status': 'ERROR',
                'message': f'测试异常: {str(e)}'
            })
    
    def _show_results(self):
        """显示测试结果"""
        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['status'] == 'PASS')
        failed_tests = sum(1 for r in self.test_results if r['status'] in ['FAIL', 'ERROR'])
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n详细结果:")
        for i, result in enumerate(self.test_results, 1):
            status_symbol = {
                'PASS': '√',
                'FAIL': '×',
                'ERROR': '⚠',
                'PARTIAL': '◐'
            }.get(result['status'], '?')
            
            print(f"{i}. {status_symbol} {result['test']}: {result['message']}")
        
        # 获取服务器统计
        if self.server_app:
            try:
                stats = self.server_app.get_server_statistics()
                print(f"\n服务器统计:")
                print(f"  总连接数: {stats.get('total_connections', 0)}")
                print(f"  成功认证: {stats.get('successful_auths', 0)}")
                print(f"  失败认证: {stats.get('failed_auths', 0)}")
                print(f"  活跃会话: {stats.get('active_sessions', 0)}")
            except:
                pass
    
    def _cleanup(self):
        """清理资源"""
        print("\n7. 清理测试环境...")
        
        try:
            if self.server_app:
                # 清理测试用户
                user_manager = self.server_app.user_manager
                test_users = ["testuser", "testuser1", "testuser2", "testuser3"]
                
                for username in test_users:
                    try:
                        user_manager.delete_user(username)
                        print(f"   √ 删除测试用户: {username}")
                    except:
                        pass
                
                # 关闭服务器
                shutdown_result = self.server_app.shutdown()
                if shutdown_result['success']:
                    print("   √ 服务器已关闭")
                else:
                    print(f"   × 服务器关闭失败: {shutdown_result['message']}")
        
        except Exception as e:
            print(f"   ⚠ 清理过程中发生错误: {e}")


def main():
    """主函数"""
    try:
        tester = AuthFlowTester()
        success = tester.run_tests()
        
        print(f"\n测试完成，结果: {'成功' if success else '失败'}")
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
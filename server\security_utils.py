"""
安全工具模块
实现密码哈希、盐值生成、会话令牌生成和输入验证安全检查
"""

import hashlib
import secrets
import re
import time
import hmac
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from common.exceptions import AuthenticationError


class SecurityUtils:
    """安全工具类"""
    
    # 密码强度要求
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_MAX_LENGTH = 128
    
    # 令牌配置
    TOKEN_LENGTH = 32  # 字节数
    SALT_LENGTH = 16   # 字节数
    
    # 输入验证正则表达式
    USERNAME_PATTERN = re.compile(r'^[a-zA-Z0-9_-]{3,50}$')
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    IP_PATTERN = re.compile(r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')
    
    @staticmethod
    def generate_salt() -> str:
        """
        生成随机盐值
        
        Returns:
            str: 十六进制盐值字符串
        """
        return secrets.token_hex(SecurityUtils.SALT_LENGTH)
    
    @staticmethod
    def hash_password(password: str, salt: str) -> str:
        """
        使用SHA-256和盐值对密码进行哈希
        
        Args:
            password: 原始密码
            salt: 盐值
            
        Returns:
            str: 哈希后的密码
        """
        if not password or not salt:
            raise AuthenticationError("密码和盐值不能为空")
        
        # 将密码和盐值组合
        combined = f"{password}{salt}".encode('utf-8')
        
        # 使用SHA-256进行哈希
        hash_obj = hashlib.sha256(combined)
        
        return hash_obj.hexdigest()
    
    @staticmethod
    def verify_password(password: str, stored_hash: str, salt: str) -> bool:
        """
        验证密码是否正确
        
        Args:
            password: 输入的密码
            stored_hash: 存储的密码哈希
            salt: 盐值
            
        Returns:
            bool: 密码正确返回True
        """
        try:
            computed_hash = SecurityUtils.hash_password(password, salt)
            # 使用时间安全的比较，防止时序攻击
            return hmac.compare_digest(computed_hash, stored_hash)
        except Exception:
            return False
    
    @staticmethod
    def generate_session_token() -> str:
        """
        生成安全的会话令牌
        
        Returns:
            str: 十六进制令牌字符串
        """
        return secrets.token_hex(SecurityUtils.TOKEN_LENGTH)
    
    @staticmethod
    def generate_secure_random(length: int = 16) -> str:
        """
        生成安全的随机字符串
        
        Args:
            length: 字节长度
            
        Returns:
            str: 十六进制随机字符串
        """
        return secrets.token_hex(length)
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """
        验证密码强度
        
        Args:
            password: 密码
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'is_valid': True,
            'score': 0,
            'messages': [],
            'requirements': {
                'length': False,
                'uppercase': False,
                'lowercase': False,
                'digit': False,
                'special': False
            }
        }
        
        if not password:
            result['is_valid'] = False
            result['messages'].append('密码不能为空')
            return result
        
        # 检查长度
        if len(password) < SecurityUtils.PASSWORD_MIN_LENGTH:
            result['is_valid'] = False
            result['messages'].append(f'密码长度至少{SecurityUtils.PASSWORD_MIN_LENGTH}位')
        elif len(password) > SecurityUtils.PASSWORD_MAX_LENGTH:
            result['is_valid'] = False
            result['messages'].append(f'密码长度不能超过{SecurityUtils.PASSWORD_MAX_LENGTH}位')
        else:
            result['requirements']['length'] = True
            result['score'] += 1
        
        # 检查大写字母
        if re.search(r'[A-Z]', password):
            result['requirements']['uppercase'] = True
            result['score'] += 1
        else:
            result['messages'].append('密码应包含大写字母')
        
        # 检查小写字母
        if re.search(r'[a-z]', password):
            result['requirements']['lowercase'] = True
            result['score'] += 1
        else:
            result['messages'].append('密码应包含小写字母')
        
        # 检查数字
        if re.search(r'\d', password):
            result['requirements']['digit'] = True
            result['score'] += 1
        else:
            result['messages'].append('密码应包含数字')
        
        # 检查特殊字符
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            result['requirements']['special'] = True
            result['score'] += 1
        else:
            result['messages'].append('密码应包含特殊字符')
        
        # 检查常见弱密码
        weak_passwords = [
            'password', '123456', 'qwerty', 'abc123', 'password123',
            'admin', 'root', 'user', 'test', '111111', '000000'
        ]
        
        if password.lower() in weak_passwords:
            result['is_valid'] = False
            result['score'] = 0
            result['messages'].append('密码过于简单，请使用更复杂的密码')
        
        # 基本要求：至少包含字母和数字
        if not (result['requirements']['uppercase'] or result['requirements']['lowercase']):
            result['is_valid'] = False
        
        if not result['requirements']['digit']:
            result['is_valid'] = False
        
        return result
    
    @staticmethod
    def validate_username(username: str) -> Dict[str, Any]:
        """
        验证用户名格式
        
        Args:
            username: 用户名
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {'is_valid': True, 'message': '用户名格式正确'}
        
        if not username:
            return {'is_valid': False, 'message': '用户名不能为空'}
        
        if not SecurityUtils.USERNAME_PATTERN.match(username):
            return {
                'is_valid': False,
                'message': '用户名只能包含字母、数字、下划线和连字符，长度3-50位'
            }
        
        # 检查保留用户名
        reserved_names = [
            'admin', 'root', 'system', 'administrator', 'guest',
            'user', 'test', 'demo', 'api', 'service'
        ]
        
        if username.lower() in reserved_names:
            return {
                'is_valid': False,
                'message': '该用户名为系统保留，请选择其他用户名'
            }
        
        return result
    
    @staticmethod
    def validate_ip_address(ip: str) -> bool:
        """
        验证IP地址格式
        
        Args:
            ip: IP地址字符串
            
        Returns:
            bool: 格式正确返回True
        """
        if not ip:
            return False
        
        return bool(SecurityUtils.IP_PATTERN.match(ip))
    
    @staticmethod
    def sanitize_input(input_str: str, max_length: int = 1000) -> str:
        """
        清理输入字符串，防止注入攻击
        
        Args:
            input_str: 输入字符串
            max_length: 最大长度
            
        Returns:
            str: 清理后的字符串
        """
        if not input_str:
            return ""
        
        # 限制长度
        if len(input_str) > max_length:
            input_str = input_str[:max_length]
        
        # 移除危险字符
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\r', '\n']
        for char in dangerous_chars:
            input_str = input_str.replace(char, '')
        
        # 去除首尾空白
        return input_str.strip()
    
    @staticmethod
    def is_safe_filename(filename: str) -> bool:
        """
        检查文件名是否安全
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 安全返回True
        """
        if not filename:
            return False
        
        # 检查危险字符
        dangerous_chars = ['/', '\\', '..', '<', '>', ':', '"', '|', '?', '*']
        for char in dangerous_chars:
            if char in filename:
                return False
        
        # 检查长度
        if len(filename) > 255:
            return False
        
        # 检查保留文件名（Windows）
        reserved_names = [
            'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4',
            'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2',
            'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        ]
        
        if filename.upper() in reserved_names:
            return False
        
        return True


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_attempts: int = 5, window_seconds: int = 300):
        """
        初始化速率限制器
        
        Args:
            max_attempts: 最大尝试次数
            window_seconds: 时间窗口（秒）
        """
        self.max_attempts = max_attempts
        self.window_seconds = window_seconds
        self.attempts = {}  # {key: [timestamp1, timestamp2, ...]}
    
    def is_allowed(self, key: str) -> Dict[str, Any]:
        """
        检查是否允许请求
        
        Args:
            key: 限制键（如IP地址或用户名）
            
        Returns:
            Dict[str, Any]: 检查结果
        """
        current_time = time.time()
        
        # 清理过期记录
        if key in self.attempts:
            self.attempts[key] = [
                timestamp for timestamp in self.attempts[key]
                if current_time - timestamp < self.window_seconds
            ]
        
        # 检查当前尝试次数
        attempt_count = len(self.attempts.get(key, []))
        
        if attempt_count >= self.max_attempts:
            # 计算剩余等待时间
            oldest_attempt = min(self.attempts[key])
            remaining_time = int(self.window_seconds - (current_time - oldest_attempt))
            
            return {
                'allowed': False,
                'remaining_time': max(0, remaining_time),
                'attempt_count': attempt_count,
                'max_attempts': self.max_attempts
            }
        
        return {
            'allowed': True,
            'remaining_attempts': self.max_attempts - attempt_count,
            'attempt_count': attempt_count,
            'max_attempts': self.max_attempts
        }
    
    def record_attempt(self, key: str):
        """
        记录一次尝试
        
        Args:
            key: 限制键
        """
        current_time = time.time()
        
        if key not in self.attempts:
            self.attempts[key] = []
        
        self.attempts[key].append(current_time)
    
    def reset_attempts(self, key: str):
        """
        重置指定键的尝试记录
        
        Args:
            key: 限制键
        """
        if key in self.attempts:
            del self.attempts[key]
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取速率限制统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        current_time = time.time()
        active_keys = 0
        blocked_keys = 0
        
        for key, timestamps in self.attempts.items():
            # 清理过期记录
            valid_timestamps = [
                t for t in timestamps
                if current_time - t < self.window_seconds
            ]
            
            if valid_timestamps:
                active_keys += 1
                if len(valid_timestamps) >= self.max_attempts:
                    blocked_keys += 1
        
        return {
            'max_attempts': self.max_attempts,
            'window_seconds': self.window_seconds,
            'active_keys': active_keys,
            'blocked_keys': blocked_keys,
            'total_tracked_keys': len(self.attempts)
        }


class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def validate_json_message(data: Any, required_fields: List[str] = None) -> Dict[str, Any]:
        """
        验证JSON消息格式
        
        Args:
            data: 要验证的数据
            required_fields: 必需字段列表
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        if not isinstance(data, dict):
            return {'valid': False, 'message': '数据必须是JSON对象'}
        
        if required_fields:
            for field in required_fields:
                if field not in data:
                    return {'valid': False, 'message': f'缺少必需字段: {field}'}
                
                if not data[field] or (isinstance(data[field], str) and not data[field].strip()):
                    return {'valid': False, 'message': f'字段 {field} 不能为空'}
        
        return {'valid': True, 'message': '数据格式正确'}
    
    @staticmethod
    def validate_string_length(value: str, min_length: int = 0, max_length: int = 1000) -> Dict[str, Any]:
        """
        验证字符串长度
        
        Args:
            value: 字符串值
            min_length: 最小长度
            max_length: 最大长度
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        if not isinstance(value, str):
            return {'valid': False, 'message': '值必须是字符串'}
        
        if len(value) < min_length:
            return {'valid': False, 'message': f'长度不能少于{min_length}位'}
        
        if len(value) > max_length:
            return {'valid': False, 'message': f'长度不能超过{max_length}位'}
        
        return {'valid': True, 'message': '长度验证通过'}
    
    @staticmethod
    def validate_no_sql_injection(value: str) -> Dict[str, Any]:
        """
        检查SQL注入攻击
        
        Args:
            value: 要检查的字符串
            
        Returns:
            Dict[str, Any]: 检查结果
        """
        if not isinstance(value, str):
            return {'valid': True, 'message': '非字符串值'}
        
        # SQL注入关键词
        sql_keywords = [
            'select', 'insert', 'update', 'delete', 'drop', 'create',
            'alter', 'exec', 'execute', 'union', 'script', '--', ';',
            'xp_', 'sp_', 'waitfor', 'delay'
        ]
        
        value_lower = value.lower()
        for keyword in sql_keywords:
            if keyword in value_lower:
                return {'valid': False, 'message': f'检测到潜在的SQL注入攻击: {keyword}'}
        
        return {'valid': True, 'message': 'SQL注入检查通过'}
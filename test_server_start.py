#!/usr/bin/env python3
"""
测试服务器启动脚本
用于诊断服务器启动问题
"""

import sys
import os
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        print("  导入 common.config_manager...")
        from common.config_manager import ConfigManager
        print("  √ 成功")
        
        print("  导入 common.log_manager...")
        from common.log_manager import LogManager
        print("  √ 成功")
        
        print("  导入 server.database_manager...")
        from server.database_manager import DatabaseManager
        print("  √ 成功")
        
        print("  导入 server.user_manager...")
        from server.user_manager import UserManager
        print("  √ 成功")
        
        print("  导入 server.session_manager...")
        from server.session_manager import SessionManager
        print("  √ 成功")
        
        print("  导入 server.authentication_handler...")
        from server.authentication_handler import AuthenticationHandler
        print("  √ 成功")
        
        print("  导入 server.socket_listener...")
        from server.socket_listener import SocketListener
        print("  √ 成功")
        
        print("  导入 server.thread_pool_manager...")
        from server.thread_pool_manager import ThreadPoolManager
        print("  √ 成功")
        
        print("  导入 server.protocol_handler...")
        from server.protocol_handler import ProtocolHandler
        print("  √ 成功")
        
        print("  导入 server.security_utils...")
        from server.security_utils import RateLimiter
        print("  √ 成功")
        
        print("所有模块导入成功！")
        return True
        
    except Exception as e:
        print(f"  × 导入失败: {e}")
        traceback.print_exc()
        return False

def test_basic_components():
    """测试基本组件创建"""
    print("\n测试基本组件创建...")
    
    try:
        # 测试配置管理器
        print("  创建配置管理器...")
        from common.config_manager import ConfigManager
        config_manager = ConfigManager("config.ini")
        print("  √ 配置管理器创建成功")
        
        # 测试日志管理器
        print("  创建日志管理器...")
        from common.log_manager import LogManager
        log_config = config_manager.get_section('logging')
        log_manager = LogManager(log_config)
        print("  √ 日志管理器创建成功")
        
        # 测试数据库管理器
        print("  创建数据库管理器...")
        from server.database_manager import DatabaseManager
        db_path = config_manager.get_value('database', 'path', 'data/auth.db')
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        db_manager = DatabaseManager(db_path)
        print("  √ 数据库管理器创建成功")
        
        # 测试用户管理器
        print("  创建用户管理器...")
        from server.user_manager import UserManager
        user_manager = UserManager(db_manager)
        print("  √ 用户管理器创建成功")
        
        # 清理
        log_manager.shutdown()
        
        print("所有基本组件创建成功！")
        return True
        
    except Exception as e:
        print(f"  × 组件创建失败: {e}")
        traceback.print_exc()
        return False

def test_server_application():
    """测试服务器应用程序"""
    print("\n测试服务器应用程序...")
    
    try:
        from server.server_application import ServerApplication
        
        print("  创建服务器应用程序...")
        app = ServerApplication("config.ini")
        print("  √ 服务器应用程序创建成功")
        
        print("  测试启动服务器...")
        start_result = app.start()
        
        if start_result['success']:
            print("  √ 服务器启动成功")
            print(f"    监听地址: {start_result.get('host', 'N/A')}")
            print(f"    监听端口: {start_result.get('port', 'N/A')}")
            
            # 等待一秒钟
            import time
            time.sleep(1)
            
            # 关闭服务器
            print("  关闭服务器...")
            shutdown_result = app.shutdown()
            
            if shutdown_result['success']:
                print("  √ 服务器关闭成功")
                return True
            else:
                print(f"  × 服务器关闭失败: {shutdown_result['message']}")
                return False
        else:
            print(f"  × 服务器启动失败: {start_result['message']}")
            return False
            
    except Exception as e:
        print(f"  × 服务器应用程序测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("网络验证工具服务器启动诊断")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("模块导入", test_imports),
        ("基本组件", test_basic_components),
        ("服务器应用程序", test_server_application)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}测试:")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"√ {test_name}测试通过")
        else:
            print(f"× {test_name}测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！服务器应该可以正常启动。")
        print("\n现在可以运行: python server_main.py")
    else:
        print("❌ 有测试失败，请检查上述错误信息。")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
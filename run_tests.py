#!/usr/bin/env python3
"""
网络验证工具测试运行器
统一运行所有单元测试和集成测试
"""

import sys
import os
import unittest
import argparse
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def discover_and_run_tests(test_directory: str, pattern: str = "test_*.py", verbosity: int = 2):
    """
    发现并运行测试
    
    Args:
        test_directory: 测试目录
        pattern: 测试文件模式
        verbosity: 详细程度
        
    Returns:
        unittest.TestResult: 测试结果
    """
    # 发现测试
    loader = unittest.TestLoader()
    start_dir = project_root / test_directory
    
    if not start_dir.exists():
        print(f"测试目录不存在: {start_dir}")
        return None
    
    suite = loader.discover(str(start_dir), pattern=pattern)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=verbosity, stream=sys.stdout, buffer=True)
    return runner.run(suite)


def run_unit_tests(verbosity: int = 2):
    """
    运行单元测试
    
    Args:
        verbosity: 详细程度
        
    Returns:
        unittest.TestResult: 测试结果
    """
    print("运行单元测试")
    print("=" * 50)
    
    return discover_and_run_tests("tests", "test_*.py", verbosity)


def run_integration_tests(verbosity: int = 2):
    """
    运行集成测试
    
    Args:
        verbosity: 详细程度
        
    Returns:
        unittest.TestResult: 测试结果
    """
    print("运行集成测试")
    print("=" * 50)
    
    # 直接运行集成测试模块
    try:
        from tests.integration_tests import run_integration_tests
        success = run_integration_tests()
        
        # 创建一个模拟的测试结果对象
        class MockResult:
            def __init__(self, success):
                self.wasSuccessful = lambda: success
                self.testsRun = 1 if success else 0
                self.failures = [] if success else [("integration_test", "Failed")]
                self.errors = []
        
        return MockResult(success)
        
    except Exception as e:
        print(f"运行集成测试时发生错误: {e}")
        
        class MockResult:
            def __init__(self):
                self.wasSuccessful = lambda: False
                self.testsRun = 0
                self.failures = []
                self.errors = [("integration_test", str(e))]
        
        return MockResult()


def run_specific_test(test_module: str, verbosity: int = 2):
    """
    运行特定测试模块
    
    Args:
        test_module: 测试模块名
        verbosity: 详细程度
        
    Returns:
        unittest.TestResult: 测试结果
    """
    print(f"运行测试模块: {test_module}")
    print("=" * 50)
    
    try:
        # 导入测试模块
        module = __import__(f"tests.{test_module}", fromlist=[test_module])
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=verbosity)
        return runner.run(suite)
        
    except ImportError as e:
        print(f"无法导入测试模块 {test_module}: {e}")
        return None
    except Exception as e:
        print(f"运行测试模块 {test_module} 时发生错误: {e}")
        return None


def print_test_summary(results: list):
    """
    打印测试摘要
    
    Args:
        results: 测试结果列表
    """
    print("\n" + "=" * 60)
    print("测试摘要")
    print("=" * 60)
    
    total_tests = 0
    total_failures = 0
    total_errors = 0
    successful_suites = 0
    
    for i, (name, result) in enumerate(results):
        if result is None:
            print(f"{i+1}. {name}: 跳过 (模块未找到或错误)")
            continue
        
        tests_run = getattr(result, 'testsRun', 0)
        failures = len(getattr(result, 'failures', []))
        errors = len(getattr(result, 'errors', []))
        success = result.wasSuccessful() if hasattr(result, 'wasSuccessful') else False
        
        total_tests += tests_run
        total_failures += failures
        total_errors += errors
        
        if success:
            successful_suites += 1
            status = "√ 通过"
        else:
            status = "× 失败"
        
        print(f"{i+1}. {name}: {status}")
        print(f"   测试数: {tests_run}, 失败: {failures}, 错误: {errors}")
    
    print("-" * 60)
    print(f"总计:")
    print(f"  测试套件: {len(results)} (成功: {successful_suites})")
    print(f"  测试用例: {total_tests}")
    print(f"  失败: {total_failures}")
    print(f"  错误: {total_errors}")
    
    overall_success = total_failures == 0 and total_errors == 0 and successful_suites > 0
    
    if overall_success:
        print(f"\n🎉 所有测试通过!")
    else:
        print(f"\n❌ 有测试失败，请检查上述输出")
    
    return overall_success


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='网络验证工具测试运行器')
    parser.add_argument('--unit', action='store_true', help='仅运行单元测试')
    parser.add_argument('--integration', action='store_true', help='仅运行集成测试')
    parser.add_argument('--module', type=str, help='运行特定测试模块')
    parser.add_argument('--verbose', '-v', action='count', default=1, help='增加详细程度')
    parser.add_argument('--quiet', '-q', action='store_true', help='安静模式')
    
    args = parser.parse_args()
    
    # 设置详细程度
    if args.quiet:
        verbosity = 0
    else:
        verbosity = min(args.verbose, 2)
    
    print("网络验证工具测试运行器")
    print("=" * 60)
    print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    results = []
    
    try:
        if args.module:
            # 运行特定模块
            result = run_specific_test(args.module, verbosity)
            results.append((f"模块: {args.module}", result))
            
        elif args.unit:
            # 仅运行单元测试
            result = run_unit_tests(verbosity)
            results.append(("单元测试", result))
            
        elif args.integration:
            # 仅运行集成测试
            result = run_integration_tests(verbosity)
            results.append(("集成测试", result))
            
        else:
            # 运行所有测试
            print("运行所有测试...")
            print()
            
            # 单元测试
            unit_result = run_unit_tests(verbosity)
            results.append(("单元测试", unit_result))
            
            print("\n" + "-" * 60 + "\n")
            
            # 集成测试
            integration_result = run_integration_tests(verbosity)
            results.append(("集成测试", integration_result))
        
        # 打印摘要
        overall_success = print_test_summary(results)
        
        return 0 if overall_success else 1
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"运行测试时发生严重错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
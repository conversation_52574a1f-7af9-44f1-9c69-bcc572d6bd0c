# 网络验证工具部署指南

## 目录

1. [部署概述](#部署概述)
2. [环境准备](#环境准备)
3. [单机部署](#单机部署)
4. [网络部署](#网络部署)
5. [生产环境部署](#生产环境部署)
6. [容器化部署](#容器化部署)
7. [安全配置](#安全配置)
8. [监控和维护](#监控和维护)
9. [故障排除](#故障排除)

## 部署概述

网络验证工具支持多种部署方式，从简单的单机测试到复杂的生产环境部署。本指南将详细介绍各种部署场景和最佳实践。

### 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端 1      │    │   客户端 2      │    │   客户端 N      │
│                 │    │                 │    │                 │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      网络验证服务器        │
                    │                           │
                    │  ┌─────────────────────┐  │
                    │  │    用户管理模块     │  │
                    │  └─────────────────────┘  │
                    │  ┌─────────────────────┐  │
                    │  │    会话管理模块     │  │
                    │  └─────────────────────┘  │
                    │  ┌─────────────────────┐  │
                    │  │    数据库模块       │  │
                    │  └─────────────────────┘  │
                    │  ┌─────────────────────┐  │
                    │  │    日志模块         │  │
                    │  └─────────────────────┘  │
                    └───────────────────────────┘
```

## 环境准备

### 系统要求

#### 服务器端
- **CPU**: 2核心或以上
- **内存**: 2GB RAM 或以上
- **存储**: 10GB 可用空间
- **网络**: 稳定的网络连接
- **操作系统**: 
  - Linux (推荐 Ubuntu 20.04 LTS 或 CentOS 8)
  - Windows Server 2016 或以上
  - macOS 10.15 或以上

#### 客户端
- **CPU**: 1核心或以上
- **内存**: 512MB RAM 或以上
- **存储**: 100MB 可用空间
- **网络**: 能够访问服务器的网络连接

### 软件依赖

#### Python环境
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip python3-venv python3-tk

# CentOS/RHEL
sudo yum install python3 python3-pip python3-tkinter

# Windows
# 从 https://python.org 下载并安装 Python 3.7+

# macOS
brew install python-tk
```

#### 可选依赖
```bash
pip3 install psutil cryptography
```

### 网络配置

#### 防火墙设置
```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 8888/tcp
sudo ufw enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=8888/tcp
sudo firewall-cmd --reload

# Windows
# 在 Windows 防火墙中添加入站规则，允许端口 8888
```

## 单机部署

适用于开发、测试或小规模使用场景。

### 快速部署

1. **下载和解压**
   ```bash
   # 假设已下载项目文件
   cd network-auth-tool
   ```

2. **运行安装脚本**
   ```bash
   python3 install.py
   ```

3. **启动服务器**
   ```bash
   python3 server_main.py
   ```

4. **启动客户端**
   ```bash
   python3 client_main.py
   ```

### 手动部署步骤

1. **创建项目目录**
   ```bash
   mkdir -p /opt/network-auth-tool
   cd /opt/network-auth-tool
   ```

2. **复制项目文件**
   ```bash
   # 复制所有项目文件到目标目录
   cp -r /path/to/source/* /opt/network-auth-tool/
   ```

3. **设置权限**
   ```bash
   chmod +x start_server.sh start_client.sh
   chown -R $USER:$USER /opt/network-auth-tool
   ```

4. **创建虚拟环境**
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

5. **初始化配置**
   ```bash
   python3 install.py
   ```

## 网络部署

适用于多用户、跨网络的使用场景。

### 服务器部署

1. **选择服务器**
   - 云服务器 (AWS EC2, 阿里云ECS, 腾讯云CVM等)
   - 物理服务器
   - 虚拟机

2. **配置服务器**
   ```bash
   # 更新系统
   sudo apt update && sudo apt upgrade -y
   
   # 安装必要软件
   sudo apt install python3 python3-pip python3-venv git -y
   
   # 创建专用用户
   sudo useradd -m -s /bin/bash netauth
   sudo usermod -aG sudo netauth
   ```

3. **部署应用**
   ```bash
   # 切换到专用用户
   sudo su - netauth
   
   # 下载项目
   git clone <repository-url> network-auth-tool
   cd network-auth-tool
   
   # 创建虚拟环境
   python3 -m venv venv
   source venv/bin/activate
   
   # 安装依赖
   pip install -r requirements.txt
   
   # 运行安装脚本
   python install.py
   ```

4. **配置网络访问**
   ```ini
   # 编辑 config.ini
   [server]
   host = 0.0.0.0  # 监听所有接口
   port = 8888
   max_connections = 100
   ```

5. **启动服务**
   ```bash
   # 前台运行（测试用）
   python server_main.py
   
   # 后台运行
   nohup python server_main.py > server.log 2>&1 &
   ```

### 客户端配置

1. **修改客户端配置**
   ```ini
   # client_config.ini
   [client]
   default_host = 服务器IP地址
   default_port = 8888
   connection_timeout = 10
   ```

2. **分发客户端**
   - 打包客户端文件
   - 分发到各个客户端机器
   - 配置服务器连接信息

## 生产环境部署

适用于正式的生产环境，需要考虑高可用性、安全性和性能。

### 系统服务配置

1. **创建systemd服务文件**
   ```bash
   sudo nano /etc/systemd/system/network-auth-server.service
   ```

   ```ini
   [Unit]
   Description=Network Authentication Server
   After=network.target
   
   [Service]
   Type=simple
   User=netauth
   Group=netauth
   WorkingDirectory=/opt/network-auth-tool
   Environment=PATH=/opt/network-auth-tool/venv/bin
   ExecStart=/opt/network-auth-tool/venv/bin/python server_main.py
   Restart=always
   RestartSec=10
   
   [Install]
   WantedBy=multi-user.target
   ```

2. **启用和启动服务**
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable network-auth-server
   sudo systemctl start network-auth-server
   sudo systemctl status network-auth-server
   ```

### 反向代理配置

使用Nginx作为反向代理（可选）：

1. **安装Nginx**
   ```bash
   sudo apt install nginx -y
   ```

2. **配置Nginx**
   ```bash
   sudo nano /etc/nginx/sites-available/network-auth
   ```

   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://127.0.0.1:8888;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

3. **启用配置**
   ```bash
   sudo ln -s /etc/nginx/sites-available/network-auth /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

### SSL/TLS配置

1. **获取SSL证书**
   ```bash
   # 使用Let's Encrypt
   sudo apt install certbot python3-certbot-nginx -y
   sudo certbot --nginx -d your-domain.com
   ```

2. **配置HTTPS重定向**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       return 301 https://$server_name$request_uri;
   }
   
   server {
       listen 443 ssl;
       server_name your-domain.com;
       
       ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
       ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
       
       location / {
           proxy_pass http://127.0.0.1:8888;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

## 容器化部署

使用Docker进行容器化部署。

### Dockerfile

```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    python3-tk \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY . .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 创建必要目录
RUN mkdir -p data logs config backup

# 设置权限
RUN chmod +x start_server.sh

# 暴露端口
EXPOSE 8888

# 运行安装脚本
RUN python install.py

# 启动命令
CMD ["python", "server_main.py"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  network-auth-server:
    build: .
    ports:
      - "8888:8888"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - network-auth-server
    restart: unless-stopped
```

### 构建和运行

```bash
# 构建镜像
docker build -t network-auth-tool .

# 运行容器
docker run -d \
  --name network-auth-server \
  -p 8888:8888 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  network-auth-tool

# 使用Docker Compose
docker-compose up -d
```

## 安全配置

### 网络安全

1. **防火墙配置**
   ```bash
   # 只允许必要的端口
   sudo ufw default deny incoming
   sudo ufw default allow outgoing
   sudo ufw allow ssh
   sudo ufw allow 8888/tcp
   sudo ufw enable
   ```

2. **IP白名单**
   ```ini
   # config.ini
   [security]
   allowed_ips = ***********/24,10.0.0.0/8
   ```

### 应用安全

1. **强密码策略**
   ```ini
   [security]
   password_min_length = 8
   password_require_uppercase = true
   password_require_lowercase = true
   password_require_numbers = true
   password_require_symbols = true
   ```

2. **会话安全**
   ```ini
   [security]
   session_timeout = 1800  # 30分钟
   max_failed_attempts = 3
   lockout_duration = 3600  # 1小时
   ```

3. **日志审计**
   ```ini
   [logging]
   level = INFO
   audit_enabled = true
   audit_file = logs/audit.log
   ```

### 数据库安全

1. **文件权限**
   ```bash
   chmod 600 data/auth.db
   chown netauth:netauth data/auth.db
   ```

2. **定期备份**
   ```bash
   # 创建备份脚本
   cat > backup.sh << 'EOF'
   #!/bin/bash
   DATE=$(date +%Y%m%d_%H%M%S)
   cp data/auth.db backup/auth_${DATE}.db
   find backup/ -name "auth_*.db" -mtime +7 -delete
   EOF
   
   chmod +x backup.sh
   
   # 添加到crontab
   crontab -e
   # 添加行: 0 2 * * * /opt/network-auth-tool/backup.sh
   ```

## 监控和维护

### 系统监控

1. **性能监控脚本**
   ```bash
   cat > monitor.sh << 'EOF'
   #!/bin/bash
   
   # 检查服务状态
   systemctl is-active network-auth-server
   
   # 检查端口监听
   netstat -tlnp | grep :8888
   
   # 检查资源使用
   ps aux | grep server_main.py
   
   # 检查日志错误
   tail -n 100 logs/server.log | grep ERROR
   EOF
   
   chmod +x monitor.sh
   ```

2. **自动重启脚本**
   ```bash
   cat > health_check.sh << 'EOF'
   #!/bin/bash
   
   if ! systemctl is-active --quiet network-auth-server; then
       echo "$(date): Service is down, restarting..." >> logs/health_check.log
       systemctl restart network-auth-server
   fi
   EOF
   
   chmod +x health_check.sh
   
   # 添加到crontab，每5分钟检查一次
   # */5 * * * * /opt/network-auth-tool/health_check.sh
   ```

### 日志管理

1. **日志轮转配置**
   ```bash
   sudo nano /etc/logrotate.d/network-auth
   ```

   ```
   /opt/network-auth-tool/logs/*.log {
       daily
       missingok
       rotate 30
       compress
       delaycompress
       notifempty
       copytruncate
   }
   ```

2. **日志分析脚本**
   ```bash
   cat > log_analysis.sh << 'EOF'
   #!/bin/bash
   
   echo "=== 今日登录统计 ==="
   grep "$(date +%Y-%m-%d)" logs/server.log | grep "USER_LOGIN" | wc -l
   
   echo "=== 今日错误统计 ==="
   grep "$(date +%Y-%m-%d)" logs/server.log | grep "ERROR" | wc -l
   
   echo "=== 活跃用户 ==="
   grep "$(date +%Y-%m-%d)" logs/server.log | grep "USER_LOGIN" | awk '{print $NF}' | sort | uniq -c
   EOF
   
   chmod +x log_analysis.sh
   ```

### 性能优化

1. **定期优化脚本**
   ```bash
   cat > optimize.sh << 'EOF'
   #!/bin/bash
   
   echo "$(date): 开始系统优化"
   
   # 运行系统优化工具
   python tools/system_optimizer.py --report --output optimization_$(date +%Y%m%d).txt
   
   # 清理临时文件
   find /tmp -name "*.tmp" -mtime +1 -delete
   
   # 压缩旧日志
   find logs/ -name "*.log" -mtime +7 -exec gzip {} \;
   
   echo "$(date): 系统优化完成"
   EOF
   
   chmod +x optimize.sh
   
   # 每周运行一次
   # 0 2 * * 0 /opt/network-auth-tool/optimize.sh
   ```

## 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查服务状态
   systemctl status network-auth-server
   
   # 查看详细日志
   journalctl -u network-auth-server -f
   
   # 检查端口占用
   netstat -tlnp | grep 8888
   ```

2. **客户端连接失败**
   ```bash
   # 检查网络连通性
   telnet server-ip 8888
   
   # 检查防火墙
   sudo ufw status
   
   # 检查服务器日志
   tail -f logs/server.log
   ```

3. **性能问题**
   ```bash
   # 检查系统资源
   top
   free -h
   df -h
   
   # 运行性能分析
   python tools/system_optimizer.py
   ```

### 恢复程序

1. **数据库恢复**
   ```bash
   # 停止服务
   systemctl stop network-auth-server
   
   # 恢复数据库
   cp backup/auth_YYYYMMDD_HHMMSS.db data/auth.db
   
   # 启动服务
   systemctl start network-auth-server
   ```

2. **配置恢复**
   ```bash
   # 恢复默认配置
   cp config.ini.example config.ini
   
   # 重新运行安装脚本
   python install.py
   ```

3. **完全重新部署**
   ```bash
   # 备份数据
   cp -r data backup_$(date +%Y%m%d)
   
   # 重新部署
   git pull origin main
   python install.py
   
   # 恢复数据
   cp backup_$(date +%Y%m%d)/auth.db data/
   ```

---

## 部署检查清单

### 部署前检查
- [ ] 系统要求满足
- [ ] 网络配置正确
- [ ] 防火墙规则设置
- [ ] SSL证书准备（如需要）
- [ ] 备份策略制定

### 部署后验证
- [ ] 服务正常启动
- [ ] 端口正常监听
- [ ] 客户端能够连接
- [ ] 用户认证功能正常
- [ ] 日志记录正常
- [ ] 监控系统工作
- [ ] 备份脚本测试

### 安全检查
- [ ] 默认密码已修改
- [ ] 不必要的端口已关闭
- [ ] 日志审计已启用
- [ ] 文件权限正确设置
- [ ] 定期备份已配置

---

*网络验证工具 v1.0.0 - 部署指南*
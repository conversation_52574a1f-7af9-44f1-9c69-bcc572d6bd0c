#!/usr/bin/env python3
"""
网络验证工具安装脚本
自动化安装和配置网络验证工具
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path


class NetworkAuthInstaller:
    """网络验证工具安装器"""
    
    def __init__(self):
        """初始化安装器"""
        self.project_root = Path(__file__).parent
        self.install_steps = []
        self.errors = []
        
    def check_python_version(self):
        """检查Python版本"""
        print("检查Python版本...")
        
        if sys.version_info < (3, 7):
            self.errors.append("需要Python 3.7或更高版本")
            return False
        
        print(f"√ Python版本: {sys.version}")
        return True
    
    def check_dependencies(self):
        """检查依赖项"""
        print("检查依赖项...")
        
        required_packages = [
            'tkinter',
            'sqlite3',
            'socket',
            'threading',
            'json',
            'hashlib',
            'datetime'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"√ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"× {package} (缺失)")
        
        # 检查可选依赖
        optional_packages = {
            'psutil': '系统监控功能',
            'cryptography': '高级加密功能'
        }
        
        for package, description in optional_packages.items():
            try:
                __import__(package)
                print(f"√ {package} ({description})")
            except ImportError:
                print(f"? {package} (可选，用于{description})")
        
        if missing_packages:
            self.errors.append(f"缺少必需的包: {', '.join(missing_packages)}")
            return False
        
        return True
    
    def install_optional_dependencies(self):
        """安装可选依赖项"""
        print("安装可选依赖项...")
        
        optional_packages = ['psutil']
        
        for package in optional_packages:
            try:
                print(f"安装 {package}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"√ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"? {package} 安装失败（可选包，不影响基本功能）")
    
    def create_directories(self):
        """创建必要的目录"""
        print("创建目录结构...")
        
        directories = [
            'data',
            'logs',
            'config',
            'backup'
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            try:
                dir_path.mkdir(exist_ok=True)
                print(f"√ 创建目录: {directory}")
            except Exception as e:
                self.errors.append(f"创建目录 {directory} 失败: {e}")
                return False
        
        return True
    
    def create_config_files(self):
        """创建配置文件"""
        print("创建配置文件...")
        
        # 服务器配置
        server_config = """[server]
host = 0.0.0.0
port = 8888
max_connections = 50

[database]
path = data/auth.db

[logging]
level = INFO
file_path = logs/server.log
max_file_size = 5242880
backup_count = 3

[security]
session_timeout = 3600
max_failed_attempts = 5
lockout_duration = 1800
password_min_length = 6
"""
        
        # 客户端配置
        client_config = """[client]
default_host = 127.0.0.1
default_port = 8888
connection_timeout = 10
receive_timeout = 30
auto_reconnect = true
max_retry_attempts = 3

[logging]
level = INFO
file_path = logs/client.log
max_file_size = 5242880
backup_count = 3

[ui]
window_width = 500
window_height = 600
theme = default
auto_save_settings = true
"""
        
        try:
            # 写入服务器配置
            with open(self.project_root / 'config.ini', 'w', encoding='utf-8') as f:
                f.write(server_config)
            print("√ 创建服务器配置文件: config.ini")
            
            # 写入客户端配置
            with open(self.project_root / 'client_config.ini', 'w', encoding='utf-8') as f:
                f.write(client_config)
            print("√ 创建客户端配置文件: client_config.ini")
            
            return True
            
        except Exception as e:
            self.errors.append(f"创建配置文件失败: {e}")
            return False
    
    def initialize_database(self):
        """初始化数据库"""
        print("初始化数据库...")
        
        try:
            # 导入数据库管理器
            sys.path.insert(0, str(self.project_root))
            from server.database_manager import DatabaseManager
            
            # 创建数据库
            db_path = self.project_root / 'data' / 'auth.db'
            db_manager = DatabaseManager(str(db_path))
            
            # 数据库会在初始化时自动创建表
            print("√ 数据库初始化完成")
            return True
            
        except Exception as e:
            self.errors.append(f"数据库初始化失败: {e}")
            return False
    
    def create_startup_scripts(self):
        """创建启动脚本"""
        print("创建启动脚本...")
        
        # Windows批处理脚本
        windows_server_script = """@echo off
echo 启动网络验证工具服务器...
python server_main.py
pause
"""
        
        windows_client_script = """@echo off
echo 启动网络验证工具客户端...
python client_main.py
pause
"""
        
        # Linux/Mac shell脚本
        unix_server_script = """#!/bin/bash
echo "启动网络验证工具服务器..."
python3 server_main.py
"""
        
        unix_client_script = """#!/bin/bash
echo "启动网络验证工具客户端..."
python3 client_main.py
"""
        
        try:
            # Windows脚本
            with open(self.project_root / 'start_server.bat', 'w', encoding='utf-8') as f:
                f.write(windows_server_script)
            
            with open(self.project_root / 'start_client.bat', 'w', encoding='utf-8') as f:
                f.write(windows_client_script)
            
            # Unix脚本
            with open(self.project_root / 'start_server.sh', 'w', encoding='utf-8') as f:
                f.write(unix_server_script)
            
            with open(self.project_root / 'start_client.sh', 'w', encoding='utf-8') as f:
                f.write(unix_client_script)
            
            # 设置Unix脚本执行权限
            if os.name != 'nt':  # 非Windows系统
                os.chmod(self.project_root / 'start_server.sh', 0o755)
                os.chmod(self.project_root / 'start_client.sh', 0o755)
            
            print("√ 创建启动脚本")
            return True
            
        except Exception as e:
            self.errors.append(f"创建启动脚本失败: {e}")
            return False
    
    def run_tests(self):
        """运行基本测试"""
        print("运行基本测试...")
        
        try:
            # 测试导入主要模块
            sys.path.insert(0, str(self.project_root))
            
            test_modules = [
                'common.config_manager',
                'common.log_manager',
                'server.database_manager',
                'server.user_manager',
                'client.network_client'
            ]
            
            for module in test_modules:
                try:
                    __import__(module)
                    print(f"√ 模块测试通过: {module}")
                except ImportError as e:
                    print(f"× 模块测试失败: {module} - {e}")
                    self.errors.append(f"模块 {module} 导入失败")
            
            return len(self.errors) == 0
            
        except Exception as e:
            self.errors.append(f"测试运行失败: {e}")
            return False
    
    def create_uninstall_script(self):
        """创建卸载脚本"""
        print("创建卸载脚本...")
        
        uninstall_script = """#!/usr/bin/env python3
\"\"\"
网络验证工具卸载脚本
\"\"\"

import os
import shutil
from pathlib import Path

def uninstall():
    project_root = Path(__file__).parent
    
    print("卸载网络验证工具...")
    
    # 删除数据目录（询问用户）
    data_dir = project_root / 'data'
    if data_dir.exists():
        response = input("是否删除数据目录（包含用户数据）？(y/N): ")
        if response.lower() == 'y':
            shutil.rmtree(data_dir)
            print("√ 删除数据目录")
        else:
            print("保留数据目录")
    
    # 删除日志目录
    logs_dir = project_root / 'logs'
    if logs_dir.exists():
        shutil.rmtree(logs_dir)
        print("√ 删除日志目录")
    
    # 删除配置文件
    config_files = ['config.ini', 'client_config.ini']
    for config_file in config_files:
        config_path = project_root / config_file
        if config_path.exists():
            os.remove(config_path)
            print(f"√ 删除配置文件: {config_file}")
    
    # 删除启动脚本
    script_files = ['start_server.bat', 'start_client.bat', 'start_server.sh', 'start_client.sh']
    for script_file in script_files:
        script_path = project_root / script_file
        if script_path.exists():
            os.remove(script_path)
            print(f"√ 删除启动脚本: {script_file}")
    
    print("卸载完成")

if __name__ == "__main__":
    uninstall()
"""
        
        try:
            with open(self.project_root / 'uninstall.py', 'w', encoding='utf-8') as f:
                f.write(uninstall_script)
            print("√ 创建卸载脚本")
            return True
            
        except Exception as e:
            self.errors.append(f"创建卸载脚本失败: {e}")
            return False
    
    def install(self):
        """执行完整安装"""
        print("网络验证工具安装程序")
        print("=" * 50)
        
        # 安装步骤
        steps = [
            ("检查Python版本", self.check_python_version),
            ("检查依赖项", self.check_dependencies),
            ("安装可选依赖", self.install_optional_dependencies),
            ("创建目录结构", self.create_directories),
            ("创建配置文件", self.create_config_files),
            ("初始化数据库", self.initialize_database),
            ("创建启动脚本", self.create_startup_scripts),
            ("运行基本测试", self.run_tests),
            ("创建卸载脚本", self.create_uninstall_script)
        ]
        
        success_count = 0
        
        for step_name, step_func in steps:
            print(f"\n{step_name}...")
            try:
                if step_func():
                    success_count += 1
                else:
                    print(f"× {step_name} 失败")
            except Exception as e:
                print(f"× {step_name} 发生错误: {e}")
                self.errors.append(f"{step_name}: {e}")
        
        print("\n" + "=" * 50)
        print("安装完成")
        print(f"成功步骤: {success_count}/{len(steps)}")
        
        if self.errors:
            print("\n错误列表:")
            for error in self.errors:
                print(f"- {error}")
            return False
        else:
            print("\n√ 安装成功！")
            print("\n使用说明:")
            print("- 启动服务器: python server_main.py 或运行 start_server.bat/.sh")
            print("- 启动客户端: python client_main.py 或运行 start_client.bat/.sh")
            print("- 默认管理员账户: admin/admin123 (请及时修改密码)")
            print("- 配置文件: config.ini (服务器) 和 client_config.ini (客户端)")
            return True


def main():
    """主函数"""
    try:
        installer = NetworkAuthInstaller()
        success = installer.install()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n安装被用户中断")
        return 1
    except Exception as e:
        print(f"安装过程中发生严重错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
#!/usr/bin/env python3
"""
诊断注册问题
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_manager import DatabaseManager
from server.user_registration_manager import UserRegistrationManager


def diagnose_registration_issue():
    """诊断注册问题"""
    print("=" * 60)
    print("诊断用户注册问题")
    print("=" * 60)
    
    try:
        # 检查主数据库
        print("\n1. 检查主数据库...")
        
        db_path = "data/auth.db"
        if not os.path.exists(db_path):
            print(f"✗ 主数据库不存在: {db_path}")
            return False
        
        print(f"✓ 主数据库存在: {db_path}")
        
        # 初始化数据库管理器
        print("\n2. 初始化数据库管理器...")
        
        try:
            db_manager = DatabaseManager(db_path)
            print("✓ 数据库管理器初始化成功")
        except Exception as e:
            print(f"✗ 数据库管理器初始化失败: {e}")
            return False
        
        # 检查数据库表结构
        print("\n3. 检查数据库表结构...")
        
        try:
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查users表结构
                cursor.execute("PRAGMA table_info(users)")
                columns = cursor.fetchall()
                
                print("users表字段:")
                required_columns = ['username', 'password_hash', 'balance', 'email', 'registration_date', 'status']
                existing_columns = [col[1] for col in columns]
                
                for col in columns:
                    print(f"  - {col[1]} ({col[2]})")
                
                # 检查必需字段
                missing_columns = []
                for req_col in required_columns:
                    if req_col not in existing_columns:
                        missing_columns.append(req_col)
                
                if missing_columns:
                    print(f"✗ 缺少必需字段: {missing_columns}")
                    return False
                else:
                    print("✓ 所有必需字段都存在")
                
        except Exception as e:
            print(f"✗ 检查表结构失败: {e}")
            return False
        
        # 测试注册管理器
        print("\n4. 测试注册管理器...")
        
        try:
            registration_manager = UserRegistrationManager(db_manager)
            print("✓ 注册管理器初始化成功")
        except Exception as e:
            print(f"✗ 注册管理器初始化失败: {e}")
            return False
        
        # 测试用户名检查
        print("\n5. 测试用户名可用性检查...")
        
        try:
            test_username = "testuser_debug"
            is_available = registration_manager.check_username_availability(test_username)
            print(f"✓ 用户名 '{test_username}' 可用性: {'可用' if is_available else '已存在'}")
        except Exception as e:
            print(f"✗ 用户名检查失败: {e}")
            return False
        
        # 测试注册验证
        print("\n6. 测试注册数据验证...")
        
        try:
            validation_errors = registration_manager.validate_registration_data(
                username="testuser123",
                password="testpass123",
                confirm_password="testpass123",
                email="<EMAIL>"
            )
            
            if validation_errors:
                print(f"验证错误: {validation_errors}")
            else:
                print("✓ 注册数据验证通过")
        except Exception as e:
            print(f"✗ 注册验证失败: {e}")
            return False
        
        # 尝试实际注册
        print("\n7. 尝试实际注册...")
        
        try:
            test_username = "debug_test_user"
            
            # 先检查用户是否已存在
            existing_user = db_manager.get_user_info(test_username)
            if existing_user:
                print(f"用户 {test_username} 已存在，先删除...")
                db_manager.delete_user(test_username)
            
            # 执行注册
            result = registration_manager.register_user(
                username=test_username,
                password="testpass123",
                confirm_password="testpass123",
                email="<EMAIL>"
            )
            
            if result['success']:
                print(f"✓ 注册成功: {result['message']}")
                
                # 验证用户是否真的创建了
                user_info = db_manager.get_user_info(test_username)
                if user_info:
                    print(f"✓ 用户信息验证成功: {user_info}")
                else:
                    print("✗ 用户信息验证失败")
                
                # 清理测试用户
                db_manager.delete_user(test_username)
                print(f"✓ 已清理测试用户")
                
            else:
                print(f"✗ 注册失败: {result['message']}")
                if 'errors' in result:
                    for error in result['errors']:
                        print(f"    - {error}")
                return False
                
        except Exception as e:
            print(f"✗ 注册测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n" + "=" * 60)
        print("✅ 诊断完成 - 注册功能正常")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_server_status():
    """检查服务器状态"""
    print("\n" + "=" * 60)
    print("检查服务器状态")
    print("=" * 60)
    
    try:
        from server.server_application import ServerApplication
        
        # 检查配置文件
        config_file = "config.ini"
        if not os.path.exists(config_file):
            print(f"✗ 配置文件不存在: {config_file}")
            return False
        
        print(f"✓ 配置文件存在: {config_file}")
        
        # 尝试初始化服务器应用
        try:
            server_app = ServerApplication(config_file)
            print("✓ 服务器应用初始化成功")
            
            # 检查服务器是否运行
            if server_app.is_server_running():
                print("✓ 服务器正在运行")
                
                # 获取服务器信息
                server_info = server_app.get_server_info()
                print(f"服务器信息: {server_info}")
                
            else:
                print("! 服务器未运行")
                
        except Exception as e:
            print(f"✗ 服务器应用初始化失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 检查服务器状态失败: {e}")
        return False


def main():
    """主函数"""
    print("开始诊断注册问题...")
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 诊断注册功能
    registration_ok = diagnose_registration_issue()
    
    # 检查服务器状态
    server_ok = check_server_status()
    
    print(f"\n诊断结果:")
    print(f"  - 注册功能: {'正常' if registration_ok else '异常'}")
    print(f"  - 服务器状态: {'正常' if server_ok else '异常'}")
    
    if not registration_ok:
        print("\n建议:")
        print("1. 检查数据库文件是否存在且可访问")
        print("2. 确认数据库迁移是否正确执行")
        print("3. 检查用户权限和文件系统权限")
        print("4. 查看详细错误日志")
    
    return 0 if (registration_ok and server_ok) else 1


if __name__ == "__main__":
    sys.exit(main())
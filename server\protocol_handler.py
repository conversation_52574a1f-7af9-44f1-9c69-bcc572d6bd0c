"""
通信协议处理模块
实现JSON消息解析、生成和协议错误处理
"""

import json
import socket
from typing import Dict, Any, Optional, List
from datetime import datetime

from server.user_manager import UserManager
from server.session_manager import SessionManager
from common.constants import MESSAGE_TYPES, AUTH_STATUS
from common.exceptions import NetworkError, AuthenticationError


class ProtocolHandler:
    """通信协议处理器"""
    
    def __init__(self, user_manager: UserManager, session_manager: SessionManager):
        """
        初始化协议处理器
        
        Args:
            user_manager: 用户管理器
            session_manager: 会话管理器
        """
        self.user_manager = user_manager
        self.session_manager = session_manager
        
        # 初始化卡密管理器
        from server.recharge_card_manager import RechargeCardManager
        self.card_manager = RechargeCardManager(self.user_manager.db_manager)
        
        # 消息处理器映射
        self.message_handlers = {
            MESSAGE_TYPES['AUTH_REQUEST']: self._handle_auth_request,
            MESSAGE_TYPES['HEARTBEAT']: self._handle_heartbeat,
            'REGISTER': self._handle_register_request,
            'USE_CARD': self._handle_use_card_request,
            'GET_TIME': self._handle_get_time_request,
        }
    
    def process_message(self, conn_id: str, message: Dict[str, Any], client_ip: str = None) -> Dict[str, Any]:
        """
        处理客户端消息
        
        Args:
            conn_id: 连接ID
            message: 客户端消息
            client_ip: 客户端IP地址
            
        Returns:
            Dict[str, Any]: 响应消息
        """
        try:
            # 验证消息格式
            validation_result = self._validate_message(message)
            if not validation_result['valid']:
                return self._create_error_response(validation_result['message'])
            
            # 获取消息类型
            message_type = message.get('type')
            
            # 查找对应的处理器
            handler = self.message_handlers.get(message_type)
            if not handler:
                return self._create_error_response(f'不支持的消息类型: {message_type}')
            
            # 处理消息
            response = handler(conn_id, message, client_ip)
            
            # 添加时间戳
            response['timestamp'] = datetime.now().isoformat()
            
            return response
            
        except Exception as e:
            return self._create_error_response(f'处理消息时发生错误: {str(e)}')
    
    def _validate_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证消息格式
        
        Args:
            message: 要验证的消息
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        # 检查是否为字典
        if not isinstance(message, dict):
            return {'valid': False, 'message': '消息必须是JSON对象'}
        
        # 检查必需字段
        if 'type' not in message:
            return {'valid': False, 'message': '消息缺少type字段'}
        
        message_type = message['type']
        
        # 根据消息类型验证特定字段
        if message_type == MESSAGE_TYPES['AUTH_REQUEST']:
            required_fields = ['username', 'password']
            for field in required_fields:
                if field not in message:
                    return {'valid': False, 'message': f'认证请求缺少{field}字段'}
                if not isinstance(message[field], str) or not message[field].strip():
                    return {'valid': False, 'message': f'{field}字段不能为空'}
        
        elif message_type == MESSAGE_TYPES['HEARTBEAT']:
            # 心跳消息只需要type字段
            pass
        
        else:
            # 未知消息类型在process_message中处理
            pass
        
        return {'valid': True, 'message': '消息格式正确'}
    
    def _handle_auth_request(self, conn_id: str, message: Dict[str, Any], client_ip: str = None) -> Dict[str, Any]:
        """
        处理认证请求
        
        Args:
            conn_id: 连接ID
            message: 认证请求消息
            client_ip: 客户端IP地址
            
        Returns:
            Dict[str, Any]: 认证响应
        """
        username = message['username'].strip()
        password = message['password']
        
        # 记录认证尝试
        print(f"用户 {username} 从 {client_ip} 尝试认证 (连接ID: {conn_id})")
        
        # 执行用户认证
        auth_result = self.user_manager.authenticate_user(username, password, client_ip)
        
        if auth_result['success']:
            # 认证成功，创建会话
            session_result = self.session_manager.create_session(
                auth_result['user_id'], 
                client_ip
            )
            
            if session_result['success']:
                response = {
                    'type': MESSAGE_TYPES['AUTH_RESPONSE'],
                    'success': True,
                    'status': AUTH_STATUS['SUCCESS'],
                    'message': '认证成功',
                    'session_token': session_result['session_token'],
                    'expires_at': session_result['expires_at'],
                    'user_info': {
                        'username': auth_result['username'],
                        'user_id': auth_result['user_id'],
                        'last_login': auth_result.get('last_login')
                    }
                }
                
                print(f"用户 {username} 认证成功，会话令牌: {session_result['session_token'][:8]}...")
                
            else:
                # 会话创建失败
                response = {
                    'type': MESSAGE_TYPES['AUTH_RESPONSE'],
                    'success': False,
                    'status': AUTH_STATUS['FAILED'],
                    'message': f'认证成功但创建会话失败: {session_result["message"]}'
                }
                
                print(f"用户 {username} 认证成功但创建会话失败")
        
        else:
            # 认证失败
            status = AUTH_STATUS['LOCKED'] if auth_result.get('locked') else AUTH_STATUS['FAILED']
            
            response = {
                'type': MESSAGE_TYPES['AUTH_RESPONSE'],
                'success': False,
                'status': status,
                'message': auth_result['message']
            }
            
            # 添加额外信息
            if 'remaining_attempts' in auth_result:
                response['remaining_attempts'] = auth_result['remaining_attempts']
            
            if 'remaining_time' in auth_result:
                response['remaining_time'] = auth_result['remaining_time']
            
            print(f"用户 {username} 认证失败: {auth_result['message']}")
        
        return response
    
    def _handle_heartbeat(self, conn_id: str, message: Dict[str, Any], client_ip: str = None) -> Dict[str, Any]:
        """
        处理心跳消息
        
        Args:
            conn_id: 连接ID
            message: 心跳消息
            client_ip: 客户端IP地址
            
        Returns:
            Dict[str, Any]: 心跳响应
        """
        # 检查是否包含会话令牌
        session_token = message.get('session_token')
        
        response = {
            'type': MESSAGE_TYPES['HEARTBEAT'],
            'success': True,
            'message': 'pong',
            'server_time': datetime.now().isoformat()
        }
        
        # 如果有会话令牌，验证并刷新会话
        if session_token:
            session_validation = self.session_manager.validate_session(session_token)
            
            if session_validation['valid']:
                # 刷新会话
                refresh_result = self.session_manager.refresh_session(session_token)
                if refresh_result['success']:
                    response['session_refreshed'] = True
                    response['expires_at'] = refresh_result['expires_at']
                else:
                    response['session_refreshed'] = False
                    response['session_message'] = refresh_result['message']
            else:
                response['session_valid'] = False
                response['session_message'] = session_validation['message']
        
        return response
    
    def _handle_register_request(self, conn_id: str, message: Dict[str, Any], client_ip: str = None) -> Dict[str, Any]:
        """
        处理用户注册请求
        
        Args:
            conn_id: 连接ID
            message: 注册请求消息
            client_ip: 客户端IP地址
            
        Returns:
            Dict[str, Any]: 注册响应
        """
        try:
            username = message.get('username', '').strip()
            password = message.get('password', '')
            email = message.get('email')
            if email:
                email = email.strip() or None
            else:
                email = None
            
            # 记录注册尝试
            print(f"用户 {username} 从 {client_ip} 尝试注册 (连接ID: {conn_id})")
            
            # 检查用户管理器是否支持注册功能
            if not hasattr(self.user_manager, 'register_user'):
                return {
                    'type': 'REGISTER_RESPONSE',
                    'success': False,
                    'message': '服务器不支持用户注册功能'
                }
            
            # 执行用户注册
            register_result = self.user_manager.register_user(username, password, email, client_ip)
            
            if register_result['success']:
                response = {
                    'type': 'REGISTER_RESPONSE',
                    'success': True,
                    'message': register_result.get('message', '注册成功'),
                    'username': username
                }
                
                print(f"用户 {username} 注册成功")
                
            else:
                response = {
                    'type': 'REGISTER_RESPONSE',
                    'success': False,
                    'message': register_result.get('message', '注册失败'),
                    'errors': register_result.get('errors', [])
                }
                
                print(f"用户 {username} 注册失败: {register_result.get('message', '未知错误')}")
            
            return response
            
        except Exception as e:
            print(f"处理注册请求时发生错误: {e}")
            return {
                'type': 'REGISTER_RESPONSE',
                'success': False,
                'message': f'注册过程中发生错误: {str(e)}'
            }
    
    def _handle_use_card_request(self, conn_id: str, message: Dict[str, Any], client_ip: str = None) -> Dict[str, Any]:
        """
        处理使用充值卡请求
        
        Args:
            conn_id: 连接ID
            message: 消息数据
            client_ip: 客户端IP
            
        Returns:
            Dict: 使用卡密响应
        """
        try:
            # 验证会话
            session_info = self.session_manager.get_session_by_connection(conn_id)
            if not session_info:
                return {
                    'type': 'USE_CARD_RESPONSE',
                    'success': False,
                    'message': '请先登录'
                }
            
            username = session_info.get('username')
            card_code = message.get('card_code', '').strip()
            
            if not card_code:
                return {
                    'type': 'USE_CARD_RESPONSE',
                    'success': False,
                    'message': '卡密不能为空'
                }
            
            print(f"用户 {username} 尝试使用卡密: {card_code} (连接ID: {conn_id})")
            
            # 使用卡密
            result = self.card_manager.use_card(card_code, username)
            
            response = {
                'type': 'USE_CARD_RESPONSE',
                'success': result['success'],
                'message': result['message']
            }
            
            if result['success']:
                response.update({
                    'card_type': result.get('card_type'),
                    'card_name': result.get('card_name'),
                    'duration_hours': result.get('duration_hours'),
                    'expires_at': result.get('expires_at'),
                    'added_time': result.get('added_time')
                })
                print(f"用户 {username} 成功使用卡密: {card_code}")
            else:
                response['error'] = result.get('error')
                print(f"用户 {username} 使用卡密失败: {result['message']}")
            
            return response
            
        except Exception as e:
            print(f"处理使用卡密请求时出错: {e}")
            return {
                'type': 'USE_CARD_RESPONSE',
                'success': False,
                'message': f'使用卡密失败: {str(e)}'
            }
    
    def _handle_get_time_request(self, conn_id: str, message: Dict[str, Any], client_ip: str = None) -> Dict[str, Any]:
        """
        处理获取剩余时间请求
        
        Args:
            conn_id: 连接ID
            message: 消息数据
            client_ip: 客户端IP
            
        Returns:
            Dict: 剩余时间响应
        """
        try:
            # 验证会话
            session_info = self.session_manager.get_session_by_connection(conn_id)
            if not session_info:
                return {
                    'type': 'GET_TIME_RESPONSE',
                    'success': False,
                    'message': '请先登录'
                }
            
            username = session_info.get('username')
            
            # 获取剩余时间
            result = self.card_manager.get_user_remaining_time(username)
            
            response = {
                'type': 'GET_TIME_RESPONSE',
                'success': result['success']
            }
            
            if result['success']:
                response.update({
                    'has_time': result.get('has_time', False),
                    'remaining_seconds': result.get('remaining_seconds', 0),
                    'remaining_hours': result.get('remaining_hours', 0),
                    'expires_at': result.get('expires_at'),
                    'status': result.get('status')
                })
            else:
                response['message'] = result.get('message', '获取剩余时间失败')
            
            return response
            
        except Exception as e:
            print(f"处理获取剩余时间请求时出错: {e}")
            return {
                'type': 'GET_TIME_RESPONSE',
                'success': False,
                'message': f'获取剩余时间失败: {str(e)}'
            }
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """
        创建错误响应
        
        Args:
            error_message: 错误消息
            
        Returns:
            Dict[str, Any]: 错误响应
        """
        return {
            'type': MESSAGE_TYPES['ERROR'],
            'success': False,
            'message': error_message,
            'timestamp': datetime.now().isoformat()
        }
    
    def create_welcome_message(self) -> Dict[str, Any]:
        """
        创建欢迎消息
        
        Returns:
            Dict[str, Any]: 欢迎消息
        """
        return {
            'type': 'welcome',
            'message': '欢迎连接到网络验证服务器',
            'server_version': '1.0.0',
            'supported_message_types': list(MESSAGE_TYPES.values()),
            'timestamp': datetime.now().isoformat()
        }
    
    def create_disconnect_message(self, reason: str = '服务器断开连接') -> Dict[str, Any]:
        """
        创建断开连接消息
        
        Args:
            reason: 断开原因
            
        Returns:
            Dict[str, Any]: 断开连接消息
        """
        return {
            'type': 'disconnect',
            'message': reason,
            'timestamp': datetime.now().isoformat()
        }
    
    def validate_session_token(self, session_token: str) -> Dict[str, Any]:
        """
        验证会话令牌
        
        Args:
            session_token: 会话令牌
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        return self.session_manager.validate_session(session_token)
    
    def handle_client_disconnect(self, conn_id: str, session_token: str = None):
        """
        处理客户端断开连接
        
        Args:
            conn_id: 连接ID
            session_token: 会话令牌（如果有）
        """
        print(f"客户端断开连接: {conn_id}")
        
        # 如果有会话令牌，销毁会话
        if session_token:
            destroy_result = self.session_manager.destroy_session(session_token)
            if destroy_result['success']:
                print(f"会话已销毁: {session_token[:8]}...")
            else:
                print(f"销毁会话失败: {destroy_result['message']}")
    
    def get_protocol_statistics(self) -> Dict[str, Any]:
        """
        获取协议统计信息
        
        Returns:
            Dict[str, Any]: 协议统计信息
        """
        return {
            'supported_message_types': list(MESSAGE_TYPES.values()),
            'auth_statuses': list(AUTH_STATUS.values()),
            'message_handlers': len(self.message_handlers)
        }


class ClientProtocolHandler:
    """客户端协议处理器"""
    
    def __init__(self):
        """初始化客户端协议处理器"""
        self.session_token = None
        self.user_info = None
    
    def create_auth_request(self, username: str, password: str, client_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        创建认证请求
        
        Args:
            username: 用户名
            password: 密码
            client_info: 客户端信息
            
        Returns:
            Dict[str, Any]: 认证请求消息
        """
        message = {
            'type': MESSAGE_TYPES['AUTH_REQUEST'],
            'username': username,
            'password': password,
            'timestamp': datetime.now().isoformat()
        }
        
        if client_info:
            message['client_info'] = client_info
        
        return message
    
    def create_heartbeat(self, include_session: bool = True) -> Dict[str, Any]:
        """
        创建心跳消息
        
        Args:
            include_session: 是否包含会话令牌
            
        Returns:
            Dict[str, Any]: 心跳消息
        """
        message = {
            'type': MESSAGE_TYPES['HEARTBEAT'],
            'timestamp': datetime.now().isoformat()
        }
        
        if include_session and self.session_token:
            message['session_token'] = self.session_token
        
        return message
    
    def process_auth_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理认证响应
        
        Args:
            response: 服务器响应
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        if response.get('success'):
            # 保存会话信息
            self.session_token = response.get('session_token')
            self.user_info = response.get('user_info')
            
            return {
                'success': True,
                'message': response.get('message', '认证成功'),
                'user_info': self.user_info,
                'session_token': self.session_token,
                'expires_at': response.get('expires_at')
            }
        else:
            return {
                'success': False,
                'message': response.get('message', '认证失败'),
                'status': response.get('status'),
                'remaining_attempts': response.get('remaining_attempts'),
                'remaining_time': response.get('remaining_time')
            }
    
    def is_authenticated(self) -> bool:
        """
        检查是否已认证
        
        Returns:
            bool: 已认证返回True
        """
        return self.session_token is not None
    
    def clear_session(self):
        """清除会话信息"""
        self.session_token = None
        self.user_info = None
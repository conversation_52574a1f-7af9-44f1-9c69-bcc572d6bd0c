#!/usr/bin/env python3
"""
充值卡系统演示脚本
展示完整的充值卡生成、管理和使用流程
"""

import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.database_manager import DatabaseManager
from server.recharge_card_manager import RechargeCardManager
from server.user_manager import <PERSON>rManager


def demo_recharge_system():
    """演示充值卡系统"""
    print("=" * 60)
    print("充值卡系统演示")
    print("=" * 60)
    
    try:
        # 初始化系统
        print("\n1. 初始化系统...")
        db_path = "data/demo_recharge.db"
        if os.path.exists(db_path):
            os.remove(db_path)
        
        db_manager = DatabaseManager(db_path)
        user_manager = UserManager(db_manager)
        card_manager = RechargeCardManager(db_manager)
        print("✓ 系统初始化完成")
        
        # 创建演示用户
        print("\n2. 创建演示用户...")
        users = [
            ("alice", "password123"),
            ("bob", "password456"),
            ("charlie", "password789")
        ]
        
        for username, password in users:
            result = user_manager.create_user(username, password)
            if result['success']:
                print(f"✓ 用户 {username} 创建成功")
            else:
                print(f"× 用户 {username} 创建失败: {result['message']}")
        
        # 生成各种类型的卡密
        print("\n3. 生成充值卡密...")
        card_generation_plan = [
            ('hour_1', 5, '1小时卡批次'),
            ('day_1', 3, '天卡批次'),
            ('week_1', 2, '周卡批次'),
            ('month_1', 1, '月卡批次')
        ]
        
        all_cards = {}
        
        for card_type, quantity, description in card_generation_plan:
            result = card_manager.generate_cards(card_type, quantity, "demo_admin")
            if result['success']:
                all_cards[card_type] = result['cards']
                card_name = card_manager.card_types[card_type]['name']
                print(f"✓ 生成 {card_name} {quantity} 张")
                
                # 显示生成的卡密
                for i, card in enumerate(result['cards'], 1):
                    print(f"  {i}. {card['card_code']} (价值: ¥{card['price']})")
            else:
                print(f"× 生成 {card_type} 失败: {result['message']}")
        
        # 演示用户使用卡密
        print("\n4. 用户使用卡密演示...")
        
        # Alice 使用1小时卡
        if 'hour_1' in all_cards and all_cards['hour_1']:
            card_code = all_cards['hour_1'][0]['card_code']
            print(f"\nAlice 使用1小时卡: {card_code}")
            
            result = card_manager.use_card(card_code, "alice")
            if result['success']:
                print(f"✓ 使用成功: {result['message']}")
                print(f"  到期时间: {result['expires_at']}")
                
                # 检查Alice的剩余时间
                time_result = card_manager.get_user_remaining_time("alice")
                if time_result['success'] and time_result['has_time']:
                    print(f"  当前剩余: {time_result['remaining_hours']:.2f} 小时")
            else:
                print(f"× 使用失败: {result['message']}")
        
        # Bob 使用天卡
        if 'day_1' in all_cards and all_cards['day_1']:
            card_code = all_cards['day_1'][0]['card_code']
            print(f"\nBob 使用天卡: {card_code}")
            
            result = card_manager.use_card(card_code, "bob")
            if result['success']:
                print(f"✓ 使用成功: {result['message']}")
                
                # 检查Bob的剩余时间
                time_result = card_manager.get_user_remaining_time("bob")
                if time_result['success'] and time_result['has_time']:
                    print(f"  当前剩余: {time_result['remaining_hours']:.2f} 小时")
        
        # Alice 再使用一张天卡（时间累加）
        if 'day_1' in all_cards and len(all_cards['day_1']) > 1:
            card_code = all_cards['day_1'][1]['card_code']
            print(f"\nAlice 再使用天卡（时间累加）: {card_code}")
            
            result = card_manager.use_card(card_code, "alice")
            if result['success']:
                print(f"✓ 使用成功: {result['message']}")
                
                # 检查Alice累加后的剩余时间
                time_result = card_manager.get_user_remaining_time("alice")
                if time_result['success'] and time_result['has_time']:
                    print(f"  累加后剩余: {time_result['remaining_hours']:.2f} 小时")
        
        # 演示错误情况
        print("\n5. 错误情况演示...")
        
        # 重复使用卡密
        if 'hour_1' in all_cards and all_cards['hour_1']:
            used_card = all_cards['hour_1'][0]['card_code']
            print(f"\n尝试重复使用卡密: {used_card}")
            
            result = card_manager.use_card(used_card, "charlie")
            if not result['success']:
                print(f"✓ 正确阻止重复使用: {result['message']}")
        
        # 使用无效卡密
        print(f"\n尝试使用无效卡密: INVALID-CARD-CODE")
        result = card_manager.use_card("INVALID-CARD-CODE", "charlie")
        if not result['success']:
            print(f"✓ 正确识别无效卡密: {result['message']}")
        
        # 不存在的用户使用卡密
        if 'hour_1' in all_cards and len(all_cards['hour_1']) > 1:
            card_code = all_cards['hour_1'][1]['card_code']
            print(f"\n不存在的用户尝试使用卡密: {card_code}")
            
            result = card_manager.use_card(card_code, "nonexistent_user")
            if not result['success']:
                print(f"✓ 正确识别不存在的用户: {result['message']}")
        
        # 显示用户时间历史
        print("\n6. 用户时间历史...")
        for username in ["alice", "bob", "charlie"]:
            print(f"\n{username} 的时间充值历史:")
            history = card_manager.get_user_time_history(username)
            if history:
                for i, record in enumerate(history, 1):
                    print(f"  {i}. {record['description']} - {record['duration_hours']}小时")
                    print(f"     使用时间: {record['added_at']}")
                    print(f"     到期时间: {record['expires_at']}")
            else:
                print("  无充值记录")
        
        # 显示系统统计
        print("\n7. 系统统计信息...")
        stats = card_manager.get_card_statistics()
        if stats:
            print(f"\n总体统计:")
            print(f"  总卡密数: {stats.get('total_cards', 0)}")
            print(f"  已使用: {stats.get('used_cards', 0)}")
            print(f"  未使用: {stats.get('unused_cards', 0)}")
            
            if stats.get('total_cards', 0) > 0:
                usage_rate = (stats.get('used_cards', 0) / stats.get('total_cards', 1)) * 100
                print(f"  使用率: {usage_rate:.1f}%")
            
            print(f"\n按类型统计:")
            for card_type, type_stats in stats.get('by_type', {}).items():
                print(f"  {type_stats['name']}:")
                print(f"    总数: {type_stats['total']}")
                print(f"    已用: {type_stats['used']}")
                print(f"    未用: {type_stats['unused']}")
        
        # 显示当前所有用户的剩余时间
        print("\n8. 当前用户剩余时间...")
        for username in ["alice", "bob", "charlie"]:
            result = card_manager.get_user_remaining_time(username)
            if result['success']:
                if result['has_time']:
                    hours = result['remaining_hours']
                    status = result['status']
                    print(f"  {username}: {hours:.2f} 小时 ({status})")
                else:
                    print(f"  {username}: 无剩余时间")
            else:
                print(f"  {username}: 获取失败")
        
        print("\n" + "=" * 60)
        print("充值卡系统演示完成")
        print("=" * 60)
        
        # 询问是否保留演示数据
        try:
            keep_data = input("\n是否保留演示数据？(y/N): ").strip().lower()
            if keep_data not in ['y', 'yes']:
                os.remove(db_path)
                print("✓ 演示数据已清理")
            else:
                print(f"✓ 演示数据已保存到: {db_path}")
        except KeyboardInterrupt:
            print("\n\n✓ 演示结束")
        
    except Exception as e:
        print(f"\n× 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


def show_card_types():
    """显示支持的卡密类型"""
    print("\n支持的卡密类型:")
    print("-" * 40)
    
    # 创建临时管理器来获取卡密类型
    db_manager = DatabaseManager(":memory:")
    card_manager = RechargeCardManager(db_manager)
    
    for card_type, config in card_manager.card_types.items():
        hours = config['duration_hours']
        if hours < 24:
            duration = f"{hours} 小时"
        elif hours < 24 * 7:
            days = hours // 24
            duration = f"{days} 天"
        elif hours < 24 * 365:
            days = hours // 24
            duration = f"{days} 天"
        else:
            years = hours // (24 * 365)
            duration = f"{years} 年"
        
        print(f"  {config['name']}: {duration} - ¥{config['price']}")


if __name__ == "__main__":
    print("充值卡系统演示程序")
    print("1. 运行完整演示")
    print("2. 显示卡密类型")
    print("3. 退出")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            demo_recharge_system()
        elif choice == '2':
            show_card_types()
        elif choice == '3':
            print("再见！")
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
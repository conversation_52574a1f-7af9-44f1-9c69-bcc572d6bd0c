# 网络验证工具客户端配置文件示例
# 复制此文件为 client_config.ini 并根据需要修改配置

[client]
# 默认服务器地址
default_host = 127.0.0.1

# 默认服务器端口
default_port = 8888

# 连接超时时间（秒）
connection_timeout = 10

# 接收数据超时时间（秒）
receive_timeout = 30

# 发送数据超时时间（秒）
send_timeout = 10

# 是否启用自动重连
auto_reconnect = true

# 重连间隔（秒）
reconnect_interval = 5

# 最大重试次数
max_retry_attempts = 3

# 心跳间隔（秒）
heartbeat_interval = 30

# 是否启用心跳检测
enable_heartbeat = true

[logging]
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
level = INFO

# 日志文件路径
file_path = logs/client.log

# 日志文件最大大小（字节）
# 5242880 = 5MB
max_file_size = 5242880

# 日志文件备份数量
backup_count = 3

# 是否启用控制台输出
console_output = true

# 日志格式
# 可选值: simple, detailed, json
format = simple

[ui]
# 窗口宽度
window_width = 500

# 窗口高度
window_height = 600

# 窗口最小宽度
min_width = 400

# 窗口最小高度
min_height = 500

# 是否允许调整窗口大小
resizable = true

# 窗口标题
window_title = 网络验证工具客户端

# 界面主题
# 可选值: default, dark, light
theme = default

# 字体大小
font_size = 10

# 字体族
font_family = 

# 是否自动保存窗口设置
auto_save_settings = true

# 是否显示状态栏
show_status_bar = true

# 是否显示工具栏
show_toolbar = true

# 界面语言
# 可选值: zh_CN, en_US
language = zh_CN

[security]
# 是否记住服务器地址
remember_server = true

# 是否记住用户名
remember_username = false

# 是否自动登录
auto_login = false

# 会话超时警告时间（秒）
session_warning_time = 300

# 是否启用SSL/TLS
enable_ssl = false

# SSL证书验证
ssl_verify = true

# SSL证书文件路径
ssl_cert_file = 

# SSL密钥文件路径
ssl_key_file = 

[performance]
# 网络缓冲区大小
buffer_size = 4096

# 是否启用数据压缩
enable_compression = false

# 压缩级别（1-9）
compression_level = 6

# 是否启用本地缓存
enable_cache = true

# 缓存大小
cache_size = 100

# 缓存过期时间（秒）
cache_ttl = 300

[notifications]
# 是否启用通知
enable_notifications = true

# 连接状态变化通知
notify_connection_change = true

# 认证状态变化通知
notify_auth_change = true

# 错误通知
notify_errors = true

# 会话超时通知
notify_session_timeout = true

# 通知显示时间（秒）
notification_duration = 5

# 通知位置
# 可选值: top_right, top_left, bottom_right, bottom_left
notification_position = top_right

[advanced]
# 是否启用调试模式
debug_mode = false

# 调试日志文件
debug_log_file = logs/client_debug.log

# 网络调试
network_debug = false

# 是否显示详细错误信息
show_detailed_errors = false

# 自定义用户代理
user_agent = NetworkAuthClient/1.0

# 是否启用统计收集
collect_statistics = true

# 统计上报间隔（秒）
statistics_interval = 300

# 是否启用崩溃报告
enable_crash_reporting = false

# 崩溃报告文件
crash_report_file = logs/crash_report.log

[shortcuts]
# 键盘快捷键配置
connect = Ctrl+C
disconnect = Ctrl+D
login = Ctrl+L
logout = Ctrl+O
quit = Ctrl+Q
refresh = F5
settings = Ctrl+S

[proxy]
# 是否启用代理
enable_proxy = false

# 代理类型
# 可选值: http, socks4, socks5
proxy_type = http

# 代理服务器地址
proxy_host = 

# 代理服务器端口
proxy_port = 

# 代理用户名
proxy_username = 

# 代理密码
proxy_password = 

[backup]
# 是否启用配置备份
auto_backup_config = true

# 配置备份间隔（小时）
config_backup_interval = 24

# 配置备份保留天数
config_backup_retention_days = 7

# 配置备份目录
config_backup_directory = backup/config
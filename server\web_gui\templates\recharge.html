<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账户充值 - 网络验证系统</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .recharge-container {
            max-width: 600px;
            margin: 30px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .balance-display {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .balance-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 10px 0;
        }
        .preset-amounts {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        .preset-btn {
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .preset-btn:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .preset-btn.selected {
            border-color: #007bff;
            background: #007bff;
            color: white;
        }
        .custom-amount {
            position: relative;
        }
        .currency-symbol {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-weight: bold;
            color: #6c757d;
        }
        .amount-input {
            padding-left: 35px;
        }
        .recharge-summary {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }
        .summary-total {
            border-top: 2px solid #dee2e6;
            padding-top: 15px;
            margin-top: 15px;
            font-weight: bold;
            font-size: 1.1rem;
        }
        .recharge-notes {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
        }
        .validation-error {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }
        .validation-success {
            color: #28a745;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="recharge-container">
            <!-- 页面标题 -->
            <div class="text-center mb-4">
                <h2><i class="fas fa-wallet"></i> 账户充值</h2>
                <p class="text-muted">为您的账户充值，享受更多服务</p>
            </div>
            
            <!-- 显示消息 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <!-- 当前余额显示 -->
            <div class="balance-display">
                <div class="h5 mb-2">当前余额</div>
                <div class="balance-amount" id="currentBalance">¥{{ "%.2f"|format(current_balance or 0) }}</div>
                <div class="small">用户: {{ username }}</div>
            </div>
            
            <form method="POST" id="rechargeForm">
                <!-- 预设金额选择 -->
                <div class="mb-4">
                    <label class="form-label h6">选择充值金额</label>
                    <div class="preset-amounts">
                        <div class="preset-btn" data-amount="10">¥10</div>
                        <div class="preset-btn" data-amount="20">¥20</div>
                        <div class="preset-btn" data-amount="50">¥50</div>
                        <div class="preset-btn" data-amount="100">¥100</div>
                        <div class="preset-btn" data-amount="200">¥200</div>
                        <div class="preset-btn" data-amount="500">¥500</div>
                    </div>
                </div>
                
                <!-- 自定义金额输入 -->
                <div class="mb-4">
                    <label for="customAmount" class="form-label h6">或输入自定义金额</label>
                    <div class="custom-amount">
                        <span class="currency-symbol">¥</span>
                        <input type="number" class="form-control amount-input" id="customAmount" name="amount" 
                               step="0.01" min="0.01" max="10000" placeholder="请输入充值金额">
                    </div>
                    <div id="amountValidation" class="validation-error"></div>
                </div>
                
                <!-- 充值说明 -->
                <div class="mb-4">
                    <label for="description" class="form-label">充值说明 (可选)</label>
                    <input type="text" class="form-control" id="description" name="description" 
                           placeholder="请输入充值说明" maxlength="100">
                </div>
                
                <!-- 充值汇总 -->
                <div class="recharge-summary" id="rechargeSummary" style="display: none;">
                    <h6>充值汇总</h6>
                    <div class="summary-row">
                        <span>当前余额:</span>
                        <span>¥{{ "%.2f"|format(current_balance or 0) }}</span>
                    </div>
                    <div class="summary-row">
                        <span>充值金额:</span>
                        <span id="rechargeAmount">¥0.00</span>
                    </div>
                    <div class="summary-row summary-total">
                        <span>充值后余额:</span>
                        <span id="newBalance">¥{{ "%.2f"|format(current_balance or 0) }}</span>
                    </div>
                </div>
                
                <!-- 充值须知 -->
                <div class="recharge-notes">
                    <h6><i class="fas fa-info-circle"></i> 充值须知</h6>
                    <ul class="mb-0">
                        <li>充值金额范围：¥0.01 - ¥10,000</li>
                        <li>充值后余额立即生效，可立即使用</li>
                        <li>支持最多2位小数精度</li>
                        <li>充值记录可在个人中心查看</li>
                        <li>如有疑问，请联系客服</li>
                    </ul>
                </div>
                
                <!-- 操作按钮 -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary me-md-2">返回</a>
                    <button type="button" class="btn btn-info me-md-2" onclick="showRechargeHistory()">
                        <i class="fas fa-history"></i> 充值历史
                    </button>
                    <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                        <i class="fas fa-credit-card"></i> 确认充值
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 充值历史模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">充值历史</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>金额</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>说明</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <tr>
                                    <td colspan="5" class="text-center">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const presetBtns = document.querySelectorAll('.preset-btn');
            const customAmountInput = document.getElementById('customAmount');
            const amountValidation = document.getElementById('amountValidation');
            const rechargeSummary = document.getElementById('rechargeSummary');
            const rechargeAmount = document.getElementById('rechargeAmount');
            const newBalance = document.getElementById('newBalance');
            const submitBtn = document.getElementById('submitBtn');
            const currentBalance = {{ current_balance or 0 }};
            
            // 预设金额按钮点击事件
            presetBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 清除其他按钮的选中状态
                    presetBtns.forEach(b => b.classList.remove('selected'));
                    // 选中当前按钮
                    this.classList.add('selected');
                    // 设置金额
                    const amount = this.dataset.amount;
                    customAmountInput.value = amount;
                    validateAmount(amount);
                });
            });
            
            // 自定义金额输入事件
            customAmountInput.addEventListener('input', function() {
                // 清除预设按钮选中状态
                presetBtns.forEach(btn => btn.classList.remove('selected'));
                validateAmount(this.value);
            });
            
            // 金额验证函数
            function validateAmount(value) {
                const amount = parseFloat(value);
                
                if (!value || isNaN(amount)) {
                    amountValidation.textContent = '';
                    amountValidation.className = 'validation-error';
                    rechargeSummary.style.display = 'none';
                    submitBtn.disabled = true;
                    return;
                }
                
                if (amount <= 0) {
                    amountValidation.textContent = '充值金额必须大于0';
                    amountValidation.className = 'validation-error';
                    rechargeSummary.style.display = 'none';
                    submitBtn.disabled = true;
                    return;
                }
                
                if (amount > 10000) {
                    amountValidation.textContent = '单次充值金额不能超过¥10,000';
                    amountValidation.className = 'validation-error';
                    rechargeSummary.style.display = 'none';
                    submitBtn.disabled = true;
                    return;
                }
                
                // 检查小数位数
                const decimalPlaces = (value.split('.')[1] || '').length;
                if (decimalPlaces > 2) {
                    amountValidation.textContent = '充值金额最多支持2位小数';
                    amountValidation.className = 'validation-error';
                    rechargeSummary.style.display = 'none';
                    submitBtn.disabled = true;
                    return;
                }
                
                // 验证通过
                const newBalanceValue = currentBalance + amount;
                amountValidation.textContent = `充值后余额: ¥${newBalanceValue.toFixed(2)}`;
                amountValidation.className = 'validation-success';
                
                // 更新充值汇总
                rechargeAmount.textContent = `¥${amount.toFixed(2)}`;
                newBalance.textContent = `¥${newBalanceValue.toFixed(2)}`;
                rechargeSummary.style.display = 'block';
                submitBtn.disabled = false;
            }
            
            // 表单提交确认
            document.getElementById('rechargeForm').addEventListener('submit', function(e) {
                const amount = parseFloat(customAmountInput.value);
                if (!amount || amount <= 0) {
                    e.preventDefault();
                    alert('请输入有效的充值金额');
                    return;
                }
                
                const confirmMsg = `确认充值 ¥${amount.toFixed(2)} 到您的账户？\\n\\n` +
                                 `当前余额: ¥${currentBalance.toFixed(2)}\\n` +
                                 `充值金额: ¥${amount.toFixed(2)}\\n` +
                                 `充值后余额: ¥${(currentBalance + amount).toFixed(2)}`;
                
                if (!confirm(confirmMsg)) {
                    e.preventDefault();
                }
            });
        });
        
        // 显示充值历史
        function showRechargeHistory() {
            const modal = new bootstrap.Modal(document.getElementById('historyModal'));
            modal.show();
            
            // 模拟加载充值历史数据
            setTimeout(() => {
                const historyData = [
                    ['2025-08-31 10:00:00', '¥100.00', '手动充值', '已完成', '在线充值'],
                    ['2025-08-30 15:30:00', '¥50.00', '管理员充值', '已完成', '系统赠送'],
                    ['2025-08-29 09:15:00', '¥200.00', '手动充值', '已完成', '账户充值']
                ];
                
                const tbody = document.getElementById('historyTableBody');
                tbody.innerHTML = '';
                
                if (historyData.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5" class="text-center">暂无充值记录</td></tr>';
                } else {
                    historyData.forEach(row => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td>${row[0]}</td>
                            <td class="text-success fw-bold">${row[1]}</td>
                            <td><span class="badge bg-info">${row[2]}</span></td>
                            <td><span class="badge bg-success">${row[3]}</span></td>
                            <td>${row[4]}</td>
                        `;
                        tbody.appendChild(tr);
                    });
                }
            }, 500);
        }
    </script>
</body>
</html>
#!/usr/bin/env python3
"""
网络验证工具项目验证脚本
验证项目完整性和功能
"""

import sys
import os
import subprocess
import importlib.util
from pathlib import Path
from typing import Dict, List, Any


class ProjectVerifier:
    """项目验证器"""
    
    def __init__(self):
        """初始化项目验证器"""
        self.project_root = Path(__file__).parent
        self.verification_results = []
        self.errors = []
        self.warnings = []
    
    def verify_project_structure(self) -> bool:
        """验证项目结构"""
        print("验证项目结构...")
        
        required_dirs = [
            'server',
            'client', 
            'common',
            'tests',
            'tools',
            'docs'
        ]
        
        required_files = [
            'server_main.py',
            'client_main.py',
            'install.py',
            'run_tests.py',
            'README.md',
            'requirements.txt'
        ]
        
        success = True
        
        # 检查目录
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists() and dir_path.is_dir():
                print(f"  √ 目录存在: {dir_name}")
            else:
                print(f"  × 目录缺失: {dir_name}")
                self.errors.append(f"缺少目录: {dir_name}")
                success = False
        
        # 检查文件
        for file_name in required_files:
            file_path = self.project_root / file_name
            if file_path.exists() and file_path.is_file():
                print(f"  √ 文件存在: {file_name}")
            else:
                print(f"  × 文件缺失: {file_name}")
                self.errors.append(f"缺少文件: {file_name}")
                success = False
        
        return success
    
    def verify_module_imports(self) -> bool:
        """验证模块导入"""
        print("\n验证模块导入...")
        
        # 服务器端模块
        server_modules = [
            'server.database_manager',
            'server.user_manager',
            'server.session_manager',
            'server.authentication_handler',
            'server.socket_listener',
            'server.protocol_handler',
            'server.thread_pool_manager',
            'server.security_utils',
            'server.server_gui',
            'server.server_application'
        ]
        
        # 客户端模块
        client_modules = [
            'client.network_client',
            'client.client_gui',
            'client.auth_result_handler',
            'client.client_application'
        ]
        
        # 共享模块
        common_modules = [
            'common.config_manager',
            'common.log_manager',
            'common.constants',
            'common.interfaces',
            'common.exceptions',
            'common.performance_monitor',
            'common.debug_tools'
        ]
        
        all_modules = server_modules + client_modules + common_modules
        success = True
        
        # 添加项目根目录到Python路径
        sys.path.insert(0, str(self.project_root))
        
        for module_name in all_modules:
            try:
                module = importlib.import_module(module_name)
                print(f"  √ 模块导入成功: {module_name}")
            except ImportError as e:
                print(f"  × 模块导入失败: {module_name} - {e}")
                self.errors.append(f"模块导入失败: {module_name}")
                success = False
            except Exception as e:
                print(f"  ⚠ 模块导入警告: {module_name} - {e}")
                self.warnings.append(f"模块导入警告: {module_name}")
        
        return success
    
    def verify_dependencies(self) -> bool:
        """验证依赖项"""
        print("\n验证Python依赖项...")
        
        # 必需的标准库模块
        required_stdlib = [
            'tkinter',
            'sqlite3',
            'socket',
            'threading',
            'json',
            'hashlib',
            'datetime',
            'pathlib',
            'configparser',
            'logging'
        ]
        
        # 可选的第三方模块
        optional_modules = [
            'psutil',
            'cryptography'
        ]
        
        success = True
        
        # 检查标准库模块
        for module_name in required_stdlib:
            try:
                importlib.import_module(module_name)
                print(f"  √ 标准库模块: {module_name}")
            except ImportError:
                print(f"  × 标准库模块缺失: {module_name}")
                self.errors.append(f"标准库模块缺失: {module_name}")
                success = False
        
        # 检查可选模块
        for module_name in optional_modules:
            try:
                importlib.import_module(module_name)
                print(f"  √ 可选模块: {module_name}")
            except ImportError:
                print(f"  ? 可选模块未安装: {module_name} (不影响基本功能)")
                self.warnings.append(f"可选模块未安装: {module_name}")
        
        return success
    
    def verify_configuration_files(self) -> bool:
        """验证配置文件"""
        print("\n验证配置文件...")
        
        config_files = [
            'config.ini.example',
            'client_config.ini.example'
        ]
        
        success = True
        
        for config_file in config_files:
            config_path = self.project_root / config_file
            if config_path.exists():
                print(f"  √ 配置文件存在: {config_file}")
                
                # 验证配置文件格式
                try:
                    import configparser
                    config = configparser.ConfigParser()
                    config.read(config_path, encoding='utf-8')
                    
                    # 检查基本配置节
                    if config_file.startswith('config.ini'):
                        required_sections = ['server', 'database', 'logging', 'security']
                    else:
                        required_sections = ['client', 'logging', 'ui']
                    
                    for section in required_sections:
                        if config.has_section(section):
                            print(f"    √ 配置节存在: {section}")
                        else:
                            print(f"    × 配置节缺失: {section}")
                            self.warnings.append(f"配置节缺失: {config_file}[{section}]")
                    
                except Exception as e:
                    print(f"  ⚠ 配置文件格式错误: {config_file} - {e}")
                    self.warnings.append(f"配置文件格式错误: {config_file}")
            else:
                print(f"  × 配置文件缺失: {config_file}")
                self.errors.append(f"配置文件缺失: {config_file}")
                success = False
        
        return success
    
    def verify_documentation(self) -> bool:
        """验证文档"""
        print("\n验证文档...")
        
        doc_files = [
            'docs/user_manual.md',
            'docs/deployment_guide.md',
            'README.md'
        ]
        
        success = True
        
        for doc_file in doc_files:
            doc_path = self.project_root / doc_file
            if doc_path.exists():
                print(f"  √ 文档存在: {doc_file}")
                
                # 检查文档大小
                file_size = doc_path.stat().st_size
                if file_size > 1000:  # 至少1KB
                    print(f"    √ 文档内容充实: {file_size} 字节")
                else:
                    print(f"    ⚠ 文档内容较少: {file_size} 字节")
                    self.warnings.append(f"文档内容较少: {doc_file}")
            else:
                print(f"  × 文档缺失: {doc_file}")
                self.errors.append(f"文档缺失: {doc_file}")
                success = False
        
        return success
    
    def verify_test_files(self) -> bool:
        """验证测试文件"""
        print("\n验证测试文件...")
        
        test_files = [
            'tests/test_database.py',
            'tests/test_authentication.py',
            'tests/test_network.py',
            'tests/integration_tests.py'
        ]
        
        success = True
        
        for test_file in test_files:
            test_path = self.project_root / test_file
            if test_path.exists():
                print(f"  √ 测试文件存在: {test_file}")
            else:
                print(f"  × 测试文件缺失: {test_file}")
                self.errors.append(f"测试文件缺失: {test_file}")
                success = False
        
        return success
    
    def verify_tools(self) -> bool:
        """验证工具脚本"""
        print("\n验证工具脚本...")
        
        tool_files = [
            'tools/system_optimizer.py',
            'tools/create_test_data.py',
            'tools/windows_service.py'
        ]
        
        success = True
        
        for tool_file in tool_files:
            tool_path = self.project_root / tool_file
            if tool_path.exists():
                print(f"  √ 工具脚本存在: {tool_file}")
            else:
                print(f"  × 工具脚本缺失: {tool_file}")
                self.errors.append(f"工具脚本缺失: {tool_file}")
                success = False
        
        return success
    
    def run_basic_functionality_test(self) -> bool:
        """运行基本功能测试"""
        print("\n运行基本功能测试...")
        
        try:
            # 测试数据库管理器
            sys.path.insert(0, str(self.project_root))
            from server.database_manager import DatabaseManager
            
            # 创建临时数据库
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
                tmp_db_path = tmp_file.name
            
            try:
                db_manager = DatabaseManager(tmp_db_path)
                print("  √ 数据库管理器创建成功")
                
                # 测试数据库连接
                with db_manager.get_connection() as conn:
                    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    if len(tables) >= 3:  # users, sessions, logs
                        print("  √ 数据库表创建成功")
                    else:
                        print("  ⚠ 数据库表数量不足")
                        self.warnings.append("数据库表数量不足")
                
                del db_manager
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(tmp_db_path)
                except:
                    pass
            
            # 测试配置管理器
            from common.config_manager import ConfigManager
            config_manager = ConfigManager()
            print("  √ 配置管理器创建成功")
            
            # 测试日志管理器
            from common.log_manager import LogManager
            log_manager = LogManager()
            print("  √ 日志管理器创建成功")
            log_manager.shutdown()
            
            return True
            
        except Exception as e:
            print(f"  × 基本功能测试失败: {e}")
            self.errors.append(f"基本功能测试失败: {e}")
            return False
    
    def generate_verification_report(self) -> Dict[str, Any]:
        """生成验证报告"""
        return {
            'timestamp': __import__('datetime').datetime.now().isoformat(),
            'project_root': str(self.project_root),
            'total_errors': len(self.errors),
            'total_warnings': len(self.warnings),
            'errors': self.errors,
            'warnings': self.warnings,
            'verification_results': self.verification_results
        }
    
    def run_full_verification(self) -> bool:
        """运行完整验证"""
        print("网络验证工具项目验证")
        print("=" * 60)
        
        verification_steps = [
            ("项目结构", self.verify_project_structure),
            ("模块导入", self.verify_module_imports),
            ("依赖项", self.verify_dependencies),
            ("配置文件", self.verify_configuration_files),
            ("文档", self.verify_documentation),
            ("测试文件", self.verify_test_files),
            ("工具脚本", self.verify_tools),
            ("基本功能", self.run_basic_functionality_test)
        ]
        
        passed_steps = 0
        total_steps = len(verification_steps)
        
        for step_name, step_func in verification_steps:
            try:
                success = step_func()
                if success:
                    passed_steps += 1
                    self.verification_results.append((step_name, True, None))
                else:
                    self.verification_results.append((step_name, False, "验证失败"))
            except Exception as e:
                print(f"  × 验证步骤异常: {step_name} - {e}")
                self.errors.append(f"验证步骤异常: {step_name}")
                self.verification_results.append((step_name, False, str(e)))
        
        # 打印摘要
        print("\n" + "=" * 60)
        print("验证摘要")
        print("=" * 60)
        
        for step_name, success, error in self.verification_results:
            status = "√ 通过" if success else "× 失败"
            print(f"{step_name}: {status}")
            if error:
                print(f"  错误: {error}")
        
        print(f"\n通过步骤: {passed_steps}/{total_steps}")
        print(f"错误数量: {len(self.errors)}")
        print(f"警告数量: {len(self.warnings)}")
        
        if self.errors:
            print(f"\n错误列表:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        
        if self.warnings:
            print(f"\n警告列表:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
        
        overall_success = len(self.errors) == 0 and passed_steps == total_steps
        
        if overall_success:
            print(f"\n🎉 项目验证通过! 项目已准备就绪。")
        else:
            print(f"\n❌ 项目验证失败，请修复上述问题。")
        
        return overall_success


def main():
    """主函数"""
    try:
        verifier = ProjectVerifier()
        success = verifier.run_full_verification()
        
        # 生成报告
        report = verifier.generate_verification_report()
        
        # 保存报告到文件
        import json
        report_file = verifier.project_root / 'verification_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n验证报告已保存到: {report_file}")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n验证被用户中断")
        return 1
    except Exception as e:
        print(f"验证过程中发生严重错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
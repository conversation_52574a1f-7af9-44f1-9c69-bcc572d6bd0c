#!/usr/bin/env python3
"""
充值卡密管理器
处理充值卡的生成、验证和使用
"""

import secrets
import string
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from enum import Enum

from server.database_manager import DatabaseManager


class CardType(Enum):
    """卡密类型枚举"""
    HOUR_1 = ("1小时卡", 1, "hours")
    DAY_1 = ("天卡", 1, "days")
    WEEK_1 = ("周卡", 7, "days")
    MONTH_1 = ("月卡", 30, "days")
    SEASON_1 = ("季卡", 90, "days")
    YEAR_1 = ("年卡", 365, "days")
    YEAR_10 = ("10年卡", 3650, "days")
    
    def __init__(self, display_name: str, duration: int, unit: str):
        self.display_name = display_name
        self.duration = duration
        self.unit = unit
    
    def get_timedelta(self) -> timedelta:
        """获取时间增量"""
        if self.unit == "hours":
            return timedelta(hours=self.duration)
        elif self.unit == "days":
            return timedelta(days=self.duration)
        else:
            return timedelta(days=self.duration)


class CardManager:
    """充值卡密管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化卡密管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self._ensure_tables()
    
    def _ensure_tables(self):
        """确保卡密相关表存在"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建充值卡表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS recharge_cards (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        card_code VARCHAR(32) NOT NULL UNIQUE,
                        card_type VARCHAR(20) NOT NULL,
                        duration_hours INTEGER NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        created_by VARCHAR(50),
                        used_at DATETIME,
                        used_by VARCHAR(50),
                        status VARCHAR(20) DEFAULT 'active',
                        description TEXT
                    )
                """)
                
                # 创建用户时间表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS user_time (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username VARCHAR(50) NOT NULL UNIQUE,
                        expire_time DATETIME,
                        total_hours INTEGER DEFAULT 0,
                        used_hours INTEGER DEFAULT 0,
                        last_login DATETIME,
                        last_logout DATETIME,
                        status VARCHAR(20) DEFAULT 'active',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (username) REFERENCES users(username)
                    )
                """)
                
                # 创建充值记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS card_recharge_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username VARCHAR(50) NOT NULL,
                        card_code VARCHAR(32) NOT NULL,
                        card_type VARCHAR(20) NOT NULL,
                        hours_added INTEGER NOT NULL,
                        recharge_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        expire_time_before DATETIME,
                        expire_time_after DATETIME,
                        FOREIGN KEY (username) REFERENCES users(username),
                        FOREIGN KEY (card_code) REFERENCES recharge_cards(card_code)
                    )
                """)
                
                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_cards_code ON recharge_cards(card_code)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_cards_status ON recharge_cards(status)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_time_username ON user_time(username)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_recharge_records_username ON card_recharge_records(username)")
                
                conn.commit()
                
        except Exception as e:
            print(f"创建卡密表失败: {e}")
    
    def generate_card_code(self, length: int = 16) -> str:
        """
        生成卡密
        
        Args:
            length: 卡密长度
            
        Returns:
            str: 生成的卡密
        """
        # 使用大写字母和数字，排除容易混淆的字符
        chars = string.ascii_uppercase + string.digits
        chars = chars.replace('0', '').replace('O', '').replace('1', '').replace('I')
        
        # 生成随机卡密
        card_code = ''.join(secrets.choice(chars) for _ in range(length))
        
        # 格式化为 XXXX-XXXX-XXXX-XXXX 格式
        if length == 16:
            formatted_code = f"{card_code[:4]}-{card_code[4:8]}-{card_code[8:12]}-{card_code[12:16]}"
        else:
            formatted_code = card_code
        
        return formatted_code
    
    def create_cards(self, card_type: CardType, count: int = 1, 
                    created_by: str = "system") -> List[Dict[str, Any]]:
        """
        批量生成充值卡
        
        Args:
            card_type: 卡密类型
            count: 生成数量
            created_by: 创建者
            
        Returns:
            List[Dict]: 生成的卡密列表
        """
        cards = []
        
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 计算小时数
                if card_type.unit == "hours":
                    duration_hours = card_type.duration
                else:
                    duration_hours = card_type.duration * 24
                
                for _ in range(count):
                    # 生成唯一卡密
                    while True:
                        card_code = self.generate_card_code()
                        
                        # 检查卡密是否已存在
                        cursor.execute("SELECT id FROM recharge_cards WHERE card_code = ?", (card_code,))
                        if not cursor.fetchone():
                            break
                    
                    # 插入卡密记录
                    cursor.execute("""
                        INSERT INTO recharge_cards 
                        (card_code, card_type, duration_hours, created_by, description)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        card_code,
                        card_type.name,
                        duration_hours,
                        created_by,
                        card_type.display_name
                    ))
                    
                    cards.append({
                        'card_code': card_code,
                        'card_type': card_type.name,
                        'display_name': card_type.display_name,
                        'duration_hours': duration_hours,
                        'created_at': datetime.now().isoformat()
                    })
                
                conn.commit()
                
                print(f"成功生成 {count} 张 {card_type.display_name}")
                
        except Exception as e:
            print(f"生成卡密失败: {e}")
            return []
        
        return cards
    
    def use_card(self, username: str, card_code: str) -> Dict[str, Any]:
        """
        使用充值卡
        
        Args:
            username: 用户名
            card_code: 卡密
            
        Returns:
            Dict: 使用结果
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查卡密是否存在且未使用
                cursor.execute("""
                    SELECT card_type, duration_hours, status, used_by
                    FROM recharge_cards 
                    WHERE card_code = ?
                """, (card_code,))
                
                card_info = cursor.fetchone()
                
                if not card_info:
                    return {
                        'success': False,
                        'message': '卡密不存在或无效'
                    }
                
                card_type, duration_hours, status, used_by = card_info
                
                if status != 'active':
                    return {
                        'success': False,
                        'message': '卡密已失效'
                    }
                
                if used_by:
                    return {
                        'success': False,
                        'message': '卡密已被使用'
                    }
                
                # 获取用户当前时间信息
                cursor.execute("SELECT expire_time, total_hours FROM user_time WHERE username = ?", (username,))
                user_time_info = cursor.fetchone()
                
                current_time = datetime.now()
                
                if user_time_info:
                    current_expire_time, total_hours = user_time_info
                    
                    # 解析当前过期时间
                    if current_expire_time:
                        try:
                            expire_time = datetime.fromisoformat(current_expire_time)
                            # 如果当前时间还没过期，从过期时间开始累加
                            if expire_time > current_time:
                                new_expire_time = expire_time + timedelta(hours=duration_hours)
                            else:
                                # 如果已过期，从当前时间开始
                                new_expire_time = current_time + timedelta(hours=duration_hours)
                        except:
                            new_expire_time = current_time + timedelta(hours=duration_hours)
                    else:
                        new_expire_time = current_time + timedelta(hours=duration_hours)
                    
                    new_total_hours = (total_hours or 0) + duration_hours
                    
                    # 更新用户时间信息
                    cursor.execute("""
                        UPDATE user_time 
                        SET expire_time = ?, total_hours = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE username = ?
                    """, (new_expire_time.isoformat(), new_total_hours, username))
                    
                else:
                    # 创建新的用户时间记录
                    new_expire_time = current_time + timedelta(hours=duration_hours)
                    
                    cursor.execute("""
                        INSERT INTO user_time 
                        (username, expire_time, total_hours, created_at, updated_at)
                        VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """, (username, new_expire_time.isoformat(), duration_hours))
                
                # 标记卡密为已使用
                cursor.execute("""
                    UPDATE recharge_cards 
                    SET used_at = CURRENT_TIMESTAMP, used_by = ?, status = 'used'
                    WHERE card_code = ?
                """, (username, card_code))
                
                # 记录充值记录
                cursor.execute("""
                    INSERT INTO card_recharge_records 
                    (username, card_code, card_type, hours_added, expire_time_before, expire_time_after)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    username, 
                    card_code, 
                    card_type, 
                    duration_hours,
                    user_time_info[0] if user_time_info else None,
                    new_expire_time.isoformat()
                ))
                
                conn.commit()
                
                return {
                    'success': True,
                    'message': f'充值成功！已添加 {duration_hours} 小时',
                    'hours_added': duration_hours,
                    'new_expire_time': new_expire_time.isoformat(),
                    'card_type': card_type
                }
                
        except Exception as e:
            print(f"使用卡密失败: {e}")
            return {
                'success': False,
                'message': f'充值失败: {str(e)}'
            }
    
    def get_user_time_info(self, username: str) -> Dict[str, Any]:
        """
        获取用户时间信息
        
        Args:
            username: 用户名
            
        Returns:
            Dict: 用户时间信息
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT expire_time, total_hours, used_hours, last_login, status
                    FROM user_time 
                    WHERE username = ?
                """, (username,))
                
                result = cursor.fetchone()
                
                if not result:
                    return {
                        'has_time': False,
                        'expire_time': None,
                        'remaining_hours': 0,
                        'total_hours': 0,
                        'used_hours': 0,
                        'is_expired': True
                    }
                
                expire_time_str, total_hours, used_hours, last_login, status = result
                
                if not expire_time_str:
                    return {
                        'has_time': False,
                        'expire_time': None,
                        'remaining_hours': 0,
                        'total_hours': total_hours or 0,
                        'used_hours': used_hours or 0,
                        'is_expired': True
                    }
                
                try:
                    expire_time = datetime.fromisoformat(expire_time_str)
                    current_time = datetime.now()
                    
                    is_expired = expire_time <= current_time
                    
                    if is_expired:
                        remaining_seconds = 0
                        remaining_hours = 0
                    else:
                        remaining_seconds = int((expire_time - current_time).total_seconds())
                        remaining_hours = remaining_seconds / 3600
                    
                    return {
                        'has_time': not is_expired,
                        'expire_time': expire_time_str,
                        'remaining_seconds': remaining_seconds,
                        'remaining_hours': remaining_hours,
                        'total_hours': total_hours or 0,
                        'used_hours': used_hours or 0,
                        'is_expired': is_expired,
                        'last_login': last_login,
                        'status': status
                    }
                    
                except Exception as e:
                    print(f"解析时间失败: {e}")
                    return {
                        'has_time': False,
                        'expire_time': None,
                        'remaining_hours': 0,
                        'total_hours': total_hours or 0,
                        'used_hours': used_hours or 0,
                        'is_expired': True
                    }
                
        except Exception as e:
            print(f"获取用户时间信息失败: {e}")
            return {
                'has_time': False,
                'expire_time': None,
                'remaining_hours': 0,
                'total_hours': 0,
                'used_hours': 0,
                'is_expired': True
            }
    
    def get_card_list(self, status: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取卡密列表
        
        Args:
            status: 状态筛选
            limit: 返回数量限制
            
        Returns:
            List[Dict]: 卡密列表
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                if status:
                    cursor.execute("""
                        SELECT card_code, card_type, duration_hours, created_at, 
                               created_by, used_at, used_by, status, description
                        FROM recharge_cards 
                        WHERE status = ?
                        ORDER BY created_at DESC
                        LIMIT ?
                    """, (status, limit))
                else:
                    cursor.execute("""
                        SELECT card_code, card_type, duration_hours, created_at, 
                               created_by, used_at, used_by, status, description
                        FROM recharge_cards 
                        ORDER BY created_at DESC
                        LIMIT ?
                    """, (limit,))
                
                cards = []
                for row in cursor.fetchall():
                    cards.append({
                        'card_code': row[0],
                        'card_type': row[1],
                        'duration_hours': row[2],
                        'created_at': row[3],
                        'created_by': row[4],
                        'used_at': row[5],
                        'used_by': row[6],
                        'status': row[7],
                        'description': row[8]
                    })
                
                return cards
                
        except Exception as e:
            print(f"获取卡密列表失败: {e}")
            return []
    
    def get_recharge_history(self, username: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取充值历史
        
        Args:
            username: 用户名筛选
            limit: 返回数量限制
            
        Returns:
            List[Dict]: 充值历史
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                if username:
                    cursor.execute("""
                        SELECT username, card_code, card_type, hours_added, 
                               recharge_time, expire_time_after
                        FROM card_recharge_records 
                        WHERE username = ?
                        ORDER BY recharge_time DESC
                        LIMIT ?
                    """, (username, limit))
                else:
                    cursor.execute("""
                        SELECT username, card_code, card_type, hours_added, 
                               recharge_time, expire_time_after
                        FROM card_recharge_records 
                        ORDER BY recharge_time DESC
                        LIMIT ?
                    """, (limit,))
                
                records = []
                for row in cursor.fetchall():
                    records.append({
                        'username': row[0],
                        'card_code': row[1],
                        'card_type': row[2],
                        'hours_added': row[3],
                        'recharge_time': row[4],
                        'expire_time_after': row[5]
                    })
                
                return records
                
        except Exception as e:
            print(f"获取充值历史失败: {e}")
            return []
"""
系统常量定义
"""

# 默认配置值
DEFAULT_CONFIG = {
    'server': {
        'host': '0.0.0.0',
        'port': 8888,
        'max_connections': 50,
        'session_timeout': 3600
    },
    'database': {
        'path': 'data/auth.db',
        'backup_interval': 24
    },
    'security': {
        'max_failed_attempts': 5,
        'lockout_duration': 1800,
        'password_min_length': 8
    },
    'logging': {
        'level': 'INFO',
        'file_path': 'logs/server.log',
        'max_file_size': 10485760,  # 10MB
        'backup_count': 5
    }
}

# 消息类型
MESSAGE_TYPES = {
    'AUTH_REQUEST': 'auth_request',
    'AUTH_RESPONSE': 'auth_response',
    'HEARTBEAT': 'heartbeat',
    'ERROR': 'error'
}

# 认证状态
AUTH_STATUS = {
    'SUCCESS': 'success',
    'FAILED': 'failed',
    'LOCKED': 'locked',
    'EXPIRED': 'expired'
}

# 日志级别
LOG_LEVELS = {
    'DEBUG': 10,
    'INFO': 20,
    'WARNING': 30,
    'ERROR': 40,
    'CRITICAL': 50
}
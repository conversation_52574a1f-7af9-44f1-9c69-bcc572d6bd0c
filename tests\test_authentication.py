#!/usr/bin/env python3
"""
认证处理模块单元测试
"""

import sys
import os
import unittest
import tempfile
import shutil
import time
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.database_manager import DatabaseManager
from server.user_manager import UserManager
from server.authentication_handler import AuthenticationHandler
from server.security_utils import RateLimiter, SecurityValidator


class TestAuthenticationHandler(unittest.TestCase):
    """认证处理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.test_dir, 'test_auth.db')
        self.db_manager = DatabaseManager(self.db_path)
        self.auth_handler = AuthenticationHandler(self.db_manager)
        
        # 创建测试用户
        user_manager = UserManager(self.db_manager)
        user_manager.create_user('testuser', 'testpass123')
        user_manager.create_user('lockeduser', 'testpass123')
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'db_manager'):
            del self.db_manager
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_successful_authentication(self):
        """测试成功认证"""
        result = self.auth_handler.authenticate('testuser', 'testpass123', '127.0.0.1')
        
        self.assertTrue(result['success'])
        self.assertIn('session_token', result)
        self.assertIn('user_info', result)
        self.assertEqual(result['user_info']['username'], 'testuser')
    
    def test_failed_authentication_wrong_password(self):
        """测试错误密码认证失败"""
        result = self.auth_handler.authenticate('testuser', 'wrongpass', '127.0.0.1')
        
        self.assertFalse(result['success'])
        self.assertIn('密码错误', result['message'])
    
    def test_failed_authentication_nonexistent_user(self):
        """测试不存在用户认证失败"""
        result = self.auth_handler.authenticate('nonexistent', 'anypass', '127.0.0.1')
        
        self.assertFalse(result['success'])
        self.assertIn('用户不存在', result['message'])
    
    def test_account_lockout(self):
        """测试账户锁定机制"""
        # 多次失败尝试
        for i in range(6):  # 超过默认的5次限制
            result = self.auth_handler.authenticate('lockeduser', 'wrongpass', '127.0.0.1')
            self.assertFalse(result['success'])
        
        # 即使使用正确密码也应该被锁定
        result = self.auth_handler.authenticate('lockeduser', 'testpass123', '127.0.0.1')
        self.assertFalse(result['success'])
        self.assertIn('账户已锁定', result['message'])
    
    def test_validate_session(self):
        """测试会话验证"""
        # 先进行认证获取会话令牌
        auth_result = self.auth_handler.authenticate('testuser', 'testpass123', '127.0.0.1')
        token = auth_result['session_token']
        
        # 验证会话
        validate_result = self.auth_handler.validate_session(token)
        self.assertTrue(validate_result['success'])
        self.assertEqual(validate_result['user_info']['username'], 'testuser')
    
    def test_logout(self):
        """测试注销"""
        # 先进行认证
        auth_result = self.auth_handler.authenticate('testuser', 'testpass123', '127.0.0.1')
        token = auth_result['session_token']
        
        # 注销
        logout_result = self.auth_handler.logout(token)
        self.assertTrue(logout_result['success'])
        
        # 验证会话已失效
        validate_result = self.auth_handler.validate_session(token)
        self.assertFalse(validate_result['success'])
    
    def test_get_auth_statistics(self):
        """测试获取认证统计"""
        # 进行一些认证操作
        self.auth_handler.authenticate('testuser', 'testpass123', '127.0.0.1')
        self.auth_handler.authenticate('testuser', 'wrongpass', '127.0.0.1')
        
        # 获取统计信息
        stats = self.auth_handler.get_auth_statistics()
        
        self.assertIn('total_attempts', stats)
        self.assertIn('successful_attempts', stats)
        self.assertIn('failed_attempts', stats)
        self.assertIn('locked_accounts', stats)
        
        self.assertGreater(stats['total_attempts'], 0)


class TestRateLimiter(unittest.TestCase):
    """速率限制器测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建一个限制较小的速率限制器用于测试
        self.rate_limiter = RateLimiter(max_attempts=3, window_seconds=60)
    
    def test_allow_within_limit(self):
        """测试限制内的请求"""
        client_ip = '127.0.0.1'
        
        # 前3次请求应该被允许
        for i in range(3):
            result = self.rate_limiter.is_allowed(client_ip)
            self.assertTrue(result['allowed'])
            self.rate_limiter.record_attempt(client_ip)
    
    def test_block_over_limit(self):
        """测试超出限制的请求"""
        client_ip = '127.0.0.1'
        
        # 记录3次尝试
        for i in range(3):
            self.rate_limiter.record_attempt(client_ip)
        
        # 第4次应该被阻止
        result = self.rate_limiter.is_allowed(client_ip)
        self.assertFalse(result['allowed'])
        self.assertGreater(result['remaining_time'], 0)
    
    def test_different_ips(self):
        """测试不同IP的独立限制"""
        ip1 = '127.0.0.1'
        ip2 = '***********'
        
        # IP1达到限制
        for i in range(3):
            self.rate_limiter.record_attempt(ip1)
        
        # IP1被阻止
        result1 = self.rate_limiter.is_allowed(ip1)
        self.assertFalse(result1['allowed'])
        
        # IP2仍然被允许
        result2 = self.rate_limiter.is_allowed(ip2)
        self.assertTrue(result2['allowed'])
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        # 记录一些尝试
        self.rate_limiter.record_attempt('127.0.0.1')
        self.rate_limiter.record_attempt('***********')
        
        stats = self.rate_limiter.get_statistics()
        
        self.assertIn('total_attempts', stats)
        self.assertIn('blocked_attempts', stats)
        self.assertIn('unique_ips', stats)
        
        self.assertGreater(stats['total_attempts'], 0)
        self.assertGreater(stats['unique_ips'], 0)


class TestSecurityValidator(unittest.TestCase):
    """安全验证器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.validator = SecurityValidator()
    
    def test_validate_username(self):
        """测试用户名验证"""
        # 有效用户名
        valid_usernames = ['user123', 'test_user', 'admin', 'user-name']
        for username in valid_usernames:
            result = self.validator.validate_username(username)
            self.assertTrue(result['valid'], f"用户名 {username} 应该有效")
        
        # 无效用户名
        invalid_usernames = ['', 'a', 'user@name', 'user name', 'very_long_username_that_exceeds_limit']
        for username in invalid_usernames:
            result = self.validator.validate_username(username)
            self.assertFalse(result['valid'], f"用户名 {username} 应该无效")
    
    def test_validate_password(self):
        """测试密码验证"""
        # 有效密码
        valid_passwords = ['password123', 'mySecurePass', 'test_pass_123']
        for password in valid_passwords:
            result = self.validator.validate_password(password)
            self.assertTrue(result['valid'], f"密码 {password} 应该有效")
        
        # 无效密码
        invalid_passwords = ['', '123', 'short']
        for password in invalid_passwords:
            result = self.validator.validate_password(password)
            self.assertFalse(result['valid'], f"密码 {password} 应该无效")
    
    def test_validate_ip_address(self):
        """测试IP地址验证"""
        # 有效IP地址
        valid_ips = ['127.0.0.1', '***********', '********', '**********']
        for ip in valid_ips:
            result = self.validator.validate_ip_address(ip)
            self.assertTrue(result['valid'], f"IP地址 {ip} 应该有效")
        
        # 无效IP地址
        invalid_ips = ['', '256.1.1.1', '192.168.1', 'not.an.ip', '***********.1']
        for ip in invalid_ips:
            result = self.validator.validate_ip_address(ip)
            self.assertFalse(result['valid'], f"IP地址 {ip} 应该无效")
    
    def test_sanitize_input(self):
        """测试输入清理"""
        # 测试SQL注入防护
        malicious_input = "'; DROP TABLE users; --"
        sanitized = self.validator.sanitize_input(malicious_input)
        self.assertNotIn('DROP', sanitized.upper())
        self.assertNotIn(';', sanitized)
        
        # 测试XSS防护
        xss_input = "<script>alert('xss')</script>"
        sanitized = self.validator.sanitize_input(xss_input)
        self.assertNotIn('<script>', sanitized)
        self.assertNotIn('</script>', sanitized)
    
    def test_check_password_strength(self):
        """测试密码强度检查"""
        # 弱密码
        weak_passwords = ['123456', 'password', 'abc123']
        for password in weak_passwords:
            result = self.validator.check_password_strength(password)
            self.assertLess(result['strength_score'], 60)
        
        # 强密码
        strong_passwords = ['MyStr0ngP@ssw0rd!', 'C0mpl3x_P@ssw0rd', 'S3cur3_P@ss123!']
        for password in strong_passwords:
            result = self.validator.check_password_strength(password)
            self.assertGreater(result['strength_score'], 70)


class TestIntegrationAuthentication(unittest.TestCase):
    """认证集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.test_dir, 'test_auth.db')
        self.db_manager = DatabaseManager(self.db_path)
        self.auth_handler = AuthenticationHandler(self.db_manager)
        
        # 创建测试用户
        user_manager = UserManager(self.db_manager)
        user_manager.create_user('testuser', 'testpass123')
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'db_manager'):
            del self.db_manager
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_complete_auth_flow(self):
        """测试完整的认证流程"""
        client_ip = '127.0.0.1'
        
        # 1. 认证
        auth_result = self.auth_handler.authenticate('testuser', 'testpass123', client_ip)
        self.assertTrue(auth_result['success'])
        
        token = auth_result['session_token']
        user_info = auth_result['user_info']
        
        # 2. 验证会话
        validate_result = self.auth_handler.validate_session(token)
        self.assertTrue(validate_result['success'])
        self.assertEqual(validate_result['user_info']['username'], user_info['username'])
        
        # 3. 注销
        logout_result = self.auth_handler.logout(token)
        self.assertTrue(logout_result['success'])
        
        # 4. 验证会话已失效
        validate_after_logout = self.auth_handler.validate_session(token)
        self.assertFalse(validate_after_logout['success'])
    
    def test_concurrent_sessions(self):
        """测试并发会话"""
        client_ips = ['127.0.0.1', '***********', '********']
        tokens = []
        
        # 创建多个会话
        for ip in client_ips:
            result = self.auth_handler.authenticate('testuser', 'testpass123', ip)
            self.assertTrue(result['success'])
            tokens.append(result['session_token'])
        
        # 验证所有会话都有效
        for token in tokens:
            result = self.auth_handler.validate_session(token)
            self.assertTrue(result['success'])
        
        # 注销一个会话
        logout_result = self.auth_handler.logout(tokens[0])
        self.assertTrue(logout_result['success'])
        
        # 验证其他会话仍然有效
        for token in tokens[1:]:
            result = self.auth_handler.validate_session(token)
            self.assertTrue(result['success'])
    
    def test_security_measures(self):
        """测试安全措施"""
        client_ip = '127.0.0.1'
        
        # 测试输入验证
        invalid_inputs = [
            ("'; DROP TABLE users; --", "password"),
            ("user", "'; DROP TABLE sessions; --"),
            ("<script>alert('xss')</script>", "password"),
            ("user", "<script>alert('xss')</script>")
        ]
        
        for username, password in invalid_inputs:
            result = self.auth_handler.authenticate(username, password, client_ip)
            # 应该安全地处理恶意输入，不会导致系统崩溃
            self.assertFalse(result['success'])


if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestAuthenticationHandler,
        TestRateLimiter,
        TestSecurityValidator,
        TestIntegrationAuthentication
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print(f"\n认证模块测试完成:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    # 返回退出代码
    sys.exit(0 if result.wasSuccessful() else 1)
#!/usr/bin/env python3
"""
网络验证工具 - GUI启动器
提供图形界面来启动各个组件
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入配置管理模块
try:
    from common.enhanced_config_manager import EnhancedConfigManager
    from common.config_quick_fix import ConfigQuickFix, quick_fix_launcher_config
except ImportError as e:
    print(f"警告: 无法导入配置管理模块: {e}")
    EnhancedConfigManager = None
    ConfigQuickFix = None
    quick_fix_launcher_config = None


class NetworkAuthLauncher:
    """网络验证工具GUI启动器"""
    
    def __init__(self):
        """初始化启动器"""
        self.root = tk.Tk()
        self.root.title("网络验证工具启动器 v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置图标（如果存在）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 运行中的进程
        self.processes = {}
        
        # 初始化配置管理器
        self.config_manager = None
        self.config_quick_fix = None
        if EnhancedConfigManager and ConfigQuickFix:
            try:
                self.config_manager = EnhancedConfigManager()
                self.config_quick_fix = ConfigQuickFix()
            except Exception as e:
                print(f"初始化配置管理器失败: {e}")
        
        # 创建界面
        self.create_widgets()
        
        # 检查系统状态
        self.check_system_status()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="网络验证工具启动器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 系统状态框架
        status_frame = ttk.LabelFrame(main_frame, text="系统状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # 状态指示器
        ttk.Label(status_frame, text="Python环境:").grid(row=0, column=0, sticky=tk.W)
        self.python_status = ttk.Label(status_frame, text="检查中...", foreground="orange")
        self.python_status.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="配置文件:").grid(row=1, column=0, sticky=tk.W)
        self.config_status = ttk.Label(status_frame, text="检查中...", foreground="orange")
        self.config_status.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="数据库:").grid(row=2, column=0, sticky=tk.W)
        self.db_status = ttk.Label(status_frame, text="检查中...", foreground="orange")
        self.db_status.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # 控制按钮框架
        control_frame = ttk.LabelFrame(main_frame, text="组件控制", padding="10")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(1, weight=1)
        control_frame.columnconfigure(2, weight=1)
        
        # 服务器控制
        server_frame = ttk.Frame(control_frame)
        server_frame.grid(row=0, column=0, padx=5, pady=5, sticky=(tk.W, tk.E))
        
        ttk.Label(server_frame, text="服务器", font=("Arial", 10, "bold")).pack()
        self.server_btn = ttk.Button(server_frame, text="启动服务器", 
                                    command=self.toggle_server, width=15)
        self.server_btn.pack(pady=2)
        self.server_status_label = ttk.Label(server_frame, text="未运行", foreground="red")
        self.server_status_label.pack()
        
        # 客户端控制
        client_frame = ttk.Frame(control_frame)
        client_frame.grid(row=0, column=1, padx=5, pady=5, sticky=(tk.W, tk.E))
        
        ttk.Label(client_frame, text="客户端", font=("Arial", 10, "bold")).pack()
        self.client_btn = ttk.Button(client_frame, text="启动客户端", 
                                    command=self.start_client, width=15)
        self.client_btn.pack(pady=2)
        self.client_status_label = ttk.Label(client_frame, text="未运行", foreground="red")
        self.client_status_label.pack()
        
        # Web管理界面控制
        webgui_frame = ttk.Frame(control_frame)
        webgui_frame.grid(row=0, column=2, padx=5, pady=5, sticky=(tk.W, tk.E))
        
        ttk.Label(webgui_frame, text="Web管理", font=("Arial", 10, "bold")).pack()
        self.webgui_btn = ttk.Button(webgui_frame, text="启动Web管理", 
                                    command=self.start_webgui, width=15)
        self.webgui_btn.pack(pady=2)
        self.webgui_status_label = ttk.Label(webgui_frame, text="未运行", foreground="red")
        self.webgui_status_label.pack()
        
        # 工具按钮框架
        tools_frame = ttk.LabelFrame(main_frame, text="系统工具", padding="10")
        tools_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 工具按钮
        ttk.Button(tools_frame, text="运行测试", command=self.run_tests).grid(row=0, column=0, padx=5)
        ttk.Button(tools_frame, text="查看状态", command=self.show_status).grid(row=0, column=1, padx=5)
        ttk.Button(tools_frame, text="安装/初始化", command=self.run_install).grid(row=0, column=2, padx=5)
        ttk.Button(tools_frame, text="修复配置", command=self.auto_fix_configs).grid(row=0, column=3, padx=5)
        ttk.Button(tools_frame, text="配置管理", command=self.open_config_manager).grid(row=0, column=4, padx=5)
        ttk.Button(tools_frame, text="打开配置目录", command=self.open_config_dir).grid(row=0, column=5, padx=5)
        ttk.Button(tools_frame, text="打开日志目录", command=self.open_logs_dir).grid(row=0, column=6, padx=5)
        
        # 输出日志框架
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        ttk.Button(log_frame, text="清空日志", command=self.clear_log).grid(row=1, column=0, pady=(5, 0))
        
        # 底部状态栏
        self.status_bar = ttk.Label(main_frame, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.config(text=message)
    
    def check_system_status(self):
        """检查系统状态"""
        def check():
            # 检查Python环境
            try:
                python_version = f"Python {sys.version.split()[0]}"
                self.python_status.config(text=python_version, foreground="green")
            except:
                self.python_status.config(text="异常", foreground="red")
            
            # 使用增强的配置检查
            self._check_enhanced_config_status()
            
            # 检查数据库
            if os.path.exists("data/auth.db"):
                self.db_status.config(text="正常", foreground="green")
            else:
                self.db_status.config(text="未初始化", foreground="orange")
        
        # 在后台线程中检查
        threading.Thread(target=check, daemon=True).start()
    
    def _check_enhanced_config_status(self):
        """使用增强配置管理器检查配置状态"""
        try:
            if self.config_manager:
                # 获取配置状态摘要
                summary = self.config_manager.get_summary_status()
                
                if summary['all_configs_ok']:
                    self.config_status.config(text="正常", foreground="green")
                elif summary['missing_configs']:
                    missing_text = ', '.join(summary['missing_configs'])
                    self.config_status.config(text=f"缺失: {missing_text}", foreground="red")
                elif summary['invalid_configs']:
                    invalid_text = ', '.join(summary['invalid_configs'])
                    self.config_status.config(text=f"无效: {invalid_text}", foreground="orange")
                else:
                    self.config_status.config(text="部分问题", foreground="orange")
            else:
                # 回退到基本检查
                config_files = ["config.ini", "client_config.ini"]
                missing_configs = [f for f in config_files if not os.path.exists(f)]
                
                if not missing_configs:
                    self.config_status.config(text="正常", foreground="green")
                else:
                    self.config_status.config(text=f"缺失: {', '.join(missing_configs)}", foreground="red")
        
        except Exception as e:
            self.config_status.config(text=f"检查失败: {str(e)}", foreground="red")
    
    def run_command(self, command, component_name):
        """在后台运行命令"""
        def run():
            try:
                self.log_message(f"启动 {component_name}...")
                self.update_status(f"正在启动 {component_name}...")
                
                # 启动进程
                process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )
                
                self.processes[component_name] = process
                self.log_message(f"{component_name} 已启动 (PID: {process.pid})")
                
                # 读取输出
                for line in iter(process.stdout.readline, ''):
                    if line.strip():
                        self.log_message(f"[{component_name}] {line.strip()}")
                
                # 进程结束
                process.wait()
                if component_name in self.processes:
                    del self.processes[component_name]
                
                self.log_message(f"{component_name} 已停止")
                self.update_status("就绪")
                
            except Exception as e:
                self.log_message(f"启动 {component_name} 失败: {str(e)}")
                messagebox.showerror("错误", f"启动 {component_name} 失败:\n{str(e)}")
        
        threading.Thread(target=run, daemon=True).start()
    
    def toggle_server(self):
        """切换服务器状态"""
        if "服务器" in self.processes:
            # 停止服务器
            try:
                process = self.processes["服务器"]
                process.terminate()
                self.log_message("正在停止服务器...")
                self.server_btn.config(text="启动服务器")
                self.server_status_label.config(text="未运行", foreground="red")
            except:
                pass
        else:
            # 启动服务器
            command = [sys.executable, "network_auth_tool.py", "server"]
            self.run_command(command, "服务器")
            self.server_btn.config(text="停止服务器")
            self.server_status_label.config(text="运行中", foreground="green")
    
    def start_client(self):
        """启动客户端"""
        command = [sys.executable, "network_auth_tool.py", "client"]
        self.run_command(command, "客户端")
        self.client_status_label.config(text="运行中", foreground="green")
    
    def start_webgui(self):
        """启动Web管理界面"""
        command = [sys.executable, "network_auth_tool.py", "webgui"]
        self.run_command(command, "Web管理")
        self.webgui_status_label.config(text="运行中", foreground="green")
    
    def run_tests(self):
        """运行测试"""
        command = [sys.executable, "network_auth_tool.py", "test"]
        self.run_command(command, "测试")
    
    def show_status(self):
        """显示系统状态"""
        command = [sys.executable, "network_auth_tool.py", "status"]
        self.run_command(command, "状态检查")
    
    def run_install(self):
        """运行安装脚本"""
        if messagebox.askyesno("确认", "是否运行安装/初始化脚本？\n这将创建必要的目录和配置文件。"):
            command = [sys.executable, "install.py"]
            self.run_command(command, "安装")
    
    def open_config_dir(self):
        """打开配置目录"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile('.')
            else:  # Linux/Mac
                subprocess.run(['xdg-open', '.'])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开目录: {str(e)}")
    
    def open_logs_dir(self):
        """打开日志目录"""
        try:
            logs_dir = "logs"
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
            
            if os.name == 'nt':  # Windows
                os.startfile(logs_dir)
            else:  # Linux/Mac
                subprocess.run(['xdg-open', logs_dir])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开日志目录: {str(e)}")
    
    def auto_fix_configs(self):
        """自动修复配置文件"""
        try:
            self.log_message("开始自动修复配置文件...")
            self.update_status("正在修复配置文件...")
            
            if quick_fix_launcher_config:
                # 使用快速修复功能
                success, messages = quick_fix_launcher_config()
                
                for message in messages:
                    self.log_message(message)
                
                if success:
                    self.log_message("配置文件修复完成！")
                    messagebox.showinfo("修复完成", "配置文件修复成功！\n请查看日志了解详细信息。")
                    # 重新检查系统状态
                    self.check_system_status()
                else:
                    self.log_message("配置文件修复失败！")
                    messagebox.showerror("修复失败", "配置文件修复失败！\n请查看日志了解详细信息。")
            
            elif self.config_manager:
                # 使用配置管理器
                result = self.config_manager.auto_fix_all()
                
                for action in result.actions_taken:
                    self.log_message(f"修复操作: {action}")
                
                for error in result.errors:
                    self.log_message(f"修复错误: {error}")
                
                if result.success:
                    self.log_message("配置文件修复完成！")
                    messagebox.showinfo("修复完成", "配置文件修复成功！")
                    self.check_system_status()
                else:
                    self.log_message("配置文件修复失败！")
                    messagebox.showerror("修复失败", "配置文件修复失败！")
            
            else:
                # 基本修复：直接复制示例文件
                self._basic_config_fix()
            
            self.update_status("就绪")
            
        except Exception as e:
            error_msg = f"配置修复过程中出错: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
            self.update_status("就绪")
    
    def _basic_config_fix(self):
        """基本配置修复"""
        try:
            config_files = ["config.ini", "client_config.ini"]
            fixed_files = []
            
            for config_file in config_files:
                if not os.path.exists(config_file):
                    example_file = f"{config_file}.example"
                    if os.path.exists(example_file):
                        shutil.copy2(example_file, config_file)
                        fixed_files.append(config_file)
                        self.log_message(f"从示例文件创建: {config_file}")
            
            if fixed_files:
                self.log_message("基本配置修复完成！")
                messagebox.showinfo("修复完成", f"已创建配置文件: {', '.join(fixed_files)}")
                self.check_system_status()
            else:
                self.log_message("没有需要修复的配置文件")
                messagebox.showinfo("修复完成", "所有配置文件都已存在")
        
        except Exception as e:
            error_msg = f"基本配置修复失败: {str(e)}"
            self.log_message(error_msg)
            raise Exception(error_msg)
    
    def open_config_manager(self):
        """打开配置管理界面"""
        try:
            if self.config_manager:
                self._show_config_manager_dialog()
            else:
                messagebox.showinfo("提示", "配置管理功能不可用\n请使用'修复配置'按钮进行基本修复")
        
        except Exception as e:
            messagebox.showerror("错误", f"打开配置管理器失败: {str(e)}")
    
    def _show_config_manager_dialog(self):
        """显示配置管理对话框"""
        # 创建配置管理窗口
        config_window = tk.Toplevel(self.root)
        config_window.title("配置文件管理")
        config_window.geometry("600x500")
        config_window.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(config_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置状态显示
        status_frame = ttk.LabelFrame(main_frame, text="配置文件状态", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 获取配置状态
        config_status = self.config_manager.get_config_status()
        
        row = 0
        for config_file, status in config_status.items():
            # 文件名
            ttk.Label(status_frame, text=config_file, font=("Arial", 10, "bold")).grid(
                row=row, column=0, sticky=tk.W, padx=(0, 10))
            
            # 状态
            if status.exists:
                if status.is_valid:
                    status_text = "正常"
                    status_color = "green"
                else:
                    status_text = "有错误"
                    status_color = "orange"
            else:
                status_text = "不存在"
                status_color = "red"
            
            status_label = ttk.Label(status_frame, text=status_text, foreground=status_color)
            status_label.grid(row=row, column=1, sticky=tk.W, padx=(0, 10))
            
            # 详细信息
            if status.errors:
                error_text = "; ".join(status.errors[:2])  # 只显示前两个错误
                if len(status.errors) > 2:
                    error_text += f" (还有{len(status.errors)-2}个错误)"
                ttk.Label(status_frame, text=error_text, foreground="red", 
                         font=("Arial", 8)).grid(row=row, column=2, sticky=tk.W)
            
            row += 1
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(button_frame, text="修复所有配置", 
                  command=lambda: self._fix_and_refresh(config_window)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="刷新状态", 
                  command=lambda: self._refresh_config_dialog(config_window)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="关闭", 
                  command=config_window.destroy).pack(side=tk.RIGHT)
        
        # 详细日志显示
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        log_text = scrolledtext.ScrolledText(log_frame, height=15, width=70)
        log_text.pack(fill=tk.BOTH, expand=True)
        
        # 显示修复日志
        if hasattr(self.config_manager.auto_fixer, 'get_repair_log'):
            repair_log = self.config_manager.auto_fixer.get_repair_log()
            for log_entry in repair_log[-20:]:  # 显示最近20条日志
                log_text.insert(tk.END, log_entry + "\n")
        
        # 存储引用以便刷新
        config_window.log_text = log_text
    
    def _fix_and_refresh(self, config_window):
        """修复配置并刷新对话框"""
        try:
            result = self.config_manager.auto_fix_all()
            
            # 更新日志显示
            if hasattr(config_window, 'log_text'):
                config_window.log_text.delete(1.0, tk.END)
                repair_log = self.config_manager.auto_fixer.get_repair_log()
                for log_entry in repair_log[-20:]:
                    config_window.log_text.insert(tk.END, log_entry + "\n")
                config_window.log_text.see(tk.END)
            
            # 刷新主窗口状态
            self.check_system_status()
            
            # 显示结果
            if result.success:
                messagebox.showinfo("修复完成", "配置文件修复成功！", parent=config_window)
            else:
                messagebox.showerror("修复失败", f"修复失败:\n{'; '.join(result.errors)}", 
                                   parent=config_window)
        
        except Exception as e:
            messagebox.showerror("错误", f"修复过程中出错: {str(e)}", parent=config_window)
    
    def _refresh_config_dialog(self, config_window):
        """刷新配置对话框"""
        config_window.destroy()
        self._show_config_manager_dialog()
    
    def on_closing(self):
        """窗口关闭事件"""
        if self.processes:
            if messagebox.askyesno("确认", "还有进程在运行，是否强制关闭？"):
                # 创建进程列表的副本以避免运行时修改字典
                processes_copy = list(self.processes.items())
                for name, process in processes_copy:
                    try:
                        process.terminate()
                        self.log_message(f"强制停止 {name}")
                    except:
                        pass
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """运行启动器"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 显示欢迎信息
        self.log_message("欢迎使用网络验证工具启动器！")
        self.log_message("请选择要启动的组件，或使用系统工具进行管理。")
        
        # 启动主循环
        self.root.mainloop()


def main():
    """主函数"""
    try:
        # 检查是否在正确的目录
        if not os.path.exists("network_auth_tool.py"):
            messagebox.showerror("错误", 
                               "未找到 network_auth_tool.py 文件！\n"
                               "请确保启动器在项目根目录中运行。")
            return 1
        
        # 创建并运行启动器
        launcher = NetworkAuthLauncher()
        launcher.run()
        
        return 0
        
    except Exception as e:
        messagebox.showerror("严重错误", f"启动器初始化失败:\n{str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
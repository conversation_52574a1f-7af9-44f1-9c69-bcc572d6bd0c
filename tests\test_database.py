#!/usr/bin/env python3
"""
数据库管理模块单元测试
"""

import sys
import os
import unittest
import tempfile
import shutil
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.database_manager import DatabaseManager
from server.user_manager import UserManager
from server.session_manager import SessionManager


class TestDatabaseManager(unittest.TestCase):
    """数据库管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.test_dir, 'test_auth.db')
        self.db_manager = DatabaseManager(self.db_path)
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'db_manager'):
            del self.db_manager
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_database_creation(self):
        """测试数据库创建"""
        self.assertTrue(os.path.exists(self.db_path))
    
    def test_get_connection(self):
        """测试获取数据库连接"""
        with self.db_manager.get_connection() as conn:
            self.assertIsNotNone(conn)
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row['name'] for row in cursor.fetchall()]
            
            # 检查必要的表是否存在
            expected_tables = ['users', 'sessions', 'logs']
            for table in expected_tables:
                self.assertIn(table, tables)
    
    def test_database_info(self):
        """测试获取数据库信息"""
        info = self.db_manager.get_database_info()
        
        self.assertIn('database_path', info)
        self.assertIn('database_size', info)
        self.assertIn('tables', info)
        self.assertIn('version', info)
        
        self.assertEqual(info['database_path'], self.db_path)
        self.assertGreater(info['database_size'], 0)


class TestUserManager(unittest.TestCase):
    """用户管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.test_dir, 'test_auth.db')
        self.db_manager = DatabaseManager(self.db_path)
        self.user_manager = UserManager(self.db_manager)
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'db_manager'):
            del self.db_manager
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_create_user(self):
        """测试创建用户"""
        result = self.user_manager.create_user('testuser', 'testpass123')
        
        self.assertTrue(result['success'])
        self.assertIn('user_id', result)
        self.assertIsInstance(result['user_id'], int)
    
    def test_create_duplicate_user(self):
        """测试创建重复用户"""
        # 创建第一个用户
        result1 = self.user_manager.create_user('testuser', 'testpass123')
        self.assertTrue(result1['success'])
        
        # 尝试创建同名用户
        result2 = self.user_manager.create_user('testuser', 'testpass456')
        self.assertFalse(result2['success'])
        self.assertIn('已存在', result2['message'])
    
    def test_verify_user(self):
        """测试用户验证"""
        # 创建用户
        self.user_manager.create_user('testuser', 'testpass123')
        
        # 正确密码验证
        result1 = self.user_manager.verify_user('testuser', 'testpass123')
        self.assertTrue(result1['success'])
        self.assertIn('user_info', result1)
        
        # 错误密码验证
        result2 = self.user_manager.verify_user('testuser', 'wrongpass')
        self.assertFalse(result2['success'])
        
        # 不存在的用户
        result3 = self.user_manager.verify_user('nonexistent', 'anypass')
        self.assertFalse(result3['success'])
    
    def test_get_user_info(self):
        """测试获取用户信息"""
        # 创建用户
        create_result = self.user_manager.create_user('testuser', 'testpass123')
        user_id = create_result['user_id']
        
        # 获取用户信息
        user_info = self.user_manager.get_user_info('testuser')
        
        self.assertIsNotNone(user_info)
        self.assertEqual(user_info['id'], user_id)
        self.assertEqual(user_info['username'], 'testuser')
        self.assertIn('created_at', user_info)
    
    def test_change_password(self):
        """测试修改密码"""
        # 创建用户
        self.user_manager.create_user('testuser', 'oldpass123')
        
        # 修改密码
        result = self.user_manager.change_password('testuser', 'newpass456')
        self.assertTrue(result['success'])
        
        # 验证新密码
        verify_result = self.user_manager.verify_user('testuser', 'newpass456')
        self.assertTrue(verify_result['success'])
        
        # 验证旧密码应该失败
        old_verify_result = self.user_manager.verify_user('testuser', 'oldpass123')
        self.assertFalse(old_verify_result['success'])
    
    def test_delete_user(self):
        """测试删除用户"""
        # 创建用户
        self.user_manager.create_user('testuser', 'testpass123')
        
        # 确认用户存在
        user_info = self.user_manager.get_user_info('testuser')
        self.assertIsNotNone(user_info)
        
        # 删除用户
        result = self.user_manager.delete_user('testuser')
        self.assertTrue(result['success'])
        
        # 确认用户已删除
        deleted_user_info = self.user_manager.get_user_info('testuser')
        self.assertIsNone(deleted_user_info)
    
    def test_list_users(self):
        """测试列出用户"""
        # 创建多个用户
        users = ['user1', 'user2', 'user3']
        for username in users:
            self.user_manager.create_user(username, 'password123')
        
        # 获取用户列表
        user_list = self.user_manager.list_users()
        
        self.assertIsInstance(user_list, list)
        self.assertEqual(len(user_list), len(users))
        
        # 检查用户名
        usernames = [user['username'] for user in user_list]
        for username in users:
            self.assertIn(username, usernames)
    
    def test_password_validation(self):
        """测试密码验证"""
        # 测试空密码
        result1 = self.user_manager.create_user('testuser1', '')
        self.assertFalse(result1['success'])
        
        # 测试过短密码
        result2 = self.user_manager.create_user('testuser2', '123')
        self.assertFalse(result2['success'])
        
        # 测试合法密码
        result3 = self.user_manager.create_user('testuser3', 'validpass123')
        self.assertTrue(result3['success'])


class TestSessionManager(unittest.TestCase):
    """会话管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.test_dir, 'test_auth.db')
        self.db_manager = DatabaseManager(self.db_path)
        self.user_manager = UserManager(self.db_manager)
        self.session_manager = SessionManager(self.db_manager)
        
        # 创建测试用户
        self.user_manager.create_user('testuser', 'testpass123')
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'db_manager'):
            del self.db_manager
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_create_session(self):
        """测试创建会话"""
        result = self.session_manager.create_session('testuser', '127.0.0.1')
        
        self.assertTrue(result['success'])
        self.assertIn('session_token', result)
        self.assertIn('expires_at', result)
        
        # 检查令牌长度
        token = result['session_token']
        self.assertGreater(len(token), 20)
    
    def test_validate_session(self):
        """测试验证会话"""
        # 创建会话
        create_result = self.session_manager.create_session('testuser', '127.0.0.1')
        token = create_result['session_token']
        
        # 验证会话
        validate_result = self.session_manager.validate_session(token)
        self.assertTrue(validate_result['success'])
        self.assertIn('user_info', validate_result)
        self.assertEqual(validate_result['user_info']['username'], 'testuser')
    
    def test_invalid_session(self):
        """测试无效会话"""
        # 验证不存在的令牌
        result = self.session_manager.validate_session('invalid_token_123')
        self.assertFalse(result['success'])
    
    def test_destroy_session(self):
        """测试销毁会话"""
        # 创建会话
        create_result = self.session_manager.create_session('testuser', '127.0.0.1')
        token = create_result['session_token']
        
        # 验证会话存在
        validate_result1 = self.session_manager.validate_session(token)
        self.assertTrue(validate_result1['success'])
        
        # 销毁会话
        destroy_result = self.session_manager.destroy_session(token)
        self.assertTrue(destroy_result['success'])
        
        # 验证会话已销毁
        validate_result2 = self.session_manager.validate_session(token)
        self.assertFalse(validate_result2['success'])
    
    def test_cleanup_expired_sessions(self):
        """测试清理过期会话"""
        # 创建会话
        create_result = self.session_manager.create_session('testuser', '127.0.0.1')
        token = create_result['session_token']
        
        # 手动设置会话为过期
        with self.db_manager.get_connection() as conn:
            expired_time = (datetime.now() - timedelta(hours=1)).isoformat()
            conn.execute(
                "UPDATE sessions SET expires_at = ? WHERE session_token = ?",
                (expired_time, token)
            )
            conn.commit()
        
        # 清理过期会话
        cleanup_result = self.session_manager.cleanup_expired_sessions()
        self.assertTrue(cleanup_result['success'])
        self.assertGreater(cleanup_result['cleaned_count'], 0)
        
        # 验证过期会话已被清理
        validate_result = self.session_manager.validate_session(token)
        self.assertFalse(validate_result['success'])
    
    def test_get_session_statistics(self):
        """测试获取会话统计"""
        # 创建多个会话
        for i in range(3):
            self.session_manager.create_session('testuser', f'127.0.0.{i+1}')
        
        # 获取统计信息
        stats = self.session_manager.get_session_statistics()
        
        self.assertIn('total_sessions', stats)
        self.assertIn('active_sessions', stats)
        self.assertIn('expired_sessions', stats)
        
        self.assertGreaterEqual(stats['active_sessions'], 3)


if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestDatabaseManager,
        TestUserManager,
        TestSessionManager
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print(f"\n测试完成:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    # 返回退出代码
    sys.exit(0 if result.wasSuccessful() else 1)
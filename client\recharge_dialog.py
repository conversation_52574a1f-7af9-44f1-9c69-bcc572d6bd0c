#!/usr/bin/env python3
"""
充值管理对话框
提供用户充值功能的图形界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
from decimal import Decimal, InvalidOperation
from typing import Dict, Any, Optional, Callable


class RechargeDialog:
    """充值管理对话框"""
    
    def __init__(self, parent, username: str, current_balance: float = 0.0, 
                 recharge_callback: Optional[Callable] = None):
        """
        初始化充值对话框
        
        Args:
            parent: 父窗口
            username: 用户名
            current_balance: 当前余额
            recharge_callback: 充值回调函数
        """
        self.parent = parent
        self.username = username
        self.current_balance = current_balance
        self.recharge_callback = recharge_callback
        self.result = None
        self.dialog = None
        
        # 预设充值金额选项
        self.preset_amounts = [10, 20, 50, 100, 200, 500]
        
    def show(self) -> Optional[Dict[str, Any]]:
        """
        显示充值对话框
        
        Returns:
            Dict: 充值结果，取消返回None
        """
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("账户充值")
        self.dialog.geometry("450x600")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        self.create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
        return self.result
    
    def create_widgets(self):
        """创建对话框控件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="账户充值", font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 用户信息框架
        info_frame = ttk.LabelFrame(main_frame, text="账户信息", padding="15")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 用户名
        ttk.Label(info_frame, text="用户名:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Label(info_frame, text=self.username, font=('Arial', 10)).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 当前余额
        ttk.Label(info_frame, text="当前余额:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=5)
        self.balance_label = ttk.Label(info_frame, text=f"¥{self.current_balance:.2f}", 
                                      font=('Arial', 12, 'bold'), foreground="green")
        self.balance_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 充值金额选择框架
        amount_frame = ttk.LabelFrame(main_frame, text="选择充值金额", padding="15")
        amount_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 预设金额按钮
        ttk.Label(amount_frame, text="快速选择:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, pady=(0, 10))
        
        preset_frame = ttk.Frame(amount_frame)
        preset_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.selected_amount = tk.StringVar()
        
        for i, amount in enumerate(self.preset_amounts):
            btn = ttk.Button(preset_frame, text=f"¥{amount}", width=8,
                           command=lambda a=amount: self.select_preset_amount(a))
            btn.grid(row=i//3, column=i%3, padx=5, pady=5, sticky=tk.W)
        
        # 自定义金额输入
        ttk.Label(amount_frame, text="或输入自定义金额:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, pady=(10, 5))
        
        custom_frame = ttk.Frame(amount_frame)
        custom_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(custom_frame, text="¥", font=('Arial', 12)).pack(side=tk.LEFT)
        self.amount_entry = ttk.Entry(custom_frame, textvariable=self.selected_amount, 
                                     font=('Arial', 12), width=15)
        self.amount_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        # 金额验证提示
        self.amount_hint = ttk.Label(amount_frame, text="", foreground="gray", font=('Arial', 9))
        self.amount_hint.pack(anchor=tk.W, pady=(5, 0))
        
        # 充值说明
        note_frame = ttk.LabelFrame(main_frame, text="充值说明", padding="15")
        note_frame.pack(fill=tk.X, pady=(0, 20))
        
        notes = [
            "• 充值金额范围：¥0.01 - ¥10,000",
            "• 充值后余额立即生效",
            "• 支持最多2位小数",
            "• 充值记录可在个人中心查看"
        ]
        
        for note in notes:
            ttk.Label(note_frame, text=note, font=('Arial', 9)).pack(anchor=tk.W, pady=2)
        
        # 充值历史按钮
        history_frame = ttk.Frame(main_frame)
        history_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Button(history_frame, text="查看充值历史", 
                  command=self.show_recharge_history).pack(side=tk.LEFT)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 充值按钮
        self.recharge_btn = ttk.Button(button_frame, text="确认充值", 
                                      command=self.confirm_recharge, style="Accent.TButton")
        self.recharge_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 取消按钮
        cancel_btn = ttk.Button(button_frame, text="取消", command=self.cancel)
        cancel_btn.pack(side=tk.RIGHT)
        
        # 绑定事件
        self.selected_amount.trace('w', self.validate_amount)
        self.amount_entry.bind('<Return>', lambda e: self.confirm_recharge())
        
        # 设置焦点
        self.amount_entry.focus()
    
    def select_preset_amount(self, amount: int):
        """选择预设金额"""
        self.selected_amount.set(str(amount))
        self.amount_entry.focus()
    
    def validate_amount(self, *args):
        """验证充值金额"""
        amount_str = self.selected_amount.get().strip()
        
        if not amount_str:
            self.amount_hint.config(text="请输入充值金额", foreground="gray")
            self.recharge_btn.config(state=tk.DISABLED)
            return
        
        try:
            amount = Decimal(amount_str)
            
            if amount <= 0:
                self.amount_hint.config(text="充值金额必须大于0", foreground="red")
                self.recharge_btn.config(state=tk.DISABLED)
                return
            
            if amount > Decimal('10000'):
                self.amount_hint.config(text="单次充值金额不能超过¥10,000", foreground="red")
                self.recharge_btn.config(state=tk.DISABLED)
                return
            
            # 检查小数位数
            if amount.as_tuple().exponent < -2:
                self.amount_hint.config(text="充值金额最多支持2位小数", foreground="red")
                self.recharge_btn.config(state=tk.DISABLED)
                return
            
            # 显示充值后余额
            new_balance = self.current_balance + float(amount)
            self.amount_hint.config(text=f"充值后余额: ¥{new_balance:.2f}", foreground="green")
            self.recharge_btn.config(state=tk.NORMAL)
            
        except (InvalidOperation, ValueError):
            self.amount_hint.config(text="请输入有效的数字", foreground="red")
            self.recharge_btn.config(state=tk.DISABLED)
    
    def confirm_recharge(self):
        """确认充值"""
        amount_str = self.selected_amount.get().strip()
        
        if not amount_str:
            messagebox.showerror("错误", "请输入充值金额")
            return
        
        try:
            amount = Decimal(amount_str)
            
            # 再次验证金额
            if amount <= 0 or amount > Decimal('10000'):
                messagebox.showerror("错误", "充值金额无效")
                return
            
            # 显示确认对话框
            confirm_msg = f"确认充值 ¥{amount:.2f} 到账户 {self.username}？\\n\\n"
            confirm_msg += f"当前余额: ¥{self.current_balance:.2f}\\n"
            confirm_msg += f"充值金额: ¥{amount:.2f}\\n"
            confirm_msg += f"充值后余额: ¥{self.current_balance + float(amount):.2f}"
            
            if messagebox.askyesno("确认充值", confirm_msg):
                # 执行充值
                self.process_recharge(amount)
            
        except (InvalidOperation, ValueError):
            messagebox.showerror("错误", "充值金额格式无效")
    
    def process_recharge(self, amount: Decimal):
        """处理充值请求"""
        try:
            # 禁用按钮，防止重复提交
            self.recharge_btn.config(state=tk.DISABLED, text="充值中...")
            self.dialog.update()
            
            if self.recharge_callback:
                # 调用充值回调函数
                result = self.recharge_callback(self.username, amount)
                
                if result and result.get('success'):
                    # 充值成功
                    messagebox.showinfo(
                        "充值成功", 
                        f"充值成功！\\n\\n"
                        f"充值金额: ¥{amount:.2f}\\n"
                        f"新余额: ¥{result.get('new_balance', 0):.2f}"
                    )
                    
                    self.result = result
                    self.dialog.destroy()
                else:
                    # 充值失败
                    error_msg = result.get('message', '充值失败') if result else '充值请求失败'
                    messagebox.showerror("充值失败", error_msg)
                    
                    # 恢复按钮状态
                    self.recharge_btn.config(state=tk.NORMAL, text="确认充值")
            else:
                # 没有充值回调函数，返回充值数据
                self.result = {
                    'success': True,
                    'amount': float(amount),
                    'username': self.username
                }
                self.dialog.destroy()
                
        except Exception as e:
            messagebox.showerror("充值失败", f"充值过程中发生错误：{str(e)}")
            self.recharge_btn.config(state=tk.NORMAL, text="确认充值")
    
    def show_recharge_history(self):
        """显示充值历史"""
        # 创建充值历史窗口
        history_window = tk.Toplevel(self.dialog)
        history_window.title(f"{self.username} - 充值历史")
        history_window.geometry("600x400")
        history_window.transient(self.dialog)
        
        # 创建表格
        frame = ttk.Frame(history_window, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 表格标题
        ttk.Label(frame, text="充值历史记录", font=('Arial', 12, 'bold')).pack(pady=(0, 10))
        
        # 创建Treeview
        columns = ('时间', '金额', '类型', '状态', '说明')
        tree = ttk.Treeview(frame, columns=columns, show='headings', height=15)
        
        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 模拟充值历史数据（实际应用中应该从数据库获取）
        sample_data = [
            ('2025-08-31 10:00:00', '¥100.00', '手动充值', '已完成', '测试充值'),
            ('2025-08-30 15:30:00', '¥50.00', '管理员充值', '已完成', '系统赠送'),
            ('2025-08-29 09:15:00', '¥200.00', '手动充值', '已完成', '账户充值'),
        ]
        
        for item in sample_data:
            tree.insert('', tk.END, values=item)
        
        # 关闭按钮
        ttk.Button(frame, text="关闭", 
                  command=history_window.destroy).pack(pady=(10, 0))
    
    def cancel(self):
        """取消充值"""
        self.result = None
        self.dialog.destroy()


def test_recharge_dialog():
    """测试充值对话框"""
    def mock_recharge_callback(username, amount):
        """模拟充值回调"""
        print(f"模拟充值: 用户={username}, 金额={amount}")
        return {
            'success': True,
            'message': '充值成功',
            'new_balance': 150.75,
            'recharge_amount': float(amount)
        }
    
    root = tk.Tk()
    root.title("充值对话框测试")
    root.geometry("300x200")
    
    def show_dialog():
        dialog = RechargeDialog(
            parent=root,
            username="testuser",
            current_balance=50.25,
            recharge_callback=mock_recharge_callback
        )
        result = dialog.show()
        if result:
            print(f"充值结果: {result}")
        else:
            print("用户取消了充值")
    
    ttk.Button(root, text="打开充值对话框", command=show_dialog).pack(pady=50)
    
    root.mainloop()


if __name__ == "__main__":
    test_recharge_dialog()